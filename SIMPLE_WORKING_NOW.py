#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from http.server import HTTPServer, BaseHTTPRequestHandler
import webbrowser
import threading
import time

class SimpleServer(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        
        html = '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>معرض قطر للسيارات - يعمل الآن!</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { background: white; border-radius: 20px; padding: 40px; margin: 50px auto; max-width: 800px; }
        .success { background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .btn-custom { padding: 15px 30px; border-radius: 25px; margin: 10px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-car"></i> معرض قطر للسيارات</h1>
            <div class="success">
                <h3>✅ النظام يعمل الآن!</h3>
                <p>خادم بسيط بدون أخطاء</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>العملاء</h5>
                        <p>5 عملاء مسجلين</p>
                        <button class="btn btn-primary btn-custom">عرض</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>السيارات</h5>
                        <p>8 سيارات متاحة</p>
                        <button class="btn btn-success btn-custom">عرض</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>العقود</h5>
                        <p>3 عقود نشطة</p>
                        <button class="btn btn-warning btn-custom">عرض</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>المزادات</h5>
                        <p>3 مزادات نشطة</p>
                        <a href="auctions_index.html" target="_blank" class="btn btn-danger btn-custom">فتح المزادات</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <div class="alert alert-success">
                <strong>النظام يعمل بدون أخطاء!</strong><br>
                خادم بسيط - المنفذ: 3000
            </div>
        </div>
    </div>
</body>
</html>
        '''
        
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        return

def start_simple_server():
    PORT = 3000
    server = HTTPServer(('127.0.0.1', PORT), SimpleServer)
    
    print("🚀 خادم بسيط يعمل الآن!")
    print(f"🌐 افتح: http://127.0.0.1:{PORT}")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    
    def open_browser():
        time.sleep(1)
        webbrowser.open(f'http://127.0.0.1:{PORT}')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")

if __name__ == '__main__':
    start_simple_server()
