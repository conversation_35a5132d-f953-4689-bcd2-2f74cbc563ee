# 📋 دليل العقود المطورة - معرض بوخليفة للسيارات

## 🎯 التحديثات الجديدة

### ✨ تطوير عقد بيع الأرقام المميزة

تم تطوير عقد بيع الأرقام المميزة ليشمل:

#### 📝 **معلومات شاملة للمعرض:**
- اسم المعرض: **معرض بوخليفة للسيارات**
- الاسم التجاري: **Qatar Boukhalifa Car Showroom**
- رقم السجل التجاري
- رقم ترخيص المعرض
- معلومات الاتصال الكاملة
- المفوض بالتوقيع

#### 👤 **معلومات مفصلة للعميل:**
- الاسم الكامل (عربي وإنجليزي)
- رقم البطاقة الشخصية
- الجنسية
- تاريخ الميلاد
- العنوان الكامل
- المهنة والشركة
- معلومات الاتصال

#### 🔢 **تفاصيل الرقم المميز:**
- عرض الرقم بخط كبير وواضح
- نوع وفئة الرقم
- السعر الأساسي
- شروط التحويل
- الالتزامات القانونية

#### ⚖️ **شروط وأحكام شاملة:**

**شروط البيع:**
- التزامات الدفع
- شروط الاسترداد
- المسؤوليات المالية

**شروط التحويل:**
- إجراءات التحويل الحكومية
- المدة الزمنية للتحويل
- صلاحية الرقم

**الالتزامات القانونية:**
- قوانين المرور القطرية
- قيود التأجير والتنازل
- المسؤوليات القانونية

**ضمانات البائع:**
- ضمان الملكية
- خلو الرقم من النزاعات
- الدعم في إجراءات التحويل

#### ✍️ **قسم التوقيعات المطور:**
- توقيع البائع مع الختم الرسمي
- توقيع المشتري مع بصمة الإبهام
- توقيع الشاهد
- إقرارات قانونية من الطرفين

---

## 🗄️ **تحديث قاعدة البيانات**

### 📊 **حقول جديدة لجدول العملاء:**

```sql
-- معلومات الهوية
name_en TEXT              -- الاسم بالإنجليزية
id_number TEXT            -- رقم البطاقة الشخصية
nationality TEXT          -- الجنسية
birth_date DATE           -- تاريخ الميلاد

-- معلومات الاتصال والعنوان
address TEXT              -- العنوان
city TEXT                 -- المدينة
postal_code TEXT          -- الرمز البريدي

-- معلومات إضافية
profession TEXT           -- المهنة
company TEXT              -- الشركة
monthly_income REAL       -- الراتب الشهري

-- معلومات النظام
created_at DATETIME       -- تاريخ الإنشاء
updated_at DATETIME       -- تاريخ التحديث
notes TEXT                -- ملاحظات
```

---

## 🚀 **كيفية التشغيل**

### 1️⃣ **تحديث قاعدة البيانات:**
```bash
# انقر نقراً مزدوجاً على:
UPDATE_DATABASE.bat

# أو استخدم:
python update_customer_database.py
```

### 2️⃣ **تشغيل النظام:**
```bash
# انقر نقراً مزدوجاً على أي من:
START_SYSTEM.bat
RUN_DIRECT.bat
SIMPLE_START.bat

# أو استخدم:
python working_app.py
```

### 3️⃣ **الوصول للنظام:**
- **الرابط:** http://127.0.0.1:9898
- **المستخدم:** admin
- **كلمة المرور:** admin123

---

## 📋 **استخدام العقود المطورة**

### ✅ **إنشاء عقد رقم مميز:**

1. **اذهب إلى:** العقود → إضافة عقد جديد
2. **اختر نوع العقد:** بيع رقم مميز
3. **أدخل بيانات العميل الكاملة**
4. **اختر الرقم المميز**
5. **حدد طريقة الدفع والمبلغ**
6. **احفظ العقد**

### 🖨️ **طباعة العقد:**

1. **اذهب إلى:** العقود → عرض العقد
2. **انقر على:** طباعة العقد أو تحميل PDF
3. **العقد سيظهر بالتصميم الجديد المطور**

---

## 🎨 **مميزات التصميم الجديد**

### 📱 **تصميم احترافي:**
- رأسية معرض بوخليفة
- ألوان متناسقة
- تنسيق واضح ومنظم

### 📄 **محتوى شامل:**
- جميع البيانات المطلوبة قانونياً
- شروط وأحكام مفصلة
- إقرارات قانونية

### 🖨️ **جاهز للطباعة:**
- تنسيق مناسب للطباعة
- خطوط واضحة
- مساحات للتوقيعات

---

## 🔧 **الملفات المضافة**

```
📁 Desktop/Visual Studio Code/048/
├── 📄 update_customer_database.py    # تحديث قاعدة البيانات
├── 📄 UPDATE_DATABASE.bat            # ملف تشغيل التحديث
├── 📄 ENHANCED_CONTRACTS_GUIDE.md    # هذا الدليل
├── 📄 START_SYSTEM.bat               # تشغيل النظام (محدث)
├── 📄 RUN_DIRECT.bat                 # تشغيل مباشر
├── 📄 SIMPLE_START.bat               # تشغيل بسيط
└── 📄 working_app.py                 # النظام الأساسي (محدث)
```

---

## 🎉 **النتيجة النهائية**

الآن لديك نظام عقود متطور يشمل:

✅ **عقود أرقام مميزة احترافية**  
✅ **بيانات شاملة للعملاء**  
✅ **شروط وأحكام قانونية مفصلة**  
✅ **تصميم جاهز للطباعة**  
✅ **معلومات معرض بوخليفة**  
✅ **قاعدة بيانات محدثة**  

---

**🏢 معرض بوخليفة للسيارات**  
**📍 الدوحة، دولة قطر**  
**📞 +974 XXXX XXXX**  
**🌐 www.boukhalifa.qa**
