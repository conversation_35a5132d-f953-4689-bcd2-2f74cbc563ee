#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Auction, PremiumNumber
from datetime import datetime, timed<PERSON><PERSON>

def create_short_test_auction():
    """إنشاء مزاد اختبار قصير المدة (5 دقائق)"""
    with app.app_context():
        try:
            # البحث عن رقم متاح
            available_number = PremiumNumber.query.filter_by(status='available').first()
            
            if not available_number:
                print("❌ لا توجد أرقام متاحة")
                return False
            
            # إنشاء مزاد جديد
            now = datetime.now()
            auction = Auction(
                premium_number_id=available_number.id,
                title=f"مزاد اختبار للرقم {available_number.number}",
                description="مزاد اختبار قصير المدة لاختبار العداد الزمني",
                starting_price=1000,
                reserve_price=5000,
                bid_increment=100,
                start_time=now,
                end_time=now + timedelta(minutes=5),  # 5 دقائق فقط
                status='active',
                auto_extend=True,
                extend_time=300,  # 5 دقائق تمديد
                views=0,
                total_bids=0,
                current_price=1000
            )
            
            db.session.add(auction)
            
            # تحديث حالة الرقم
            available_number.status = 'in_auction'
            
            db.session.commit()
            
            print(f"✅ تم إنشاء مزاد اختبار بنجاح!")
            print(f"🔢 الرقم: {available_number.number}")
            print(f"🆔 معرف المزاد: {auction.id}")
            print(f"⏰ وقت البداية: {auction.start_time}")
            print(f"⏰ وقت النهاية: {auction.end_time}")
            print(f"🌐 الرابط: http://127.0.0.1:9898/auction/{auction.id}")
            
            return auction.id
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المزاد: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    auction_id = create_short_test_auction()
    if auction_id:
        print(f"\n🎯 يمكنك الآن اختبار المزاد على: http://127.0.0.1:9898/auction/{auction_id}")
