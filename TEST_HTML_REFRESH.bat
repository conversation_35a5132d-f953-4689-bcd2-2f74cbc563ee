@echo off
chcp 65001 > nul
title اختبار HTML للتحديث التلقائي

echo.
echo ========================================
echo   اختبار HTML للتحديث التلقائي
echo   اختبار مستقل بدون Flask
echo ========================================
echo.

cd /d "%~dp0"

echo 🌐 فتح ملف اختبار HTML...
start test_refresh.html

echo.
echo ✅ تم فتح اختبار HTML!
echo.
echo 🔍 ما يجب أن تراه:
echo    1. صفحة زرقاء مع مؤشر في أعلى اليمين
echo    2. وقت يتحدث كل ثانية
echo    3. عداد تنازلي: (3s) → (2s) → (1s)
echo    4. تحديث الصفحة كل 3 ثوان
echo    5. يمكنك تغيير السرعة من القائمة
echo.
echo 🛠️ للتحقق:
echo    - اضغط F12 لفتح Developer Tools
echo    - اذهب إلى Console
echo    - يجب أن ترى رسائل التحديث
echo.
echo 💡 إذا عمل هذا الاختبار:
echo    - المشكلة في Flask وليس في JavaScript
echo    - نحتاج لإصلاح كود Flask
echo.
echo 💡 إذا لم يعمل هذا الاختبار:
echo    - المشكلة في المتصفح أو JavaScript
echo    - جرب متصفح آخر
echo.
echo ⏹️ اضغط أي مفتاح للخروج...
pause > nul
