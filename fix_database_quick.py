#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def fix_database():
    """إصلاح قاعدة البيانات بإضافة الأعمدة المفقودة"""
    
    db_files = ['auction.db', 'working_database.db', 'instance/auction.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"🔧 إصلاح قاعدة البيانات: {db_file}")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # التحقق من وجود جدول customer
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='customer'")
                if cursor.fetchone():
                    print("✅ جدول customer موجود")
                    
                    # التحقق من الأعمدة الموجودة
                    cursor.execute("PRAGMA table_info(customer)")
                    columns = [column[1] for column in cursor.fetchall()]
                    print(f"الأعمدة الموجودة: {columns}")
                    
                    # إضافة الأعمدة المفقودة
                    missing_columns = {
                        'name_en': 'TEXT',
                        'id_number': 'TEXT',
                        'nationality': 'TEXT',
                        'birth_date': 'DATE',
                        'address': 'TEXT',
                        'city': 'TEXT',
                        'postal_code': 'TEXT',
                        'profession': 'TEXT',
                        'company': 'TEXT',
                        'monthly_income': 'FLOAT',
                        'created_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                        'updated_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                        'notes': 'TEXT'
                    }
                    
                    for column_name, column_type in missing_columns.items():
                        if column_name not in columns:
                            try:
                                cursor.execute(f"ALTER TABLE customer ADD COLUMN {column_name} {column_type}")
                                print(f"✅ تم إضافة العمود: {column_name}")
                            except sqlite3.OperationalError as e:
                                if "duplicate column name" in str(e):
                                    print(f"⚠️ العمود {column_name} موجود بالفعل")
                                else:
                                    print(f"❌ خطأ في إضافة العمود {column_name}: {e}")
                    
                    # إنشاء عميل تجريبي إذا لم يكن موجوداً
                    cursor.execute("SELECT COUNT(*) FROM customer")
                    if cursor.fetchone()[0] == 0:
                        cursor.execute("""
                            INSERT INTO customer (name_ar, name_en, phone, email) 
                            VALUES ('أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '+974 5555 1234', '<EMAIL>')
                        """)
                        print("✅ تم إضافة عميل تجريبي")
                
                conn.commit()
                conn.close()
                print(f"✅ تم إصلاح قاعدة البيانات: {db_file}")
                
            except Exception as e:
                print(f"❌ خطأ في إصلاح {db_file}: {e}")
        else:
            print(f"⚠️ قاعدة البيانات غير موجودة: {db_file}")

def create_simple_auction():
    """إنشاء مزاد بسيط للاختبار"""
    
    db_file = 'auction.db'
    if os.path.exists(db_file):
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # إنشاء رقم مميز إذا لم يكن موجوداً
            cursor.execute("SELECT COUNT(*) FROM premium_number WHERE number='777'")
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO premium_number (number, category, price, status) 
                    VALUES ('777', 'مميز', 75000, 'auction')
                """)
                print("✅ تم إضافة الرقم المميز 777")
            
            # الحصول على معرف الرقم المميز
            cursor.execute("SELECT id FROM premium_number WHERE number='777'")
            number_id = cursor.fetchone()[0]
            
            # إنشاء مزاد إذا لم يكن موجوداً
            cursor.execute("SELECT COUNT(*) FROM auction WHERE premium_number_id=?", (number_id,))
            if cursor.fetchone()[0] == 0:
                from datetime import datetime, timedelta
                start_time = datetime.now()
                end_time = start_time + timedelta(minutes=10)
                
                cursor.execute("""
                    INSERT INTO auction (
                        premium_number_id, title, starting_price, current_price, 
                        start_time, end_time, status, commission_rate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    number_id, 
                    'مزاد الرقم المميز 777', 
                    50000, 
                    303000,
                    start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'active',
                    5.0
                ))
                print("✅ تم إنشاء مزاد للرقم 777")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المزاد: {e}")

if __name__ == '__main__':
    print("🔧 إصلاح قاعدة البيانات السريع")
    print("=" * 40)
    
    fix_database()
    create_simple_auction()
    
    print("=" * 40)
    print("✅ تم الانتهاء من الإصلاح")
    print("🚀 يمكنك الآن تشغيل الخادم")
    input("اضغط Enter للمتابعة...")
