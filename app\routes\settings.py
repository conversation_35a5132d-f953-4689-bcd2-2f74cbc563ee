"""
Settings routes for Qatar Car Showroom System
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.setting import Setting
from datetime import datetime
import json

bp = Blueprint('settings', __name__, url_prefix='/settings')

@bp.route('/')
@login_required
def index():
    """Settings main page"""
    if not current_user.has_permission('manage_settings'):
        flash('ليس لديك صلاحية للوصول إلى إعدادات النظام', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get all settings
    settings = Setting.query.all()
    settings_dict = {setting.key: setting.value for setting in settings}
    
    return render_template('settings/index.html', settings=settings_dict)

@bp.route('/company', methods=['GET', 'POST'])
@login_required
def company():
    """Company settings"""
    if not current_user.has_permission('manage_settings'):
        flash('ليس لديك صلاحية لتعديل إعدادات الشركة', 'error')
        return redirect(url_for('settings.index'))
    
    if request.method == 'POST':
        # Get form data
        company_name = request.form.get('company_name', '').strip()
        showroom_name = request.form.get('showroom_name', '').strip()
        phone = request.form.get('phone', '').strip()
        email = request.form.get('email', '').strip()
        website = request.form.get('website', '').strip()
        address_ar = request.form.get('address_ar', '').strip()
        address_en = request.form.get('address_en', '').strip()
        
        # Validation
        if not company_name:
            flash('اسم الشركة مطلوب', 'error')
            return redirect(url_for('settings.company'))
        
        try:
            # Update or create settings
            settings_to_update = [
                ('company_name', company_name),
                ('showroom_name', showroom_name),
                ('company_phone', phone),
                ('company_email', email),
                ('company_website', website),
                ('company_address_ar', address_ar),
                ('company_address_en', address_en)
            ]
            
            for key, value in settings_to_update:
                setting = Setting.query.filter_by(key=key).first()
                if setting:
                    setting.value = value
                    setting.updated_at = datetime.utcnow()
                else:
                    setting = Setting(
                        key=key,
                        value=value,
                        description=f'Company {key}',
                        updated_by_id=current_user.id
                    )
                    db.session.add(setting)
            
            db.session.commit()
            flash('تم تحديث إعدادات الشركة بنجاح', 'success')
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الإعدادات', 'error')
    
    # Get current settings
    settings = Setting.query.all()
    settings_dict = {setting.key: setting.value for setting in settings}
    
    return render_template('settings/company.html', settings=settings_dict)

@bp.route('/system', methods=['GET', 'POST'])
@login_required
def system():
    """System settings"""
    if not current_user.has_permission('manage_settings'):
        flash('ليس لديك صلاحية لتعديل إعدادات النظام', 'error')
        return redirect(url_for('settings.index'))
    
    if request.method == 'POST':
        # Get form data
        default_language = request.form.get('default_language', 'ar')
        currency = request.form.get('currency', 'QAR')
        timezone = request.form.get('timezone', 'Asia/Qatar')
        date_format = request.form.get('date_format', 'dd/mm/yyyy')
        
        try:
            # Update system settings
            settings_to_update = [
                ('default_language', default_language),
                ('currency', currency),
                ('timezone', timezone),
                ('date_format', date_format)
            ]
            
            for key, value in settings_to_update:
                setting = Setting.query.filter_by(key=key).first()
                if setting:
                    setting.value = value
                    setting.updated_at = datetime.utcnow()
                else:
                    setting = Setting(
                        key=key,
                        value=value,
                        description=f'System {key}',
                        updated_by_id=current_user.id
                    )
                    db.session.add(setting)
            
            db.session.commit()
            flash('تم تحديث إعدادات النظام بنجاح', 'success')
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الإعدادات', 'error')
    
    # Get current settings
    settings = Setting.query.all()
    settings_dict = {setting.key: setting.value for setting in settings}
    
    return render_template('settings/system.html', settings=settings_dict)

@bp.route('/ticker', methods=['GET', 'POST'])
@login_required
def ticker():
    """Ticker settings"""
    if not current_user.has_permission('manage_settings'):
        flash('ليس لديك صلاحية لتعديل إعدادات الشريط المتحرك', 'error')
        return redirect(url_for('settings.index'))
    
    if request.method == 'POST':
        # Get form data
        ticker_enabled = bool(request.form.get('ticker_enabled'))
        ticker_speed = request.form.get('ticker_speed', '50')
        ticker_color = request.form.get('ticker_color', '#ffffff')
        ticker_bg_color = request.form.get('ticker_bg_color', '#dc3545')
        
        try:
            # Update ticker settings
            settings_to_update = [
                ('ticker_enabled', str(ticker_enabled)),
                ('ticker_speed', ticker_speed),
                ('ticker_color', ticker_color),
                ('ticker_bg_color', ticker_bg_color)
            ]
            
            for key, value in settings_to_update:
                setting = Setting.query.filter_by(key=key).first()
                if setting:
                    setting.value = value
                    setting.updated_at = datetime.utcnow()
                else:
                    setting = Setting(
                        key=key,
                        value=value,
                        description=f'Ticker {key}',
                        updated_by_id=current_user.id
                    )
                    db.session.add(setting)
            
            db.session.commit()
            flash('تم تحديث إعدادات الشريط المتحرك بنجاح', 'success')
            
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الإعدادات', 'error')
    
    # Get current settings
    settings = Setting.query.all()
    settings_dict = {setting.key: setting.value for setting in settings}
    
    return render_template('settings/ticker.html', settings=settings_dict)

@bp.route('/database')
@login_required
def database():
    """Database management"""
    if not current_user.has_permission('manage_settings'):
        flash('ليس لديك صلاحية لإدارة قاعدة البيانات', 'error')
        return redirect(url_for('settings.index'))
    
    # Get database statistics
    from app.models.user import User
    from app.models.customer import Customer
    from app.models.car import Car
    from app.models.contract import Contract
    from app.models.premium_number import PremiumNumber
    from app.models.maintenance import Maintenance
    
    stats = {
        'users': User.query.count(),
        'customers': Customer.query.count(),
        'cars': Car.query.count(),
        'contracts': Contract.query.count(),
        'premium_numbers': PremiumNumber.query.count(),
        'maintenance_records': Maintenance.query.count()
    }
    
    return render_template('settings/database.html', stats=stats)

@bp.route('/backup')
@login_required
def backup():
    """Create database backup"""
    if not current_user.has_permission('manage_settings'):
        return jsonify({'error': 'Unauthorized'}), 403
    
    try:
        import shutil
        import os
        from datetime import datetime
        
        # Create backup filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.db'
        
        # Copy database file
        db_path = 'instance/database.db'
        backup_path = f'instance/{backup_filename}'
        
        if os.path.exists(db_path):
            shutil.copy2(db_path, backup_path)
            return jsonify({
                'success': True,
                'message': f'تم إنشاء النسخة الاحتياطية: {backup_filename}',
                'filename': backup_filename
            })
        else:
            return jsonify({
                'success': False,
                'message': 'ملف قاعدة البيانات غير موجود'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'
        })

@bp.route('/export')
@login_required
def export():
    """Export data"""
    if not current_user.has_permission('manage_settings'):
        flash('ليس لديك صلاحية لتصدير البيانات', 'error')
        return redirect(url_for('settings.index'))
    
    return render_template('settings/export.html')

@bp.route('/api/setting/<key>', methods=['GET', 'POST'])
@login_required
def api_setting(key):
    """API endpoint for individual setting"""
    if not current_user.has_permission('manage_settings'):
        return jsonify({'error': 'Unauthorized'}), 403
    
    if request.method == 'POST':
        value = request.json.get('value')
        
        try:
            setting = Setting.query.filter_by(key=key).first()
            if setting:
                setting.value = value
                setting.updated_at = datetime.utcnow()
            else:
                setting = Setting(
                    key=key,
                    value=value,
                    description=f'Setting {key}',
                    updated_by_id=current_user.id
                )
                db.session.add(setting)
            
            db.session.commit()
            return jsonify({'success': True, 'message': 'تم تحديث الإعداد'})
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})
    
    else:
        setting = Setting.query.filter_by(key=key).first()
        if setting:
            return jsonify({'value': setting.value})
        else:
            return jsonify({'value': None})
