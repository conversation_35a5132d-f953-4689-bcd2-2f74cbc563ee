#!/usr/bin/env python3
"""
Qatar Car Showroom - Final Run Script
Resolves config.py issue and runs with premium numbers support
"""

import os
import sys

# Ensure we're in the right directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Add to Python path
sys.path.insert(0, script_dir)

def main():
    print("🚀 نظام معرض قطر للسيارات - الإصدار النهائي")
    print("🔧 مع حل مشكلة config.py ودعم الأرقام المميزة")
    print("=" * 60)
    
    try:
        # Test imports first
        print("🔍 فحص المكتبات...")
        
        from flask import Flask
        print("✅ Flask متاح")
        
        from flask_sqlalchemy import SQLAlchemy
        print("✅ SQLAlchemy متاح")
        
        from flask_login import LoginManager
        print("✅ Flask-Login متاح")
        
        # Try to import our app
        print("🔍 فحص التطبيق...")
        from app import create_app, db
        print("✅ تم حل مشكلة config.py!")
        
        # Create app
        print("🏗️ إنشاء التطبيق...")
        app = create_app()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # Setup database
        print("🗄️ إعداد قاعدة البيانات...")
        with app.app_context():
            db.create_all()
            
            # Create admin user
            from app.models.user import User
            from werkzeug.security import generate_password_hash
            
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role='admin',
                    is_active=True
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء مستخدم admin")
            else:
                print("✅ مستخدم admin موجود")
        
        print("=" * 60)
        print("🎉 نظام معرض قطر للسيارات - جاهز للتشغيل!")
        print("=" * 60)
        print("🌐 الخادم: http://127.0.0.1:1414")
        print("🔐 المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("✨ الميزات الجديدة:")
        print("   🚗 بيع السيارات")
        print("   🔢 بيع الأرقام المميزة")
        print("   🎯 العقود المدمجة")
        print("   💳 طرق دفع متعددة")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)
        
        # Start server
        app.run(host='127.0.0.1', port=1414, debug=False, use_reloader=False)
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت جميع المكتبات المطلوبة")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
