import sqlite3
from datetime import datetime, timedelta

# الاتصال بقاعدة البيانات
conn = sqlite3.connect('auction.db')
cursor = conn.cursor()

try:
    # حذف البيانات القديمة
    cursor.execute("DELETE FROM bid")
    cursor.execute("DELETE FROM auction")
    cursor.execute("DELETE FROM customer")
    cursor.execute("DELETE FROM premium_number")
    
    # إنشاء عميل
    cursor.execute("""
        INSERT INTO customer (name_ar, phone, email) 
        VALUES (?, ?, ?)
    """, ("أحمد محمد الكعبي", "+974 5555 1234", "<EMAIL>"))
    customer_id = cursor.lastrowid
    
    # إنشاء رقم مميز
    cursor.execute("""
        INSERT INTO premium_number (number, category, price, status) 
        VALUES (?, ?, ?, ?)
    """, ("777777", "VIP", 75000, "auction"))
    premium_number_id = cursor.lastrowid
    
    # إنشاء مزاد منتهي
    end_time = datetime.now() - timedelta(hours=1)
    start_time = end_time - timedelta(hours=2)
    
    cursor.execute("""
        INSERT INTO auction (
            title, description, premium_number_id, starting_price, 
            current_price, reserve_price, bid_increment, start_time, 
            end_time, status, auto_extend, commission_rate, 
            commission_amount, total_bids, winner, winner_id, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        "مزاد الرقم المميز 777777",
        "مزاد تجريبي لاختبار عرض الفائز",
        premium_number_id,
        50000,
        78850,
        60000,
        1000,
        start_time.isoformat(),
        end_time.isoformat(),
        "ended",
        0,
        5.0,
        3942.5,
        1,
        "أحمد محمد الكعبي",
        customer_id,
        datetime.now().isoformat()
    ))
    auction_id = cursor.lastrowid
    
    # إنشاء مزايدة فائزة
    bid_time = end_time - timedelta(minutes=5)
    cursor.execute("""
        INSERT INTO bid (auction_id, customer_id, bid_amount, bid_time) 
        VALUES (?, ?, ?, ?)
    """, (auction_id, customer_id, 78850, bid_time.isoformat()))
    
    # حفظ التغييرات
    conn.commit()
    
    print("✅ تم إنشاء البيانات بنجاح!")
    print(f"🏆 الفائز: أحمد محمد الكعبي")
    print(f"💰 المبلغ: 78,850 ر.ق")
    print(f"🌐 اختبر: http://127.0.0.1:9898/auction/standalone/{auction_id}")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    conn.rollback()
finally:
    conn.close()
