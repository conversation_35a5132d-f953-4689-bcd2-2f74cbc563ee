#!/usr/bin/env python3
"""
اختبار إجراءات الصيانة المفعلة
"""

import requests
import json

def test_maintenance_actions():
    """اختبار جميع إجراءات الصيانة"""
    base_url = "http://127.0.0.1:9898"
    
    print("🧪 اختبار إجراءات الصيانة المفعلة")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # تسجيل الدخول
        print("🔐 تسجيل الدخول...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
            
            # اختبار صفحة جميع الصيانات
            print("\n📋 اختبار صفحة جميع الصيانات...")
            maintenance_response = session.get(f"{base_url}/maintenance/all")
            
            if maintenance_response.status_code == 200:
                print("✅ صفحة جميع الصيانات تعمل")
                
                # البحث عن روابط الإجراءات في الصفحة
                content = maintenance_response.text

                # التحقق من وجود روابط الإجراءات
                actions_found = []

                if 'view_maintenance' in content:
                    actions_found.append("عرض التفاصيل")

                if 'edit_maintenance' in content:
                    actions_found.append("تعديل")

                if 'print_maintenance' in content:
                    actions_found.append("طباعة")

                if 'deleteMaintenance' in content:
                    actions_found.append("حذف")

                # البحث عن أيقونات الإجراءات
                if 'fa-eye' in content:
                    if "عرض التفاصيل" not in actions_found:
                        actions_found.append("أيقونة عرض")

                if 'fa-edit' in content:
                    if "تعديل" not in actions_found:
                        actions_found.append("أيقونة تعديل")

                if 'fa-print' in content:
                    if "طباعة" not in actions_found:
                        actions_found.append("أيقونة طباعة")

                if 'fa-trash' in content:
                    if "حذف" not in actions_found:
                        actions_found.append("أيقونة حذف")

                print(f"🔗 الإجراءات المتاحة: {', '.join(actions_found)}")

                # طباعة جزء من المحتوى للتشخيص
                if 'btn-group' in content:
                    print("✅ تم العثور على مجموعة أزرار")
                else:
                    print("❌ لم يتم العثور على مجموعة أزرار")
                
                if len(actions_found) >= 3:
                    print("✅ معظم الإجراءات مفعلة بنجاح!")
                    
                    # اختبار API حذف الإشعارات
                    print("\n🗑️ اختبار API حذف الإشعارات...")
                    
                    dismiss_data = {
                        'action': 'dismiss_all_homepage'
                    }
                    
                    api_response = session.post(
                        f"{base_url}/api/notifications/dismiss-all",
                        json=dismiss_data,
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if api_response.status_code == 200:
                        api_data = api_response.json()
                        print(f"✅ API يعمل: {api_data.get('message')}")
                        print(f"📊 عدد الإشعارات المحذوفة: {api_data.get('dismissed_count', 0)}")
                    else:
                        print(f"❌ خطأ في API: {api_response.status_code}")
                    
                    return True
                else:
                    print(f"⚠️ بعض الإجراءات مفقودة. تم العثور على {len(actions_found)} من 4")
                    return False
            else:
                print(f"❌ خطأ في صفحة إدارة الصيانة: {maintenance_response.status_code}")
                return False
                
        else:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_specific_endpoints():
    """اختبار endpoints محددة"""
    base_url = "http://127.0.0.1:9898"
    
    print("\n🔍 اختبار endpoints محددة...")
    print("-" * 40)
    
    session = requests.Session()
    
    # تسجيل الدخول أولاً
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    session.post(f"{base_url}/login", data=login_data)
    
    # قائمة endpoints للاختبار
    endpoints_to_test = [
        ("/maintenance", "صفحة إدارة الصيانة"),
        ("/maintenance/all", "جميع الصيانات"),
        ("/maintenance/reports", "تقارير الصيانة"),
        ("/maintenance/calendar", "تقويم الصيانة")
    ]
    
    for endpoint, description in endpoints_to_test:
        try:
            response = session.get(f"{base_url}{endpoint}")
            if response.status_code == 200:
                print(f"✅ {description}: يعمل")
            else:
                print(f"❌ {description}: خطأ {response.status_code}")
        except Exception as e:
            print(f"❌ {description}: خطأ في الاتصال")

if __name__ == '__main__':
    success = test_maintenance_actions()
    test_specific_endpoints()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم تفعيل جميع إجراءات الصيانة بنجاح!")
        print("📋 يمكنك الآن:")
        print("   👁️ عرض تفاصيل أي صيانة")
        print("   ✏️ تعديل بيانات الصيانة")
        print("   🖨️ طباعة تقرير الصيانة")
        print("   🗑️ حذف الصيانة")
        print("   📱 حذف جميع الإشعارات")
    else:
        print("⚠️ بعض الإجراءات قد تحتاج إلى مراجعة")
    
    print("\n🌐 افتح المتصفح واذهب إلى:")
    print("   http://127.0.0.1:9898/maintenance")
    print("=" * 60)
