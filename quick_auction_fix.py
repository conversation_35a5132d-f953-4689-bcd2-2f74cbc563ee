#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template_string, redirect, url_for, session, request, flash
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'qatar-auction-2024'

def init_db():
    """إنشاء قاعدة بيانات بسيطة"""
    conn = sqlite3.connect('simple_auction.db')
    cursor = conn.cursor()
    
    # إنشاء جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT UNIQUE,
            password TEXT,
            name TEXT
        )
    ''')
    
    # إنشاء مستخدم افتراضي
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password, name) 
        VALUES ('admin', 'admin', 'مدير النظام')
    ''')
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معرض قطر للسيارات - لوحة التحكم</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .sidebar {
                background: linear-gradient(135deg, #2c3e50, #34495e);
                min-height: 100vh;
                padding: 20px 0;
            }
            .sidebar .nav-link {
                color: #ecf0f1;
                padding: 15px 25px;
                margin: 5px 15px;
                border-radius: 10px;
                transition: all 0.3s ease;
            }
            .sidebar .nav-link:hover {
                background: rgba(255,255,255,0.1);
                color: white;
                transform: translateX(-5px);
            }
            .sidebar .nav-link.active {
                background: #3498db;
                color: white;
            }
            .main-content {
                background: #f8f9fa;
                min-height: 100vh;
                padding: 30px;
            }
            .card {
                border: none;
                border-radius: 15px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .card:hover {
                transform: translateY(-5px);
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3 sidebar">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-car"></i> معرض قطر للسيارات
                        </h4>
                        <small class="text-light">لوحة التحكم</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                        <a class="nav-link" href="{{ url_for('auction') }}">
                            <i class="fas fa-gavel"></i> المزادات
                        </a>
                        <a class="nav-link" href="#" onclick="openStandaloneAuctions()">
                            <i class="fas fa-external-link-alt"></i> المزادات المستقلة
                        </a>
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </nav>
                </div>
                
                <!-- Main Content -->
                <div class="col-md-9 main-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>مرحباً بك في لوحة التحكم</h2>
                        <div class="text-muted">
                            <i class="fas fa-clock"></i> {{ current_time }}
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-gavel fa-3x mb-3"></i>
                                    <h5>المزادات</h5>
                                    <p>إدارة المزادات والمزايدات</p>
                                    <a href="{{ url_for('auction') }}" class="btn btn-light">
                                        <i class="fas fa-arrow-left"></i> دخول
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-external-link-alt fa-3x mb-3"></i>
                                    <h5>المزادات المستقلة</h5>
                                    <p>عرض المزادات بتحديث تلقائي</p>
                                    <button class="btn btn-light" onclick="openStandaloneAuctions()">
                                        <i class="fas fa-eye"></i> عرض
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-cog fa-3x mb-3"></i>
                                    <h5>الإعدادات</h5>
                                    <p>إعدادات النظام والمستخدمين</p>
                                    <button class="btn btn-light" disabled>
                                        <i class="fas fa-tools"></i> قريباً
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Cards -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h5><i class="fas fa-info-circle"></i> حالة النظام</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> 
                                        <strong>النظام يعمل بشكل طبيعي</strong>
                                        <br>
                                        <small>جميع الخدمات متاحة والمزادات المستقلة تعمل بتحديث تلقائي كل 3 ثوان</small>
                                    </div>
                                    
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <h4 class="text-primary">3</h4>
                                            <small>مزادات متاحة</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h4 class="text-success">100%</h4>
                                            <small>وقت التشغيل</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h4 class="text-info">3s</h4>
                                            <small>تحديث تلقائي</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h4 class="text-warning">{{ current_time.split()[1] }}</h4>
                                            <small>الوقت الحالي</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            function openStandaloneAuctions() {
                // فتح صفحة الفهرس للمزادات المستقلة
                window.open('auctions_index.html', '_blank');
            }
            
            // تحديث الوقت كل ثانية
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('ar-QA');
                document.querySelector('.text-muted').innerHTML = '<i class="fas fa-clock"></i> ' + timeString;
            }
            
            setInterval(updateTime, 1000);
        </script>
    </body>
    </html>
    ''', current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username == 'admin' and password == 'admin':
            session['user_id'] = 1
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-card {
                background: rgba(255,255,255,0.95);
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                backdrop-filter: blur(10px);
                max-width: 400px;
                width: 100%;
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="fas fa-car fa-3x text-primary mb-3"></i>
                <h3>معرض قطر للسيارات</h3>
                <p class="text-muted">تسجيل الدخول للوحة التحكم</p>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" name="username" value="admin" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" name="password" value="admin" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </button>
            </form>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    المستخدم: admin | كلمة المرور: admin
                </small>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/auction')
def auction():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>المزادات - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-gavel"></i> نظام المزادات</h2>
                <button class="btn btn-success" onclick="openStandaloneAuctions()">
                    <i class="fas fa-external-link-alt"></i> المزادات المستقلة
                </button>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5><i class="fas fa-fire"></i> مزاد الرقم 777</h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-danger">777</h2>
                            <p><strong>السعر الحالي:</strong> 303,000 ر.ق</p>
                            <p><strong>الوقت المتبقي:</strong> 05:30</p>
                            <p><strong>المزايدات:</strong> 24</p>
                            <a href="auction_777_standalone.html" target="_blank" class="btn btn-danger">
                                <i class="fas fa-eye"></i> عرض المزاد
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-crown"></i> مزاد الرقم 999</h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-primary">999</h2>
                            <p><strong>السعر الحالي:</strong> 450,000 ر.ق</p>
                            <p><strong>الوقت المتبقي:</strong> 07:45</p>
                            <p><strong>المزايدات:</strong> 38</p>
                            <a href="auction_999_standalone.html" target="_blank" class="btn btn-primary">
                                <i class="fas fa-eye"></i> عرض المزاد
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-star"></i> مزاد الرقم 555</h5>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-success">555</h2>
                            <p><strong>السعر الحالي:</strong> 180,000 ر.ق</p>
                            <p><strong>الوقت المتبقي:</strong> 03:15</p>
                            <p><strong>المزايدات:</strong> 16</p>
                            <a href="auction_555_standalone.html" target="_blank" class="btn btn-success">
                                <i class="fas fa-eye"></i> عرض المزاد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> جميع المزادات تعمل بنظام التحديث التلقائي كل 3 ثوان.
                انقر على "عرض المزاد" لفتح الصفحة المستقلة لكل مزاد.
            </div>
        </div>

        <script>
            function openStandaloneAuctions() {
                window.open('auctions_index.html', '_blank');
            }
        </script>
    </body>
    </html>
    ''')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

if __name__ == '__main__':
    init_db()
    print("🚀 تشغيل نظام المزادات البسيط")
    print("=" * 40)
    print("🌐 الخادم: http://127.0.0.1:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 40)
    
    app.run(host='127.0.0.1', port=5000, debug=False)
