#!/usr/bin/env python3
"""
Direct Server Start - No imports, just execution
"""

import os
import sys

# Change to script directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

print("Starting Qatar Car Showroom...")
print(f"Working directory: {os.getcwd()}")

# Check if working_app.py exists
if os.path.exists('working_app.py'):
    print("Found working_app.py - executing...")
    
    # Execute the file directly
    try:
        with open('working_app.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Execute the code
        exec(code)
        
    except Exception as e:
        print(f"Error executing working_app.py: {e}")
        import traceback
        traceback.print_exc()
else:
    print("ERROR: working_app.py not found!")
    print("Available files:")
    for file in os.listdir('.'):
        if file.endswith('.py'):
            print(f"  - {file}")

input("Press Enter to exit...")
