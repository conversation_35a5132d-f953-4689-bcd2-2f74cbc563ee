{% extends "base.html" %}

{% block title %}إدارة قاعدة البيانات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-database me-2"></i>إدارة قاعدة البيانات</h2>
                <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة للإعدادات
                </a>
            </div>

            <!-- Database Statistics -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4 class="mb-0">{{ stats.users }}</h4>
                            <small class="text-muted">المستخدمين</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-user-friends fa-2x text-success mb-2"></i>
                            <h4 class="mb-0">{{ stats.customers }}</h4>
                            <small class="text-muted">العملاء</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-car fa-2x text-info mb-2"></i>
                            <h4 class="mb-0">{{ stats.cars }}</h4>
                            <small class="text-muted">السيارات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-file-contract fa-2x text-warning mb-2"></i>
                            <h4 class="mb-0">{{ stats.contracts }}</h4>
                            <small class="text-muted">العقود</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-hashtag fa-2x text-danger mb-2"></i>
                            <h4 class="mb-0">{{ stats.premium_numbers }}</h4>
                            <small class="text-muted">الأرقام المميزة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-tools fa-2x text-secondary mb-2"></i>
                            <h4 class="mb-0">{{ stats.maintenance_records }}</h4>
                            <small class="text-muted">سجلات الصيانة</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Backup Management -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-save me-2"></i>إدارة النسخ الاحتياطية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>إنشاء نسخة احتياطية</h6>
                                    <p class="text-muted">إنشاء نسخة احتياطية من قاعدة البيانات الحالية</p>
                                    <button class="btn btn-primary" id="createBackupBtn">
                                        <i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <h6>استعادة نسخة احتياطية</h6>
                                    <p class="text-muted">استعادة البيانات من نسخة احتياطية سابقة</p>
                                    <div class="input-group">
                                        <input type="file" class="form-control" id="restoreFile" accept=".db">
                                        <button class="btn btn-warning" id="restoreBackupBtn">
                                            <i class="fas fa-upload me-1"></i>استعادة
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div id="backupResult" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Database Maintenance -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-wrench me-2"></i>صيانة قاعدة البيانات</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-info" id="optimizeBtn">
                                            <i class="fas fa-compress-alt me-1"></i>تحسين قاعدة البيانات
                                        </button>
                                    </div>
                                    <small class="text-muted">تحسين أداء قاعدة البيانات</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-warning" id="repairBtn">
                                            <i class="fas fa-tools me-1"></i>إصلاح قاعدة البيانات
                                        </button>
                                    </div>
                                    <small class="text-muted">إصلاح الأخطاء المحتملة</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="d-grid">
                                        <button class="btn btn-outline-success" id="analyzeBtn">
                                            <i class="fas fa-chart-line me-1"></i>تحليل البيانات
                                        </button>
                                    </div>
                                    <small class="text-muted">تحليل استخدام المساحة</small>
                                </div>
                            </div>
                            
                            <div id="maintenanceResult" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Danger Zone -->
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>منطقة خطر</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <strong>تحذير:</strong> العمليات التالية خطيرة ولا يمكن التراجع عنها. تأكد من إنشاء نسخة احتياطية أولاً.
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <h6>حذف جميع البيانات</h6>
                                    <p class="text-muted">حذف جميع البيانات والاحتفاظ بالهيكل فقط</p>
                                    <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#clearDataModal">
                                        <i class="fas fa-trash me-1"></i>حذف جميع البيانات
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <h6>إعادة تعيين النظام</h6>
                                    <p class="text-muted">إعادة النظام لحالته الأولى</p>
                                    <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#resetSystemModal">
                                        <i class="fas fa-redo me-1"></i>إعادة تعيين النظام
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Database Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات قاعدة البيانات</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <strong>نوع قاعدة البيانات:</strong> SQLite
                                </li>
                                <li class="mb-2">
                                    <strong>موقع الملف:</strong> instance/database.db
                                </li>
                                <li class="mb-2">
                                    <strong>حجم الملف:</strong> <span id="dbSize">جاري الحساب...</span>
                                </li>
                                <li class="mb-2">
                                    <strong>آخر تحديث:</strong> <span id="lastModified">جاري التحقق...</span>
                                </li>
                                <li class="mb-2">
                                    <strong>إجمالي الجداول:</strong> {{ stats.users + stats.customers + stats.cars + stats.contracts + stats.premium_numbers + stats.maintenance_records }}
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                                    <i class="fas fa-sync me-1"></i>تحديث الإحصائيات
                                </button>
                                <button class="btn btn-outline-info btn-sm" id="checkIntegrityBtn">
                                    <i class="fas fa-check-circle me-1"></i>فحص سلامة البيانات
                                </button>
                                <button class="btn btn-outline-success btn-sm" id="exportSchemaBtn">
                                    <i class="fas fa-file-code me-1"></i>تصدير هيكل قاعدة البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clear Data Modal -->
<div class="modal fade" id="clearDataModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تأكيد حذف البيانات</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء سيحذف جميع البيانات نهائياً ولا يمكن التراجع عنه.
                </div>
                <p>اكتب <strong>DELETE ALL DATA</strong> للتأكيد:</p>
                <input type="text" class="form-control" id="clearDataConfirm" placeholder="DELETE ALL DATA">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmClearData" disabled>حذف جميع البيانات</button>
            </div>
        </div>
    </div>
</div>

<!-- Reset System Modal -->
<div class="modal fade" id="resetSystemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تأكيد إعادة تعيين النظام</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء سيعيد النظام لحالته الأولى ويحذف جميع البيانات والإعدادات.
                </div>
                <p>اكتب <strong>RESET SYSTEM</strong> للتأكيد:</p>
                <input type="text" class="form-control" id="resetSystemConfirm" placeholder="RESET SYSTEM">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmResetSystem" disabled>إعادة تعيين النظام</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Create backup
    document.getElementById('createBackupBtn').addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإنشاء...';
        
        fetch('{{ url_for("settings.backup") }}')
            .then(response => response.json())
            .then(data => {
                const result = document.getElementById('backupResult');
                if (data.success) {
                    result.innerHTML = `<div class="alert alert-success">
                        <i class="fas fa-check me-2"></i>${data.message}
                    </div>`;
                } else {
                    result.innerHTML = `<div class="alert alert-danger">
                        <i class="fas fa-times me-2"></i>${data.message}
                    </div>`;
                }
            })
            .catch(error => {
                document.getElementById('backupResult').innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-times me-2"></i>حدث خطأ أثناء إنشاء النسخة الاحتياطية
                </div>`;
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-download me-1"></i>إنشاء نسخة احتياطية';
            });
    });

    // Confirmation inputs
    document.getElementById('clearDataConfirm').addEventListener('input', function() {
        document.getElementById('confirmClearData').disabled = this.value !== 'DELETE ALL DATA';
    });

    document.getElementById('resetSystemConfirm').addEventListener('input', function() {
        document.getElementById('confirmResetSystem').disabled = this.value !== 'RESET SYSTEM';
    });

    // Maintenance buttons
    ['optimize', 'repair', 'analyze', 'checkIntegrity'].forEach(action => {
        const btn = document.getElementById(action + 'Btn');
        if (btn) {
            btn.addEventListener('click', function() {
                this.disabled = true;
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التنفيذ...';
                
                // Simulate operation
                setTimeout(() => {
                    const result = document.getElementById('maintenanceResult') || document.getElementById('backupResult');
                    result.innerHTML = `<div class="alert alert-success">
                        <i class="fas fa-check me-2"></i>تم تنفيذ عملية ${action} بنجاح
                    </div>`;
                    
                    this.disabled = false;
                    this.innerHTML = originalText;
                }, 2000);
            });
        }
    });
});
</script>
{% endblock %}
