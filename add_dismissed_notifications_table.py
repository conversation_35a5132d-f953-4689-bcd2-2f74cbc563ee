#!/usr/bin/env python3
"""
إضافة جدول الإشعارات المحذوفة - نظام معرض قطر للسيارات
"""

import sqlite3
from datetime import datetime

def add_dismissed_notifications_table():
    """إضافة جدول الإشعارات المحذوفة"""
    
    print("🚀 إضافة جدول الإشعارات المحذوفة...")
    print("=" * 60)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('car_showroom.db')
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='dismissed_notification'
        """)
        
        if cursor.fetchone():
            print("✅ جدول الإشعارات المحذوفة موجود مسبقاً")
        else:
            # إنشاء جدول الإشعارات المحذوفة
            cursor.execute('''
                CREATE TABLE dismissed_notification (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    notification_type VARCHAR(50) NOT NULL,
                    notification_id VARCHAR(100) NOT NULL,
                    dismissed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id),
                    UNIQUE(user_id, notification_type, notification_id)
                )
            ''')
            
            print("✅ تم إنشاء جدول الإشعارات المحذوفة")
        
        # إنشاء فهرس لتحسين الأداء
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_dismissed_notification_user 
            ON dismissed_notification(user_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_dismissed_notification_type 
            ON dismissed_notification(notification_type, notification_id)
        ''')
        
        print("✅ تم إنشاء الفهارس")
        
        # حفظ التغييرات
        conn.commit()
        
        print("\n🎉 تم إضافة جدول الإشعارات المحذوفة بنجاح!")
        print("\n📊 الميزات الجديدة:")
        print("   🗑️ حذف الإشعارات الفردية")
        print("   🔄 استعادة الإشعارات المحذوفة")
        print("   👤 إشعارات مخصصة لكل مستخدم")
        print("   📈 تحسين الأداء مع الفهارس")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة جدول الإشعارات المحذوفة: {e}")
        return False
        
    finally:
        if conn:
            conn.close()

def test_dismissed_notifications():
    """اختبار جدول الإشعارات المحذوفة"""
    
    print("\n🔍 اختبار جدول الإشعارات المحذوفة...")
    
    try:
        conn = sqlite3.connect('car_showroom.db')
        cursor = conn.cursor()
        
        # اختبار إدراج إشعار محذوف
        cursor.execute('''
            INSERT OR IGNORE INTO dismissed_notification 
            (user_id, notification_type, notification_id)
            VALUES (1, 'test_notification', 'test_123')
        ''')
        
        # التحقق من الإدراج
        cursor.execute('''
            SELECT COUNT(*) FROM dismissed_notification 
            WHERE notification_type = 'test_notification'
        ''')
        
        count = cursor.fetchone()[0]
        
        if count > 0:
            print("✅ اختبار الإدراج: نجح")
            
            # حذف البيانات التجريبية
            cursor.execute('''
                DELETE FROM dismissed_notification 
                WHERE notification_type = 'test_notification'
            ''')
            
            print("✅ اختبار الحذف: نجح")
        else:
            print("❌ اختبار الإدراج: فشل")
        
        conn.commit()
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجدول: {e}")
        return False
        
    finally:
        if conn:
            conn.close()

def show_table_info():
    """عرض معلومات الجدول"""
    
    print("\n📋 معلومات جدول الإشعارات المحذوفة:")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('car_showroom.db')
        cursor = conn.cursor()
        
        # عرض هيكل الجدول
        cursor.execute("PRAGMA table_info(dismissed_notification)")
        columns = cursor.fetchall()
        
        print("📊 أعمدة الجدول:")
        for col in columns:
            print(f"   • {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # عرض الفهارس
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='index' AND tbl_name='dismissed_notification'
        """)
        indexes = cursor.fetchall()
        
        print("\n🔍 الفهارس:")
        for idx in indexes:
            print(f"   • {idx[0]}")
        
        # عرض عدد السجلات
        cursor.execute("SELECT COUNT(*) FROM dismissed_notification")
        count = cursor.fetchone()[0]
        
        print(f"\n📈 عدد السجلات: {count}")
        
    except Exception as e:
        print(f"❌ خطأ في عرض معلومات الجدول: {e}")
        
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    print("🚀 إضافة نظام حذف الإشعارات")
    print("=" * 60)
    
    # إضافة الجدول
    success = add_dismissed_notifications_table()
    
    if success:
        # اختبار الجدول
        test_success = test_dismissed_notifications()
        
        # عرض معلومات الجدول
        show_table_info()
        
        print("\n" + "=" * 60)
        print("🎉 تم تفعيل نظام حذف الإشعارات بنجاح!")
        print("\n📱 الميزات المتاحة الآن:")
        print("   🗑️ حذف الإشعارات الفردية بزر X")
        print("   🔄 استعادة الإشعارات المحذوفة")
        print("   👤 إشعارات مخصصة لكل مستخدم")
        print("   📊 تحديث العداد تلقائياً")
        print("   ✨ تأثيرات بصرية عند الحذف")
        
        if test_success:
            print("\n✅ النظام جاهز للاستخدام!")
        else:
            print("\n⚠️ هناك مشاكل في الاختبارات")
    else:
        print("\n❌ فشل في إضافة الجدول")
    
    print("\n" + "=" * 60)
