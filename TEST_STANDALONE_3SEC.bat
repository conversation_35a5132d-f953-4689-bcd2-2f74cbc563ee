@echo off
chcp 65001 > nul
title اختبار العرض المستقل - تحديث كل 3 ثوان

echo.
echo ========================================
echo   اختبار العرض المستقل المطور
echo   تحديث تلقائي كل 3 ثوان
echo ========================================
echo.

cd /d "%~dp0"

echo 🎯 إنشاء مزاد تجريبي...
python create_demo_auction_3sec.py

echo.
echo 🚀 تشغيل النظام...
start /B python working_app.py

echo ⏳ انتظار تشغيل الخادم...
timeout /t 3 /nobreak > nul

echo 🌐 فتح العرض المستقل...
start http://127.0.0.1:9898/auction/standalone/1

echo.
echo ✅ تم تشغيل العرض المستقل بنجاح!
echo.
echo 🔄 المميزات المفعلة:
echo    ✓ تحديث تلقائي كل 3 ثوان
echo    ✓ عداد تنازلي مرئي
echo    ✓ مؤشر تحديث محسن
echo    ✓ أدوات تحكم متقدمة
echo.
echo 💡 لتغيير سرعة التحديث:
echo    - انقر على أيقونة الإعدادات في الصفحة
echo    - اختر السرعة المطلوبة من القائمة
echo.
echo ⏹️ اضغط أي مفتاح لإيقاف النظام...
pause > nul

echo 🛑 إيقاف النظام...
taskkill /f /im python.exe > nul 2>&1
echo ✅ تم إيقاف النظام بنجاح
