#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def update_user_table():
    """تحديث جدول المستخدمين لإضافة الحقول الجديدة"""
    
    db_path = 'working_database.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 جاري تحديث جدول المستخدمين...")
        
        # التحقق من الحقول الموجودة
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 الحقول الموجودة: {columns}")
        
        # إضافة الحقول الجديدة إذا لم تكن موجودة
        new_columns = [
            ('email', 'TEXT'),
            ('full_name', 'TEXT'),
            ('phone', 'TEXT'),
            ('avatar', 'TEXT'),
            ('created_at', 'DATETIME'),
            ('last_login', 'DATETIME')
        ]
        
        for column_name, column_type in new_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE user ADD COLUMN {column_name} {column_type}")
                    print(f"✅ تم إضافة حقل {column_name}")
                except sqlite3.OperationalError as e:
                    print(f"⚠️ خطأ في إضافة حقل {column_name}: {e}")
        
        # تحديث البيانات الموجودة
        current_time = datetime.now().isoformat()
        cursor.execute("""
            UPDATE user 
            SET created_at = ?, full_name = username 
            WHERE created_at IS NULL
        """, (current_time,))
        
        conn.commit()
        print("✅ تم تحديث جدول المستخدمين بنجاح")
        
        # عرض البيانات المحدثة
        cursor.execute("SELECT * FROM user")
        users = cursor.fetchall()
        print(f"👥 عدد المستخدمين: {len(users)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🚀 تحديث قاعدة بيانات المستخدمين")
    print("=" * 50)
    
    success = update_user_table()
    
    if success:
        print("\n🎉 تم التحديث بنجاح!")
        print("يمكنك الآن تشغيل التطبيق بدون مشاكل")
    else:
        print("\n❌ فشل التحديث!")
    
    input("\nاضغط Enter للخروج...")
