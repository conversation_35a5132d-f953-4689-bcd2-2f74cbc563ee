#!/usr/bin/env python3
"""
Simple Run Script for Qatar Car Showroom
"""

import os
import sys

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    print("🚀 تشغيل نظام معرض قطر للسيارات...")
    
    # Import the working app
    from working_app import app, db, User
    from werkzeug.security import generate_password_hash
    
    print("✅ تم استيراد التطبيق بنجاح")
    
    # Setup database and admin user
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # Create admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                password_hash=generate_password_hash('admin123'),
                role='admin',
                is_active=True,
                email='<EMAIL>'
            )
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء مستخدم admin")
        else:
            print("✅ مستخدم admin موجود")
    
    print("=" * 60)
    print("🎉 نظام معرض قطر للسيارات جاهز!")
    print("🌐 الخادم: http://127.0.0.1:9898")
    print("🔐 المستخدم: admin | كلمة المرور: admin123")
    print("=" * 60)
    
    # Start the server
    app.run(host='127.0.0.1', port=9898, debug=False, use_reloader=False)
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من وجود ملف working_app.py")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
    
input("اضغط Enter للخروج...")
