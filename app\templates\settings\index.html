{% extends "base.html" %}

{% block title %}إعدادات النظام{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cogs me-2"></i>إعدادات النظام</h2>
            </div>

            <!-- Settings Cards -->
            <div class="row">
                <!-- Company Settings -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-building fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">إعدادات الشركة</h5>
                            <p class="card-text text-muted">
                                إدارة معلومات الشركة والمعرض
                            </p>
                            <a href="{{ url_for('settings.company') }}" class="btn btn-primary">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-cog fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">إعدادات النظام</h5>
                            <p class="card-text text-muted">
                                اللغة، العملة، والتوقيت
                            </p>
                            <a href="{{ url_for('settings.system') }}" class="btn btn-success">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Ticker Settings -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-scroll fa-3x text-warning"></i>
                            </div>
                            <h5 class="card-title">الشريط المتحرك</h5>
                            <p class="card-text text-muted">
                                إعدادات الشريط المتحرك والإعلانات
                            </p>
                            <a href="{{ url_for('settings.ticker') }}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Database Management -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-database fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">إدارة قاعدة البيانات</h5>
                            <p class="card-text text-muted">
                                النسخ الاحتياطي والإحصائيات
                            </p>
                            <a href="{{ url_for('settings.database') }}" class="btn btn-info">
                                <i class="fas fa-chart-bar me-1"></i>عرض
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Data Export -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-download fa-3x text-secondary"></i>
                            </div>
                            <h5 class="card-title">تصدير البيانات</h5>
                            <p class="card-text text-muted">
                                تصدير البيانات بصيغ مختلفة
                            </p>
                            <a href="{{ url_for('settings.export') }}" class="btn btn-secondary">
                                <i class="fas fa-download me-1"></i>تصدير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-info-circle fa-3x text-dark"></i>
                            </div>
                            <h5 class="card-title">معلومات النظام</h5>
                            <p class="card-text text-muted">
                                إصدار النظام والمعلومات التقنية
                            </p>
                            <button class="btn btn-dark" data-bs-toggle="modal" data-bs-target="#systemInfoModal">
                                <i class="fas fa-info me-1"></i>عرض
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Settings Summary -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>ملخص الإعدادات الحالية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted">معلومات الشركة</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>اسم الشركة:</strong> {{ settings.get('company_name', 'غير محدد') }}</li>
                                        <li><strong>اسم المعرض:</strong> {{ settings.get('showroom_name', 'غير محدد') }}</li>
                                        <li><strong>الهاتف:</strong> {{ settings.get('company_phone', 'غير محدد') }}</li>
                                        <li><strong>البريد الإلكتروني:</strong> {{ settings.get('company_email', 'غير محدد') }}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">إعدادات النظام</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>اللغة الافتراضية:</strong> {{ settings.get('default_language', 'العربية') }}</li>
                                        <li><strong>العملة:</strong> {{ settings.get('currency', 'QAR') }}</li>
                                        <li><strong>المنطقة الزمنية:</strong> {{ settings.get('timezone', 'Asia/Qatar') }}</li>
                                        <li><strong>تنسيق التاريخ:</strong> {{ settings.get('date_format', 'dd/mm/yyyy') }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Info Modal -->
<div class="modal fade" id="systemInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معلومات النظام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات النظام</h6>
                        <ul class="list-unstyled">
                            <li><strong>اسم النظام:</strong> نظام معرض قطر للسيارات</li>
                            <li><strong>الإصدار:</strong> 1.0.0</li>
                            <li><strong>تاريخ الإصدار:</strong> 2024</li>
                            <li><strong>نوع النسخة:</strong> محمولة</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>المعلومات التقنية</h6>
                        <ul class="list-unstyled">
                            <li><strong>إطار العمل:</strong> Flask</li>
                            <li><strong>قاعدة البيانات:</strong> SQLite</li>
                            <li><strong>اللغة:</strong> Python</li>
                            <li><strong>الواجهة:</strong> Bootstrap 5</li>
                        </ul>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <p class="text-muted mb-0">© 2024 معرض قطر للسيارات - جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
