@echo off
chcp 65001 >nul
title معرض قطر للسيارات - Flask

echo.
echo ========================================
echo 🚀 معرض قطر للسيارات - Flask البسيط
echo ========================================
echo.

echo 🔍 فحص Flask...
python -c "import flask; print('✅ Flask متوفر:', flask.__version__)"

echo.
echo 🚀 تشغيل Flask البسيط...
echo 🌐 العنوان: http://127.0.0.1:5000
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin
echo.
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo ========================================
echo.

python SIMPLE_FLASK_APP.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل Flask
    echo.
    echo 💡 الحلول البديلة المتاحة:
    echo.
    echo 1. المزادات المستقلة (تعمل بدون خادم):
    echo    - افتح الملف: auctions_index.html
    echo.
    echo 2. نظام الإدارة التفاعلي:
    echo    - افتح الملف: FINAL_NO_SERVER_SOLUTION.html
    echo.
    echo 3. الخادم البديل:
    echo    - شغل: python WORKING_SERVER_NOW.py
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
)
