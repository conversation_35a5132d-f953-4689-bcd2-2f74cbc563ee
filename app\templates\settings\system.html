{% extends "base.html" %}

{% block title %}إعدادات النظام{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog me-2"></i>إعدادات النظام</h2>
                <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة للإعدادات
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>إعدادات النظام العامة</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <!-- Default Language -->
                                    <div class="col-md-6 mb-3">
                                        <label for="default_language" class="form-label">
                                            <i class="fas fa-language me-1"></i>اللغة الافتراضية
                                        </label>
                                        <select class="form-select" id="default_language" name="default_language">
                                            <option value="ar" {{ 'selected' if settings.get('default_language', 'ar') == 'ar' }}>العربية</option>
                                            <option value="en" {{ 'selected' if settings.get('default_language', 'ar') == 'en' }}>English</option>
                                        </select>
                                    </div>

                                    <!-- Currency -->
                                    <div class="col-md-6 mb-3">
                                        <label for="currency" class="form-label">
                                            <i class="fas fa-money-bill me-1"></i>العملة
                                        </label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="QAR" {{ 'selected' if settings.get('currency', 'QAR') == 'QAR' }}>ريال قطري (QAR)</option>
                                            <option value="USD" {{ 'selected' if settings.get('currency', 'QAR') == 'USD' }}>دولار أمريكي (USD)</option>
                                            <option value="EUR" {{ 'selected' if settings.get('currency', 'QAR') == 'EUR' }}>يورو (EUR)</option>
                                            <option value="SAR" {{ 'selected' if settings.get('currency', 'QAR') == 'SAR' }}>ريال سعودي (SAR)</option>
                                            <option value="AED" {{ 'selected' if settings.get('currency', 'QAR') == 'AED' }}>درهم إماراتي (AED)</option>
                                        </select>
                                    </div>

                                    <!-- Timezone -->
                                    <div class="col-md-6 mb-3">
                                        <label for="timezone" class="form-label">
                                            <i class="fas fa-clock me-1"></i>المنطقة الزمنية
                                        </label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="Asia/Qatar" {{ 'selected' if settings.get('timezone', 'Asia/Qatar') == 'Asia/Qatar' }}>قطر (Asia/Qatar)</option>
                                            <option value="Asia/Riyadh" {{ 'selected' if settings.get('timezone', 'Asia/Qatar') == 'Asia/Riyadh' }}>الرياض (Asia/Riyadh)</option>
                                            <option value="Asia/Dubai" {{ 'selected' if settings.get('timezone', 'Asia/Qatar') == 'Asia/Dubai' }}>دبي (Asia/Dubai)</option>
                                            <option value="UTC" {{ 'selected' if settings.get('timezone', 'Asia/Qatar') == 'UTC' }}>UTC</option>
                                        </select>
                                    </div>

                                    <!-- Date Format -->
                                    <div class="col-md-6 mb-3">
                                        <label for="date_format" class="form-label">
                                            <i class="fas fa-calendar me-1"></i>تنسيق التاريخ
                                        </label>
                                        <select class="form-select" id="date_format" name="date_format">
                                            <option value="dd/mm/yyyy" {{ 'selected' if settings.get('date_format', 'dd/mm/yyyy') == 'dd/mm/yyyy' }}>يوم/شهر/سنة (31/12/2024)</option>
                                            <option value="mm/dd/yyyy" {{ 'selected' if settings.get('date_format', 'dd/mm/yyyy') == 'mm/dd/yyyy' }}>شهر/يوم/سنة (12/31/2024)</option>
                                            <option value="yyyy-mm-dd" {{ 'selected' if settings.get('date_format', 'dd/mm/yyyy') == 'yyyy-mm-dd' }}>سنة-شهر-يوم (2024-12-31)</option>
                                            <option value="dd-mm-yyyy" {{ 'selected' if settings.get('date_format', 'dd/mm/yyyy') == 'dd-mm-yyyy' }}>يوم-شهر-سنة (31-12-2024)</option>
                                        </select>
                                    </div>
                                </div>

                                <hr>

                                <div class="row">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-3">إعدادات إضافية</h6>
                                    </div>

                                    <!-- Auto Backup -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup" 
                                                   {{ 'checked' if settings.get('auto_backup', 'false') == 'true' }}>
                                            <label class="form-check-label" for="auto_backup">
                                                <i class="fas fa-save me-1"></i>النسخ الاحتياطي التلقائي
                                            </label>
                                        </div>
                                        <small class="text-muted">إنشاء نسخة احتياطية يومياً</small>
                                    </div>

                                    <!-- Email Notifications -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" 
                                                   {{ 'checked' if settings.get('email_notifications', 'false') == 'true' }}>
                                            <label class="form-check-label" for="email_notifications">
                                                <i class="fas fa-envelope me-1"></i>إشعارات البريد الإلكتروني
                                            </label>
                                        </div>
                                        <small class="text-muted">إرسال إشعارات مهمة عبر البريد</small>
                                    </div>

                                    <!-- Debug Mode -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="debug_mode" name="debug_mode" 
                                                   {{ 'checked' if settings.get('debug_mode', 'false') == 'true' }}>
                                            <label class="form-check-label" for="debug_mode">
                                                <i class="fas fa-bug me-1"></i>وضع التطوير
                                            </label>
                                        </div>
                                        <small class="text-muted">عرض معلومات إضافية للمطورين</small>
                                    </div>

                                    <!-- Maintenance Mode -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                                   {{ 'checked' if settings.get('maintenance_mode', 'false') == 'true' }}>
                                            <label class="form-check-label" for="maintenance_mode">
                                                <i class="fas fa-tools me-1"></i>وضع الصيانة
                                            </label>
                                        </div>
                                        <small class="text-muted">منع الوصول للنظام مؤقتاً</small>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Current Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>الإعدادات الحالية</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <strong>اللغة:</strong> 
                                    <span class="badge bg-primary">{{ 'العربية' if settings.get('default_language', 'ar') == 'ar' else 'English' }}</span>
                                </li>
                                <li class="mb-2">
                                    <strong>العملة:</strong> 
                                    <span class="badge bg-success">{{ settings.get('currency', 'QAR') }}</span>
                                </li>
                                <li class="mb-2">
                                    <strong>المنطقة الزمنية:</strong> 
                                    <span class="badge bg-info">{{ settings.get('timezone', 'Asia/Qatar') }}</span>
                                </li>
                                <li class="mb-2">
                                    <strong>تنسيق التاريخ:</strong> 
                                    <span class="badge bg-warning">{{ settings.get('date_format', 'dd/mm/yyyy') }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- System Status -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-heartbeat me-2"></i>حالة النظام</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>النسخ الاحتياطي:</span>
                                <span class="badge {{ 'bg-success' if settings.get('auto_backup', 'false') == 'true' else 'bg-secondary' }}">
                                    {{ 'مفعل' if settings.get('auto_backup', 'false') == 'true' else 'معطل' }}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>الإشعارات:</span>
                                <span class="badge {{ 'bg-success' if settings.get('email_notifications', 'false') == 'true' else 'bg-secondary' }}">
                                    {{ 'مفعل' if settings.get('email_notifications', 'false') == 'true' else 'معطل' }}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>وضع التطوير:</span>
                                <span class="badge {{ 'bg-warning' if settings.get('debug_mode', 'false') == 'true' else 'bg-secondary' }}">
                                    {{ 'مفعل' if settings.get('debug_mode', 'false') == 'true' else 'معطل' }}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>وضع الصيانة:</span>
                                <span class="badge {{ 'bg-danger' if settings.get('maintenance_mode', 'false') == 'true' else 'bg-success' }}">
                                    {{ 'مفعل' if settings.get('maintenance_mode', 'false') == 'true' else 'معطل' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
