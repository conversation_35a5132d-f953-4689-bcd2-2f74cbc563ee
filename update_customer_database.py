#!/usr/bin/env python3
"""
تحديث قاعدة البيانات - إضافة حقول جديدة لجدول العملاء
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def update_customer_table():
    """تحديث جدول العملاء بإضافة الحقول الجديدة"""
    
    db_path = 'working_database.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 تحديث جدول العملاء...")
        
        # قائمة الحقول الجديدة
        new_columns = [
            ('name_en', 'TEXT'),
            ('id_number', 'TEXT'),
            ('nationality', 'TEXT'),
            ('birth_date', 'DATE'),
            ('address', 'TEXT'),
            ('city', 'TEXT'),
            ('postal_code', 'TEXT'),
            ('profession', 'TEXT'),
            ('company', 'TEXT'),
            ('monthly_income', 'REAL'),
            ('created_at', 'DATETIME'),
            ('updated_at', 'DATETIME'),
            ('notes', 'TEXT')
        ]
        
        # إضافة كل حقل جديد
        for column_name, column_type in new_columns:
            try:
                cursor.execute(f'ALTER TABLE customer ADD COLUMN {column_name} {column_type}')
                print(f"✅ تم إضافة حقل {column_name}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    print(f"⚠️ حقل {column_name} موجود مسبقاً")
                else:
                    print(f"❌ خطأ في إضافة حقل {column_name}: {e}")
        
        # تحديث التواريخ للعملاء الموجودين
        current_time = datetime.now().isoformat()
        cursor.execute('''
            UPDATE customer 
            SET created_at = ?, updated_at = ? 
            WHERE created_at IS NULL
        ''', (current_time, current_time))
        
        conn.commit()
        print("✅ تم تحديث جدول العملاء بنجاح")
        
        # عرض هيكل الجدول الجديد
        cursor.execute("PRAGMA table_info(customer)")
        columns = cursor.fetchall()
        
        print("\n📋 هيكل جدول العملاء الجديد:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False
    
    return True

def create_sample_customer():
    """إنشاء عميل تجريبي بالبيانات الجديدة"""
    
    try:
        from working_app import app, db, Customer
        
        with app.app_context():
            # التحقق من وجود عميل تجريبي
            sample_customer = Customer.query.filter_by(name_ar='أحمد محمد الكعبي').first()
            
            if not sample_customer:
                sample_customer = Customer(
                    name_ar='أحمد محمد الكعبي',
                    name_en='Ahmed Mohammed Al-Kaabi',
                    phone='+974 5555 1234',
                    email='<EMAIL>',
                    id_number='28512345678',
                    nationality='قطري',
                    address='الدوحة، منطقة الخليج الغربي، شارع الكورنيش',
                    city='الدوحة',
                    postal_code='12345',
                    profession='مهندس',
                    company='شركة قطر للبترول',
                    monthly_income=25000.0,
                    notes='عميل مميز - يفضل الأرقام المميزة'
                )
                
                db.session.add(sample_customer)
                db.session.commit()
                
                print("✅ تم إنشاء عميل تجريبي بالبيانات الجديدة")
            else:
                print("⚠️ العميل التجريبي موجود مسبقاً")
                
    except Exception as e:
        print(f"❌ خطأ في إنشاء العميل التجريبي: {e}")

def main():
    print("🚀 تحديث قاعدة البيانات - إضافة حقول العملاء الجديدة")
    print("=" * 60)
    
    # تحديث قاعدة البيانات
    if update_customer_table():
        print("\n🔄 إنشاء عميل تجريبي...")
        create_sample_customer()
        
        print("\n" + "=" * 60)
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        print("🎉 الآن يمكن إضافة معلومات مفصلة للعملاء في العقود")
        print("=" * 60)
    else:
        print("❌ فشل في تحديث قاعدة البيانات")

if __name__ == '__main__':
    main()
    input("اضغط Enter للخروج...")
