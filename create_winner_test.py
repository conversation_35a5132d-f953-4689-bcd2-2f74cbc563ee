#!/usr/bin/env python3
"""
إنشاء مزاد تجريبي مع فائز لاختبار العرض
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Auction, PremiumNumber, Bid, Customer
from datetime import datetime, timedelta

def create_winner_test():
    """إنشاء مزاد تجريبي مع فائز"""
    with app.app_context():
        try:
            # البحث عن رقم متاح
            number = PremiumNumber.query.filter_by(status='available').first()
            if not number:
                print("❌ لا توجد أرقام متاحة")
                return False

            # البحث عن عملاء
            customers = Customer.query.limit(3).all()
            if len(customers) < 2:
                print("❌ يجب وجود عميلين على الأقل")
                return False

            # إنشاء مزاد منتهي
            start_time = datetime.now() - timedelta(minutes=10)
            end_time = datetime.now() - timedelta(minutes=1)

            auction = Auction(
                premium_number_id=number.id,
                title=f"مزاد اختبار الفائز - {number.number}",
                description=f"مزاد تجريبي لاختبار عرض اسم الفائز",
                starting_price=number.price,
                current_price=number.price + 2000,
                bid_increment=500,
                start_time=start_time,
                end_time=end_time,
                status='ended',
                commission_rate=5.0,
                auto_extend=False,
                created_by=1,
                total_bids=3
            )

            db.session.add(auction)
            db.session.flush()

            # إضافة مزايدات
            bid1 = Bid(
                auction_id=auction.id,
                customer_id=customers[0].id,
                bid_amount=number.price,
                bid_time=start_time + timedelta(minutes=2)
            )
            
            bid2 = Bid(
                auction_id=auction.id,
                customer_id=customers[1].id,
                bid_amount=number.price + 500,
                bid_time=start_time + timedelta(minutes=5)
            )
            
            bid3 = Bid(
                auction_id=auction.id,
                customer_id=customers[0].id,
                bid_amount=number.price + 1000,
                bid_time=start_time + timedelta(minutes=7)
            )
            
            # المزايدة الرابحة
            winning_bid = Bid(
                auction_id=auction.id,
                customer_id=customers[1].id,
                bid_amount=number.price + 2000,
                bid_time=start_time + timedelta(minutes=9)
            )

            db.session.add_all([bid1, bid2, bid3, winning_bid])

            # تحديث حالة الرقم
            number.status = 'sold'
            
            # حساب العمولة
            auction.commission_amount = (auction.current_price * auction.commission_rate) / 100
            
            db.session.commit()

            winner = customers[1]
            
            print("✅ تم إنشاء مزاد اختبار بنجاح!")
            print(f"   📋 الرقم: {number.number}")
            print(f"   🏆 الفائز: {winner.name_ar}")
            print(f"   📞 الهاتف: {winner.phone}")
            print(f"   💰 المبلغ النهائي: {auction.current_price:,.0f} ر.ق")
            print(f"   💵 العمولة: {auction.commission_amount:,.0f} ر.ق")
            print(f"   📊 عدد المزايدات: 4")
            print(f"   🌐 تفاصيل المزاد: http://127.0.0.1:9898/auction/details/{auction.id}")
            print(f"   📺 العرض المستقل: http://127.0.0.1:9898/auction/standalone/{auction.id}")
            print(f"   🔧 إدارة المزادات: http://127.0.0.1:9898/auction_management")
            
            return auction.id

        except Exception as e:
            print(f"❌ خطأ في إنشاء المزاد: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🏆 إنشاء مزاد تجريبي مع فائز...")
    create_winner_test()
    input("\nاضغط Enter للخروج...")
