/**
 * أدوات التحكم في المزاد - ضبط الحجم والوقت
 */

class AuctionControls {
    constructor() {
        this.currentSize = 'medium';
        this.init();
    }

    init() {
        this.createControlPanel();
        this.bindEvents();
        this.loadSettings();
    }

    createControlPanel() {
        // إنشاء لوحة التحكم
        const controlPanel = document.createElement('div');
        controlPanel.id = 'auction-controls';
        controlPanel.className = 'auction-size-controls';
        controlPanel.innerHTML = `
            <div class="control-header">
                <h6><i class="fas fa-cog"></i> إعدادات العرض</h6>
            </div>
            <div class="control-section">
                <label>حجم العرض:</label>
                <div class="size-buttons">
                    <button class="size-btn" data-size="small">صغير</button>
                    <button class="size-btn active" data-size="medium">متوسط</button>
                    <button class="size-btn" data-size="large">كبير</button>
                    <button class="size-btn" data-size="fullscreen">ملء الشاشة</button>
                </div>
            </div>
            <div class="control-section">
                <label>سرعة التحديث:</label>
                <select id="refresh-rate" class="form-select form-select-sm">
                    <option value="1000">سريع (ثانية)</option>
                    <option value="2000" selected>عادي (ثانيتان)</option>
                    <option value="5000">بطيء (5 ثوان)</option>
                </select>
            </div>
            <div class="control-section">
                <button class="size-btn" id="toggle-sound">
                    <i class="fas fa-volume-up"></i> الصوت
                </button>
                <button class="size-btn" id="toggle-notifications">
                    <i class="fas fa-bell"></i> الإشعارات
                </button>
            </div>
            <div class="control-section">
                <button class="size-btn" id="minimize-controls">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        `;

        // إضافة اللوحة للصفحة
        document.body.appendChild(controlPanel);
    }

    bindEvents() {
        // أزرار تغيير الحجم
        document.querySelectorAll('.size-btn[data-size]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.changeSize(e.target.dataset.size);
            });
        });

        // تغيير سرعة التحديث
        const refreshRate = document.getElementById('refresh-rate');
        if (refreshRate) {
            refreshRate.addEventListener('change', (e) => {
                this.changeRefreshRate(parseInt(e.target.value));
            });
        }

        // تبديل الصوت
        const soundBtn = document.getElementById('toggle-sound');
        if (soundBtn) {
            soundBtn.addEventListener('click', () => {
                this.toggleSound();
            });
        }

        // تبديل الإشعارات
        const notificationBtn = document.getElementById('toggle-notifications');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.toggleNotifications();
            });
        }

        // تصغير اللوحة
        const minimizeBtn = document.getElementById('minimize-controls');
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', () => {
                this.toggleMinimize();
            });
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        this.changeSize('small');
                        break;
                    case '2':
                        e.preventDefault();
                        this.changeSize('medium');
                        break;
                    case '3':
                        e.preventDefault();
                        this.changeSize('large');
                        break;
                    case '4':
                        e.preventDefault();
                        this.changeSize('fullscreen');
                        break;
                }
            }
        });
    }

    changeSize(size) {
        // إزالة الفئات السابقة
        const container = document.querySelector('.container, .auction-container, body');
        if (container) {
            container.classList.remove('size-small', 'size-medium', 'size-large', 'size-fullscreen');
            container.classList.add(`size-${size}`);
        }

        // تحديث الأزرار
        document.querySelectorAll('.size-btn[data-size]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-size="${size}"]`).classList.add('active');

        this.currentSize = size;
        this.saveSettings();

        // إشعار التغيير
        this.showNotification(`تم تغيير حجم العرض إلى: ${this.getSizeText(size)}`);
    }

    changeRefreshRate(rate) {
        // تحديث معدل التحديث للمزاد
        if (window.auctionRefreshInterval) {
            clearInterval(window.auctionRefreshInterval);
        }

        window.auctionRefreshInterval = setInterval(() => {
            if (typeof updateAuctionData === 'function') {
                updateAuctionData();
            }
        }, rate);

        this.showNotification(`تم تغيير سرعة التحديث إلى: ${rate/1000} ثانية`);
        this.saveSettings();
    }

    toggleSound() {
        const soundEnabled = localStorage.getItem('auction-sound') !== 'false';
        localStorage.setItem('auction-sound', !soundEnabled);
        
        const btn = document.getElementById('toggle-sound');
        const icon = btn.querySelector('i');
        
        if (!soundEnabled) {
            icon.className = 'fas fa-volume-up';
            btn.classList.remove('btn-muted');
            this.showNotification('تم تفعيل الصوت');
        } else {
            icon.className = 'fas fa-volume-mute';
            btn.classList.add('btn-muted');
            this.showNotification('تم إيقاف الصوت');
        }
    }

    toggleNotifications() {
        const notificationsEnabled = localStorage.getItem('auction-notifications') !== 'false';
        localStorage.setItem('auction-notifications', !notificationsEnabled);
        
        const btn = document.getElementById('toggle-notifications');
        const icon = btn.querySelector('i');
        
        if (!notificationsEnabled) {
            icon.className = 'fas fa-bell';
            btn.classList.remove('btn-muted');
            this.showNotification('تم تفعيل الإشعارات');
        } else {
            icon.className = 'fas fa-bell-slash';
            btn.classList.add('btn-muted');
            this.showNotification('تم إيقاف الإشعارات');
        }
    }

    toggleMinimize() {
        const panel = document.getElementById('auction-controls');
        const isMinimized = panel.classList.contains('minimized');
        
        if (isMinimized) {
            panel.classList.remove('minimized');
            panel.style.height = 'auto';
        } else {
            panel.classList.add('minimized');
            panel.style.height = '40px';
        }
    }

    getSizeText(size) {
        const sizeTexts = {
            'small': 'صغير',
            'medium': 'متوسط',
            'large': 'كبير',
            'fullscreen': 'ملء الشاشة'
        };
        return sizeTexts[size] || 'متوسط';
    }

    showNotification(message) {
        // إنشاء إشعار مؤقت
        const notification = document.createElement('div');
        notification.className = 'auction-notification';
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
        `;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 9999;
            animation: slideDown 0.3s ease;
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.animation = 'slideUp 0.3s ease';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    saveSettings() {
        const settings = {
            size: this.currentSize,
            refreshRate: document.getElementById('refresh-rate')?.value || 2000,
            sound: localStorage.getItem('auction-sound') !== 'false',
            notifications: localStorage.getItem('auction-notifications') !== 'false'
        };
        localStorage.setItem('auction-settings', JSON.stringify(settings));
    }

    loadSettings() {
        try {
            const settings = JSON.parse(localStorage.getItem('auction-settings') || '{}');
            
            if (settings.size) {
                this.changeSize(settings.size);
            }
            
            if (settings.refreshRate) {
                const refreshSelect = document.getElementById('refresh-rate');
                if (refreshSelect) {
                    refreshSelect.value = settings.refreshRate;
                    this.changeRefreshRate(parseInt(settings.refreshRate));
                }
            }

            // تحديث حالة الأزرار
            if (settings.sound === false) {
                this.toggleSound();
            }
            
            if (settings.notifications === false) {
                this.toggleNotifications();
            }
        } catch (e) {
            console.log('لا توجد إعدادات محفوظة');
        }
    }
}

// تهيئة أدوات التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.auction-header, .auction-container, [data-auction-id]')) {
        new AuctionControls();
    }
});

// إضافة CSS للحركات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
    }
    
    @keyframes slideUp {
        from { transform: translateX(-50%) translateY(0); opacity: 1; }
        to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
    }
    
    .auction-size-controls.minimized .control-section:not(:last-child) {
        display: none;
    }
    
    .btn-muted {
        opacity: 0.5;
        background: #6c757d !important;
    }
`;
document.head.appendChild(style);
