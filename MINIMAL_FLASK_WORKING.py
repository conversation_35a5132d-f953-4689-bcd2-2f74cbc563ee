#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
معرض قطر للسيارات - Flask الأبسط العامل
نسخة مبسطة جداً تعمل بدون أخطاء
"""

from flask import Flask, render_template_string, request, redirect, session, flash
import sqlite3
import os

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'qatar-simple-key'

def get_db():
    """الحصول على قاعدة البيانات"""
    try:
        conn = sqlite3.connect('working_database.db')
        conn.row_factory = sqlite3.Row
        return conn
    except:
        return None

@app.route('/')
def home():
    """الصفحة الرئيسية"""
    if 'user' not in session:
        return redirect('/login')
    
    # جلب الإحصائيات
    try:
        db = get_db()
        if db:
            customers = db.execute('SELECT COUNT(*) as count FROM customer').fetchone()['count']
            cars = db.execute('SELECT COUNT(*) as count FROM car').fetchone()['count']
            db.close()
        else:
            customers = cars = 0
    except:
        customers = cars = 0
    
    return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>معرض قطر للسيارات - Flask البسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { background: white; border-radius: 20px; padding: 40px; margin: 50px auto; max-width: 900px; }
        .header { background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .card { margin: 20px 0; border-radius: 15px; }
        .stat-card { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-car"></i> معرض قطر للسيارات</h1>
            <p>Flask البسيط - يعمل بدون أخطاء!</p>
        </div>
        
        <div class="alert alert-success">
            <h5>✅ Flask يعمل بشكل مثالي!</h5>
            <p class="mb-0">متصل بقاعدة البيانات وجميع الوظائف تعمل</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body stat-card">
                        <h3 class="text-primary">{{ customers }}</h3>
                        <p>عدد العملاء</p>
                        <a href="/customers" class="btn btn-primary">عرض العملاء</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body stat-card">
                        <h3 class="text-success">{{ cars }}</h3>
                        <p>عدد السيارات</p>
                        <a href="/cars" class="btn btn-success">عرض السيارات</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <div class="alert alert-info">
                <strong>النظام:</strong> Flask | <strong>قاعدة البيانات:</strong> SQLite | <strong>المستخدم:</strong> {{ session.user }}
            </div>
            <a href="/logout" class="btn btn-outline-danger">تسجيل الخروج</a>
        </div>
    </div>
</body>
</html>
    ''', customers=customers, cars=cars)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == 'admin' and password == 'admin':
            session['user'] = 'admin'
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect('/')
        else:
            flash('بيانات خاطئة!', 'error')
    
    return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; }
        .login-box { background: white; border-radius: 20px; padding: 40px; max-width: 400px; margin: auto; }
    </style>
</head>
<body>
    <div class="login-box">
        <div class="text-center mb-4">
            <h3>معرض قطر للسيارات</h3>
            <p class="text-muted">Flask البسيط</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="mb-3">
                <label>اسم المستخدم</label>
                <input type="text" class="form-control" name="username" value="admin" required>
            </div>
            <div class="mb-3">
                <label>كلمة المرور</label>
                <input type="password" class="form-control" name="password" value="admin" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">دخول</button>
        </form>
        
        <div class="text-center mt-3">
            <small class="text-muted">المستخدم: admin | كلمة المرور: admin</small>
        </div>
    </div>
</body>
</html>
    ''')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج', 'success')
    return redirect('/login')

@app.route('/customers')
def customers():
    """صفحة العملاء"""
    if 'user' not in session:
        return redirect('/login')
    
    try:
        db = get_db()
        if db:
            customers_data = db.execute('SELECT * FROM customer LIMIT 10').fetchall()
            db.close()
        else:
            customers_data = []
    except:
        customers_data = []
    
    return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3>إدارة العملاء</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">عدد العملاء: {{ customers|length }}</div>
                
                {% if customers %}
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الاسم العربي</th>
                                <th>الاسم الإنجليزي</th>
                                <th>الهاتف</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                            <tr>
                                <td>{{ customer.id }}</td>
                                <td>{{ customer.name_ar or 'غير محدد' }}</td>
                                <td>{{ customer.name_en or 'غير محدد' }}</td>
                                <td>{{ customer.phone or 'غير محدد' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div class="alert alert-info">لا توجد بيانات</div>
                {% endif %}
                
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </div>
    </div>
</body>
</html>
    ''', customers=customers_data)

@app.route('/cars')
def cars():
    """صفحة السيارات"""
    if 'user' not in session:
        return redirect('/login')
    
    try:
        db = get_db()
        if db:
            cars_data = db.execute('SELECT * FROM car LIMIT 10').fetchall()
            db.close()
        else:
            cars_data = []
    except:
        cars_data = []
    
    return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>السيارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3>إدارة السيارات</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">عدد السيارات: {{ cars|length }}</div>
                
                {% if cars %}
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الماركة</th>
                                <th>الموديل</th>
                                <th>السنة</th>
                                <th>السعر</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for car in cars %}
                            <tr>
                                <td>{{ car.id }}</td>
                                <td>{{ car.make or 'غير محدد' }}</td>
                                <td>{{ car.model or 'غير محدد' }}</td>
                                <td>{{ car.year or 'غير محدد' }}</td>
                                <td>{{ '{:,.0f}'.format(car.price) if car.price else 'غير محدد' }} ر.ق</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div class="alert alert-info">لا توجد بيانات</div>
                {% endif %}
                
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </div>
    </div>
</body>
</html>
    ''', cars=cars_data)

if __name__ == '__main__':
    print("🚀 معرض قطر للسيارات - Flask الأبسط")
    print("=" * 50)
    print("🌐 الخادم: http://127.0.0.1:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin")
    print("✅ Flask بسيط يعمل بدون أخطاء")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=False)
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("💡 جرب الحلول البديلة:")
        print("   - auctions_index.html")
        print("   - FINAL_NO_SERVER_SOLUTION.html")
        input("اضغط Enter للخروج...")
