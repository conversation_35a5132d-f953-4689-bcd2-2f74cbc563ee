#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لمشكلة عرض اسم الفائز
معرض قطر للسيارات - نظام المزادات
"""

from working_app import *
from datetime import datetime, timedelta
import random

def create_test_data():
    """إنشاء بيانات تجريبية مع فائز واضح"""
    with app.app_context():
        print("🔧 إنشاء بيانات تجريبية...")
        
        # إنشاء عملاء تجريبيين
        customers_data = [
            {"name_ar": "أحمد محمد الكعبي", "phone": "+974 5555 1234", "email": "<EMAIL>"},
            {"name_ar": "سارة أحمد النعيمي", "phone": "+974 5555 5678", "email": "<EMAIL>"},
            {"name_ar": "محمد علي الثاني", "phone": "+974 5555 9012", "email": "<EMAIL>"},
            {"name_ar": "فاطمة حسن المري", "phone": "+974 5555 3456", "email": "<EMAIL>"},
            {"name_ar": "عبدالله سالم الكواري", "phone": "+974 5555 7890", "email": "<EMAIL>"}
        ]
        
        # حذف العملاء الموجودين
        Customer.query.delete()
        
        # إنشاء عملاء جدد
        customers = []
        for customer_data in customers_data:
            customer = Customer(**customer_data)
            db.session.add(customer)
            customers.append(customer)
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(customers)} عميل")
        
        # إنشاء رقم مميز تجريبي
        premium_number = PremiumNumber.query.filter_by(number="777777").first()
        if not premium_number:
            premium_number = PremiumNumber(
                number="777777",
                category="VIP",
                price=75000,
                status="auction"
            )
            db.session.add(premium_number)
            db.session.commit()
        
        # إنشاء مزاد منتهي مع فائز
        auction = Auction.query.filter_by(premium_number_id=premium_number.id).first()
        if auction:
            # حذف المزايدات القديمة
            Bid.query.filter_by(auction_id=auction.id).delete()
            db.session.delete(auction)
            db.session.commit()
        
        # إنشاء مزاد جديد منتهي
        end_time = datetime.now() - timedelta(hours=1)  # انتهى منذ ساعة
        start_time = end_time - timedelta(hours=2)  # استمر ساعتين
        
        auction = Auction(
            title=f"مزاد الرقم المميز {premium_number.number}",
            description="مزاد تجريبي لاختبار عرض الفائز",
            premium_number_id=premium_number.id,
            starting_price=50000,
            current_price=78850,  # السعر النهائي
            reserve_price=60000,
            bid_increment=1000,
            start_time=start_time,
            end_time=end_time,
            status="ended",  # مزاد منتهي
            auto_extend=False,
            commission_rate=5.0,
            commission_amount=3942.5,  # 5% من 78850
            total_bids=8
        )
        db.session.add(auction)
        db.session.commit()
        
        print(f"✅ تم إنشاء مزاد منتهي: {auction.title}")
        
        # إنشاء مزايدات تجريبية
        bid_amounts = [50000, 52000, 55000, 58000, 62000, 68000, 72000, 78850]
        bid_times = []
        
        # إنشاء أوقات مزايدات متدرجة
        for i, amount in enumerate(bid_amounts):
            bid_time = start_time + timedelta(minutes=15 * i)
            bid_times.append(bid_time)
        
        # إنشاء المزايدات
        for i, (amount, bid_time) in enumerate(zip(bid_amounts, bid_times)):
            customer = customers[i % len(customers)]  # توزيع المزايدات على العملاء
            
            bid = Bid(
                auction_id=auction.id,
                customer_id=customer.id,
                bid_amount=amount,
                bid_time=bid_time
            )
            db.session.add(bid)
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(bid_amounts)} مزايدة")
        
        # تحديث الفائز
        winning_bid = Bid.query.filter_by(auction_id=auction.id).order_by(Bid.bid_amount.desc()).first()
        if winning_bid:
            auction.winner = winning_bid.customer.name_ar
            auction.winner_id = winning_bid.customer_id
            db.session.commit()
            print(f"✅ الفائز: {winning_bid.customer.name_ar} بمبلغ {winning_bid.bid_amount:,.0f} ر.ق")
        
        return auction.id

def test_winner_display():
    """اختبار عرض الفائز في جميع الصفحات"""
    with app.app_context():
        print("\n🧪 اختبار عرض الفائز...")
        
        # اختبار العرض المستقل
        auction = Auction.query.filter_by(status="ended").first()
        if not auction:
            print("❌ لا يوجد مزاد منتهي للاختبار")
            return
        
        print(f"📋 اختبار المزاد: {auction.title}")
        print(f"   الحالة: {auction.status}")
        print(f"   السعر النهائي: {auction.current_price:,.0f} ر.ق")
        
        # اختبار جلب المزايدات مع العملاء
        latest_bids = Bid.query.join(Customer).filter(
            Bid.auction_id == auction.id
        ).order_by(Bid.bid_time.desc()).limit(10).all()
        
        print(f"   عدد المزايدات المجلبة: {len(latest_bids)}")
        
        if latest_bids:
            winner = latest_bids[0]
            print(f"   الفائز: {winner.customer.name_ar}")
            print(f"   مبلغ الفوز: {winner.bid_amount:,.0f} ر.ق")
            print(f"   وقت الفوز: {winner.bid_time}")
        else:
            print("   ❌ لا توجد مزايدات!")
        
        # اختبار جميع المزايدات
        all_bids = Bid.query.filter_by(auction_id=auction.id).order_by(Bid.bid_amount.desc()).all()
        print(f"\n📊 جميع المزايدات ({len(all_bids)}):")
        for i, bid in enumerate(all_bids, 1):
            customer = Customer.query.get(bid.customer_id)
            print(f"   {i}. {customer.name_ar}: {bid.bid_amount:,.0f} ر.ق")

def fix_database_relations():
    """إصلاح العلاقات في قاعدة البيانات"""
    with app.app_context():
        print("\n🔧 إصلاح العلاقات في قاعدة البيانات...")
        
        # التحقق من جميع المزايدات
        orphaned_bids = db.session.query(Bid).outerjoin(Customer).filter(Customer.id == None).all()
        if orphaned_bids:
            print(f"⚠️ وجدت {len(orphaned_bids)} مزايدة بدون عميل")
            for bid in orphaned_bids:
                db.session.delete(bid)
            db.session.commit()
            print("✅ تم حذف المزايدات المعطلة")
        
        # التحقق من المزادات المنتهية
        ended_auctions = Auction.query.filter_by(status="ended").all()
        print(f"📋 المزادات المنتهية: {len(ended_auctions)}")
        
        for auction in ended_auctions:
            # البحث عن الفائز
            winning_bid = Bid.query.filter_by(auction_id=auction.id).order_by(Bid.bid_amount.desc()).first()
            if winning_bid:
                customer = Customer.query.get(winning_bid.customer_id)
                if customer:
                    auction.winner = customer.name_ar
                    auction.winner_id = customer.id
                    print(f"✅ تم تحديث فائز المزاد {auction.id}: {customer.name_ar}")
                else:
                    print(f"❌ المزاد {auction.id}: العميل غير موجود")
            else:
                print(f"⚠️ المزاد {auction.id}: لا توجد مزايدات")
        
        db.session.commit()

def main():
    """الدالة الرئيسية لإصلاح مشكلة عرض الفائز"""
    print("🏆 إصلاح شامل لمشكلة عرض اسم الفائز")
    print("=" * 50)
    
    try:
        # إصلاح العلاقات
        fix_database_relations()
        
        # إنشاء بيانات تجريبية
        auction_id = create_test_data()
        
        # اختبار العرض
        test_winner_display()
        
        print("\n" + "=" * 50)
        print("✅ تم إصلاح مشكلة عرض الفائز بنجاح!")
        print(f"🌐 اختبر النتيجة: http://127.0.0.1:9898/auction/standalone/{auction_id}")
        print(f"📋 إدارة المزادات: http://127.0.0.1:9898/auction_management")
        print(f"📄 تفاصيل المزاد: http://127.0.0.1:9898/auction/details/{auction_id}")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
