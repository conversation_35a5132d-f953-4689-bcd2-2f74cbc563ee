# 🇶🇦 نظام مزادات الأرقام المميزة القطرية

## 📋 وصف المشروع
نظام متكامل لإدارة مزادات الأرقام المميزة للسيارات في قطر مع واجهة عربية كاملة ولوحات سيارات واقعية.

## 🚀 طريقة التشغيل السريع
1. **انقر نقرة مزدوجة على**: `START_QATAR_AUCTION.bat`
2. **أو شغل الأمر**: `python working_app.py`
3. **افتح المتصفح على**: http://127.0.0.1:9898

## 🔐 بيانات الدخول
- **المستخدم**: admin
- **كلمة المرور**: admin123

## ✨ الميزات الرئيسية

### 🎯 إدارة المزادات
- إنشاء مزادات جديدة للأرقام المميزة
- مزايدة مباشرة وفورية
- عداد تنازلي للمزادات
- تمديد تلقائي للمزادات
- تتبع المزايدات والفائزين

### 🔢 إدارة الأرقام المميزة
- إضافة أرقام مميزة جديدة
- تصنيف الأرقام (VIP, Premium, Special)
- لوحات سيارات قطرية واقعية
- عرض الأرقام بتصميم أنيق

### 🚗 إدارة السيارات
- إضافة وتعديل السيارات
- بيع السيارات مع العقود
- إدارة العملاء والمبيعات
- تقارير مفصلة

### 📄 نظام العقود
- إنشاء عقود تلقائية
- تصدير العقود بصيغة Word و PDF
- قوالب عقود احترافية
- طباعة مباشرة

## 📁 الملفات المهمة

### 🎯 ملفات التشغيل
- `working_app.py` - الملف الرئيسي للنظام
- `START_QATAR_AUCTION.bat` - تشغيل سريع
- `working_database.db` - قاعدة البيانات

### 📊 ملفات التحديث
- `update_auction_database.py` - تحديث قاعدة بيانات المزادات
- `update_car_database.py` - تحديث قاعدة بيانات السيارات
- `update_contracts_database.py` - تحديث قاعدة بيانات العقود

### 🧪 ملفات الاختبار
- `test_basic.py` - اختبارات أساسية
- `SIMPLE_TEST.py` - اختبار مبسط

## 🎨 تصميم لوحات السيارات
النظام يعرض الأرقام المميزة على لوحات سيارات قطرية واقعية مع:
- خلفية بيضاء نقية
- حدود سوداء واضحة
- أرقام كبيرة في الوسط
- تصميم نظيف ومبسط

## 🔧 المتطلبات التقنية
- Python 3.7+
- Flask
- SQLAlchemy
- python-docx
- reportlab

## 📞 الدعم الفني
للمساعدة أو الاستفسارات، يرجى مراجعة ملفات التوثيق المرفقة.

---
**تم تطوير النظام خصيصاً لمزادات الأرقام المميزة في قطر** 🇶🇦
