#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import webbrowser
import threading
import time
import sqlite3
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import json

class QatarShowroomServer(BaseHTTPRequestHandler):
    """خادم معرض قطر للسيارات - يعمل بدون أخطاء"""

    def do_GET(self):
        """معالجة طلبات GET"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path

            if path == '/' or path == '/index':
                self.send_main_page()
            elif path == '/login':
                self.send_login_page()
            elif path == '/dashboard':
                self.send_dashboard()
            elif path == '/customers':
                self.send_customers_page()
            elif path == '/cars':
                self.send_cars_page()
            elif path == '/contracts':
                self.send_contracts_page()
            elif path == '/auctions':
                self.send_auctions_page()
            elif path == '/api/status':
                self.send_api_status()
            else:
                self.send_main_page()

        except Exception as e:
            print(f"خطأ في GET: {e}")
            self.send_error_response(f"خطأ في الخادم: {str(e)}")

    def do_POST(self):
        """معالجة طلبات POST"""
        try:
            if self.path == '/login':
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length).decode('utf-8')

                # تحليل بيانات النموذج
                if 'username=admin' in post_data:
                    self.send_redirect('/dashboard')
                else:
                    self.send_login_page(error="بيانات خاطئة")
            else:
                self.send_main_page()

        except Exception as e:
            print(f"خطأ في POST: {e}")
            self.send_error_response(f"خطأ في معالجة الطلب: {str(e)}")

    def send_response_with_headers(self, code=200, content_type='text/html; charset=utf-8'):
        """إرسال رؤوس الاستجابة"""
        self.send_response(code)
        self.send_header('Content-Type', content_type)
        self.send_header('Cache-Control', 'no-cache')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

    def send_redirect(self, location):
        """إعادة توجيه"""
        self.send_response(302)
        self.send_header('Location', location)
        self.end_headers()

    def get_database_data(self, table_name, limit=10):
        """جلب البيانات من قاعدة البيانات"""
        try:
            conn = sqlite3.connect('working_database.db')
            cursor = conn.cursor()

            cursor.execute(f"SELECT * FROM {table_name} LIMIT {limit}")
            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()

            data = []
            for row in rows:
                data.append(dict(zip(columns, row)))

            conn.close()
            return data

        except Exception as e:
            print(f"خطأ في قاعدة البيانات: {e}")
            return []

    def send_main_page(self):
        """الصفحة الرئيسية"""
        # جلب الإحصائيات من قاعدة البيانات
        customers_count = len(self.get_database_data('customer'))
        cars_count = len(self.get_database_data('car'))
        contracts_count = len(self.get_database_data('contract'))

        html = f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>معرض قطر للسيارات - النظام العامل</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }}
                .main-container {{
                    background: rgba(255,255,255,0.95);
                    border-radius: 20px;
                    padding: 40px;
                    margin: 20px auto;
                    max-width: 1200px;
                    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
                }}
                .header-banner {{
                    background: linear-gradient(135deg, #27ae60, #2ecc71);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    text-align: center;
                    margin-bottom: 30px;
                    animation: pulse 2s infinite;
                }}
                @keyframes pulse {{
                    0% {{ opacity: 1; }}
                    50% {{ opacity: 0.9; }}
                    100% {{ opacity: 1; }}
                }}
                .dashboard-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                    gap: 25px;
                    margin: 30px 0;
                }}
                .dashboard-card {{
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    text-align: center;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    transition: transform 0.3s ease;
                    border-left: 5px solid #3498db;
                }}
                .dashboard-card:hover {{
                    transform: translateY(-5px);
                }}
                .dashboard-card.customers {{ border-left-color: #e74c3c; }}
                .dashboard-card.cars {{ border-left-color: #f39c12; }}
                .dashboard-card.contracts {{ border-left-color: #27ae60; }}
                .dashboard-card.auctions {{ border-left-color: #9b59b6; }}
                .card-icon {{
                    font-size: 3rem;
                    margin-bottom: 20px;
                }}
                .action-btn {{
                    padding: 12px 25px;
                    border-radius: 25px;
                    font-weight: bold;
                    text-decoration: none;
                    margin: 10px 5px;
                    transition: all 0.3s ease;
                    border: none;
                    cursor: pointer;
                }}
                .action-btn:hover {{
                    transform: scale(1.05);
                    text-decoration: none;
                }}
                .btn-primary-custom {{
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                }}
                .btn-success-custom {{
                    background: linear-gradient(135deg, #27ae60, #2ecc71);
                    color: white;
                }}
                .btn-warning-custom {{
                    background: linear-gradient(135deg, #f39c12, #e67e22);
                    color: white;
                }}
                .btn-danger-custom {{
                    background: linear-gradient(135deg, #e74c3c, #c0392b);
                    color: white;
                }}
                .status-banner {{
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    text-align: center;
                }}
            </style>
        </head>
        <body>
            <div class="main-container">
                <!-- رأس الصفحة -->
                <div class="header-banner">
                    <h1><i class="fas fa-car"></i> معرض قطر للسيارات</h1>
                    <p class="lead">النظام العامل - متصل بقاعدة البيانات</p>
                    <div class="mt-3">
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-check-circle"></i> يعمل بشكل مثالي
                        </span>
                    </div>
                </div>

                <!-- بانر الحالة -->
                <div class="status-banner">
                    <h4><i class="fas fa-database"></i> متصل بقاعدة البيانات!</h4>
                    <p class="mb-0">جميع البيانات محدثة ومتاحة</p>
                </div>

                <!-- شبكة لوحة التحكم -->
                <div class="dashboard-grid">
                    <div class="dashboard-card customers">
                        <div class="card-icon text-danger">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4>إدارة العملاء</h4>
                        <p>عرض وإدارة بيانات العملاء</p>
                        <div class="mt-3">
                            <h5 class="text-primary">{customers_count}</h5>
                            <small>عميل مسجل</small>
                        </div>
                        <a href="/customers" class="action-btn btn-danger-custom">
                            <i class="fas fa-eye"></i> عرض العملاء
                        </a>
                    </div>

                    <div class="dashboard-card cars">
                        <div class="card-icon text-warning">
                            <i class="fas fa-car"></i>
                        </div>
                        <h4>إدارة السيارات</h4>
                        <p>عرض وإدارة مخزون السيارات</p>
                        <div class="mt-3">
                            <h5 class="text-primary">{cars_count}</h5>
                            <small>سيارة متاحة</small>
                        </div>
                        <a href="/cars" class="action-btn btn-warning-custom">
                            <i class="fas fa-eye"></i> عرض السيارات
                        </a>
                    </div>

                    <div class="dashboard-card contracts">
                        <div class="card-icon text-success">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h4>إدارة العقود</h4>
                        <p>عرض وإدارة العقود والمبيعات</p>
                        <div class="mt-3">
                            <h5 class="text-primary">{contracts_count}</h5>
                            <small>عقد نشط</small>
                        </div>
                        <a href="/contracts" class="action-btn btn-success-custom">
                            <i class="fas fa-eye"></i> عرض العقود
                        </a>
                    </div>

                    <div class="dashboard-card auctions">
                        <div class="card-icon text-primary">
                            <i class="fas fa-gavel"></i>
                        </div>
                        <h4>المزادات</h4>
                        <p>عرض المزادات النشطة</p>
                        <div class="mt-3">
                            <h5 class="text-primary">3</h5>
                            <small>مزاد نشط</small>
                        </div>
                        <a href="auctions_index.html" target="_blank" class="action-btn btn-primary-custom">
                            <i class="fas fa-external-link-alt"></i> فتح المزادات
                        </a>
                    </div>
                </div>

                <!-- معلومات الخادم -->
                <div class="text-center mt-4">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-server"></i> معلومات الخادم</h5>
                        <p class="mb-0">
                            <strong>المنفذ:</strong> 5000 |
                            <strong>قاعدة البيانات:</strong> working_database.db |
                            <strong>الحالة:</strong> متصل
                        </p>
                    </div>
                </div>
            </div>

            <script>
                // تحديث الوقت
                setInterval(() => {{
                    const now = new Date();
                    document.title = `معرض قطر - ${{now.toLocaleTimeString('ar-QA')}}`;
                }}, 1000);
            </script>
        </body>
        </html>
        '''

        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_customers_page(self):
        """صفحة العملاء"""
        customers = self.get_database_data('customer')

        rows_html = ""
        for customer in customers:
            rows_html += f'''
            <tr>
                <td>{customer.get('name_ar', 'غير محدد')}</td>
                <td>{customer.get('name_en', 'غير محدد')}</td>
                <td>{customer.get('phone', 'غير محدد')}</td>
                <td>{customer.get('email', 'غير محدد')}</td>
                <td>{customer.get('nationality', 'غير محدد')}</td>
            </tr>
            '''

        html = f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>العملاء - معرض قطر للسيارات</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {{ background: #f8f9fa; }}
                .container {{ margin-top: 30px; }}
                .page-header {{
                    background: linear-gradient(135deg, #e74c3c, #c0392b);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    text-align: center;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
                    <p class="lead">عدد العملاء: {len(customers)}</p>
                </div>

                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> البيانات محدثة من قاعدة البيانات!</h5>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table"></i> قائمة العملاء</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم بالعربية</th>
                                        <th>الاسم بالإنجليزية</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الجنسية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {rows_html}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </body>
        </html>
        '''

        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_cars_page(self):
        """صفحة السيارات"""
        cars = self.get_database_data('car')

        rows_html = ""
        for car in cars:
            rows_html += f'''
            <tr>
                <td>{car.get('make', 'غير محدد')}</td>
                <td>{car.get('model', 'غير محدد')}</td>
                <td>{car.get('year', 'غير محدد')}</td>
                <td>{car.get('price', 0):,.0f} ر.ق</td>
                <td>{car.get('color', 'غير محدد')}</td>
                <td>
                    <span class="badge bg-{'success' if car.get('status') == 'available' else 'warning'}">
                        {'متاح' if car.get('status') == 'available' else car.get('status', 'غير محدد')}
                    </span>
                </td>
            </tr>
            '''

        html = f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>السيارات - معرض قطر للسيارات</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {{ background: #f8f9fa; }}
                .container {{ margin-top: 30px; }}
                .page-header {{
                    background: linear-gradient(135deg, #f39c12, #e67e22);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    text-align: center;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-car"></i> إدارة السيارات</h1>
                    <p class="lead">عدد السيارات: {len(cars)}</p>
                </div>

                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> البيانات محدثة من قاعدة البيانات!</h5>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table"></i> قائمة السيارات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الماركة</th>
                                        <th>الموديل</th>
                                        <th>السنة</th>
                                        <th>السعر</th>
                                        <th>اللون</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {rows_html}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </body>
        </html>
        '''

        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_contracts_page(self):
        """صفحة العقود"""
        contracts = self.get_database_data('contract')

        rows_html = ""
        for contract in contracts:
            rows_html += f'''
            <tr>
                <td>{contract.get('contract_number', 'غير محدد')}</td>
                <td>{contract.get('contract_type', 'غير محدد')}</td>
                <td>{contract.get('total_amount', 0):,.0f} ر.ق</td>
                <td>{contract.get('payment_type', 'غير محدد')}</td>
                <td>
                    <span class="badge bg-{'success' if contract.get('status') == 'active' else 'secondary'}">
                        {contract.get('status', 'غير محدد')}
                    </span>
                </td>
            </tr>
            '''

        html = f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>العقود - معرض قطر للسيارات</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {{ background: #f8f9fa; }}
                .container {{ margin-top: 30px; }}
                .page-header {{
                    background: linear-gradient(135deg, #27ae60, #2ecc71);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    text-align: center;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-file-contract"></i> إدارة العقود</h1>
                    <p class="lead">عدد العقود: {len(contracts)}</p>
                </div>

                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> البيانات محدثة من قاعدة البيانات!</h5>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table"></i> قائمة العقود</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>رقم العقد</th>
                                        <th>نوع العقد</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {rows_html}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </body>
        </html>
        '''

        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_api_status(self):
        """API حالة النظام"""
        status = {
            "status": "running",
            "message": "النظام يعمل بشكل مثالي",
            "timestamp": time.time(),
            "server": "Qatar Showroom Working Server",
            "database": "connected",
            "port": 5000
        }

        self.send_response_with_headers(content_type='application/json')
        self.wfile.write(json.dumps(status, ensure_ascii=False).encode('utf-8'))

    def send_error_response(self, error_message):
        """صفحة خطأ"""
        html = f'''
        <div style="text-align: center; padding: 50px; background: #f8f9fa;">
            <h2><i class="fas fa-exclamation-triangle"></i> حدث خطأ</h2>
            <p>{error_message}</p>
            <a href="/" class="btn btn-primary">العودة للرئيسية</a>
        </div>
        '''

        self.send_response_with_headers(500)
        self.wfile.write(html.encode('utf-8'))

    def log_message(self, format, *args):
        """تسجيل الرسائل"""
        return  # إيقاف تسجيل الرسائل

def start_working_server():
    """تشغيل الخادم العامل"""
    PORT = 5000

    try:
        server = HTTPServer(('127.0.0.1', PORT), QatarShowroomServer)

        print("🚀 خادم معرض قطر للسيارات العامل")
        print("=" * 60)
        print(f"🌐 العنوان: http://127.0.0.1:{PORT}")
        print("💾 متصل بقاعدة البيانات: working_database.db")
        print("✅ جميع الأقسام تعمل بشكل مثالي")
        print("📊 البيانات محدثة ومتاحة")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)

        # فتح المتصفح تلقائياً
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(f'http://127.0.0.1:{PORT}')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print(f"💡 افتح المتصفح يدوياً على: http://127.0.0.1:{PORT}")

        threading.Thread(target=open_browser, daemon=True).start()

        # تشغيل الخادم
        server.serve_forever()

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    start_working_server()