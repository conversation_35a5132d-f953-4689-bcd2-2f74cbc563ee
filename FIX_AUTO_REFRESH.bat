@echo off
chcp 65001 > nul
title إصلاح واختبار التحديث التلقائي

echo.
echo ========================================
echo   إصلاح واختبار التحديث التلقائي
echo   العرض المستقل - كل 3 ثوان
echo ========================================
echo.

cd /d "%~dp0"

echo 🔧 إنشاء مزاد اختبار...
python test_auto_refresh.py

echo.
echo 🚀 تشغيل النظام...
start /B python working_app.py

echo ⏳ انتظار تشغيل الخادم...
timeout /t 4 /nobreak > nul

echo 🌐 فتح العرض المستقل للاختبار...
start http://127.0.0.1:9898/auction/standalone/1

echo.
echo ✅ تم تشغيل اختبار التحديث التلقائي!
echo.
echo 🔍 كيفية الاختبار:
echo    1. راقب المؤشر في أعلى يمين الصفحة
echo    2. تأكد من وجود العداد التنازلي (3s, 2s, 1s)
echo    3. راقب تحديث الصفحة كل 3 ثوان
echo    4. جرب تغيير السرعة من أيقونة الإعدادات
echo.
echo 🛠️ إذا لم يعمل التحديث:
echo    - تأكد من أن المزاد نشط (active)
echo    - افتح Developer Tools (F12)
echo    - راقب رسائل Console للأخطاء
echo    - جرب إعادة تحميل الصفحة (F5)
echo.
echo 💡 نصائح:
echo    - العداد التنازلي يجب أن يظهر بجانب النص
echo    - اللون يتغير للأحمر في آخر 3 ثوان
echo    - يظهر "تحديث..." عند التحديث الفعلي
echo.
echo ⏹️ اضغط أي مفتاح لإيقاف النظام...
pause > nul

echo 🛑 إيقاف النظام...
taskkill /f /im python.exe > nul 2>&1
echo ✅ تم إيقاف النظام
