#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def fix_database_structure():
    """إصلاح هيكل قاعدة البيانات لجميع الجداول"""
    
    db_files = ['auction.db', 'working_database.db', 'instance/auction.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"🔧 إصلاح {db_file}...")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # إصلاح جدول العملاء
                print("📋 إصلاح جدول العملاء...")
                customer_columns = [
                    ('name_en', 'TEXT'),
                    ('id_number', 'TEXT'),
                    ('nationality', 'TEXT DEFAULT "قطري"'),
                    ('birth_date', 'DATE'),
                    ('address', 'TEXT'),
                    ('city', 'TEXT DEFAULT "الدوحة"'),
                    ('postal_code', 'TEXT'),
                    ('profession', 'TEXT'),
                    ('company', 'TEXT'),
                    ('monthly_income', 'FLOAT DEFAULT 0'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('notes', 'TEXT')
                ]
                
                for column_name, column_type in customer_columns:
                    try:
                        cursor.execute(f"ALTER TABLE customer ADD COLUMN {column_name} {column_type}")
                        print(f"✅ تم إضافة العمود: customer.{column_name}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e):
                            print(f"⚠️ خطأ في customer.{column_name}: {e}")
                
                # تحديث البيانات الموجودة للعملاء
                cursor.execute("UPDATE customer SET name_en = 'Ahmed Mohammed Al-Kaabi' WHERE name_en IS NULL OR name_en = ''")
                cursor.execute("UPDATE customer SET nationality = 'قطري' WHERE nationality IS NULL OR nationality = ''")
                cursor.execute("UPDATE customer SET city = 'الدوحة' WHERE city IS NULL OR city = ''")
                
                # إصلاح جدول العقود
                print("📋 إصلاح جدول العقود...")
                contract_columns = [
                    ('contract_type', 'TEXT DEFAULT "car_sale"'),
                    ('payment_method', 'TEXT DEFAULT "cash"'),
                    ('down_payment', 'FLOAT DEFAULT 0'),
                    ('monthly_payment', 'FLOAT DEFAULT 0'),
                    ('installment_months', 'INTEGER DEFAULT 0'),
                    ('interest_rate', 'FLOAT DEFAULT 0'),
                    ('total_amount', 'FLOAT DEFAULT 0'),
                    ('remaining_amount', 'FLOAT DEFAULT 0'),
                    ('contract_date', 'DATE DEFAULT CURRENT_DATE'),
                    ('delivery_date', 'DATE'),
                    ('warranty_period', 'INTEGER DEFAULT 12'),
                    ('notes', 'TEXT'),
                    ('status', 'TEXT DEFAULT "active"'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ]
                
                for column_name, column_type in contract_columns:
                    try:
                        cursor.execute(f"ALTER TABLE contract ADD COLUMN {column_name} {column_type}")
                        print(f"✅ تم إضافة العمود: contract.{column_name}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e):
                            print(f"⚠️ خطأ في contract.{column_name}: {e}")
                
                # إصلاح جدول المزادات
                print("📋 إصلاح جدول المزادات...")
                auction_columns = [
                    ('commission_rate', 'FLOAT DEFAULT 5.0'),
                    ('commission_amount', 'FLOAT DEFAULT 0'),
                    ('auto_extend', 'BOOLEAN DEFAULT 0'),
                    ('extend_time', 'INTEGER DEFAULT 300'),
                    ('views', 'INTEGER DEFAULT 0'),
                    ('total_bids', 'INTEGER DEFAULT 0'),
                    ('created_by', 'INTEGER'),
                    ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP')
                ]
                
                for column_name, column_type in auction_columns:
                    try:
                        cursor.execute(f"ALTER TABLE auction ADD COLUMN {column_name} {column_type}")
                        print(f"✅ تم إضافة العمود: auction.{column_name}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e):
                            print(f"⚠️ خطأ في auction.{column_name}: {e}")
                
                # إنشاء بيانات تجريبية للعملاء إذا لم تكن موجودة
                cursor.execute("SELECT COUNT(*) FROM customer")
                customer_count = cursor.fetchone()[0]
                
                if customer_count == 0:
                    print("📝 إنشاء بيانات تجريبية للعملاء...")
                    sample_customers = [
                        ('أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '12345678901', '50123456', '<EMAIL>'),
                        ('فاطمة علي الأنصاري', 'Fatima Ali Al-Ansari', '12345678902', '50123457', '<EMAIL>'),
                        ('محمد سالم المري', 'Mohammed Salem Al-Marri', '12345678903', '50123458', '<EMAIL>'),
                        ('نورا خالد الثاني', 'Nora Khalid Al-Thani', '12345678904', '50123459', '<EMAIL>'),
                        ('عبدالله أحمد الكواري', 'Abdullah Ahmed Al-Kuwari', '12345678905', '50123460', '<EMAIL>')
                    ]
                    
                    for name_ar, name_en, id_number, phone, email in sample_customers:
                        cursor.execute('''
                            INSERT INTO customer (name_ar, name_en, id_number, phone, email, nationality, city)
                            VALUES (?, ?, ?, ?, ?, 'قطري', 'الدوحة')
                        ''', (name_ar, name_en, id_number, phone, email))
                    
                    print(f"✅ تم إنشاء {len(sample_customers)} عميل تجريبي")
                
                # إنشاء بيانات تجريبية للعقود إذا لم تكن موجودة
                cursor.execute("SELECT COUNT(*) FROM contract")
                contract_count = cursor.fetchone()[0]
                
                if contract_count == 0:
                    print("📝 إنشاء بيانات تجريبية للعقود...")
                    # الحصول على معرفات العملاء
                    cursor.execute("SELECT id FROM customer LIMIT 3")
                    customer_ids = [row[0] for row in cursor.fetchall()]
                    
                    if customer_ids:
                        sample_contracts = [
                            (customer_ids[0], 'car_sale', 'cash', 150000, 'تويوتا كامري 2023'),
                            (customer_ids[1] if len(customer_ids) > 1 else customer_ids[0], 'premium_number', 'installment', 300000, 'رقم مميز 777'),
                            (customer_ids[2] if len(customer_ids) > 2 else customer_ids[0], 'car_sale', 'installment', 200000, 'نيسان التيما 2023')
                        ]
                        
                        for customer_id, contract_type, payment_method, total_amount, description in sample_contracts:
                            cursor.execute('''
                                INSERT INTO contract (customer_id, contract_type, payment_method, total_amount, description, status)
                                VALUES (?, ?, ?, ?, ?, 'active')
                            ''', (customer_id, contract_type, payment_method, total_amount, description))
                        
                        print(f"✅ تم إنشاء {len(sample_contracts)} عقد تجريبي")
                
                conn.commit()
                conn.close()
                print(f"✅ تم إصلاح {db_file} بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في {db_file}: {e}")

def start_server():
    """تشغيل الخادم بعد الإصلاح"""
    print("\n🚀 تشغيل الخادم...")
    try:
        os.system("python working_app.py")
    except:
        print("❌ فشل في تشغيل الخادم")
        print("💡 جرب تشغيل: python working_app.py")

if __name__ == '__main__':
    print("🔧 إصلاح شامل لجميع أقسام النظام")
    print("=" * 50)
    
    # إصلاح قاعدة البيانات
    fix_database_structure()
    
    print("=" * 50)
    print("✅ تم الانتهاء من الإصلاح")
    print("🎯 الأقسام المصلحة:")
    print("   ✓ المزادات")
    print("   ✓ العملاء") 
    print("   ✓ العقود")
    print("   ✓ قاعدة البيانات")
    print("=" * 50)
    
    # تشغيل الخادم
    input("اضغط Enter لتشغيل الخادم...")
    start_server()
