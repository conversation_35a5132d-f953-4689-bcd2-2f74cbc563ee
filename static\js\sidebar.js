// Sidebar JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar
    initSidebar();
    
    // Set active menu item based on current page
    setActiveMenuItem();
    
    // Load notifications count
    loadNotificationsCount();
});

function initSidebar() {
    const sidebar = document.getElementById('sidebar');
    const body = document.body;
    
    // Check if sidebar should be collapsed from localStorage
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    
    if (isCollapsed) {
        sidebar.classList.add('collapsed');
        body.classList.add('sidebar-collapsed');
    } else {
        body.classList.add('sidebar-open');
    }
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const body = document.body;

    if (sidebar.classList.contains('collapsed')) {
        // Expand sidebar
        sidebar.classList.remove('collapsed');
        body.classList.remove('sidebar-collapsed');
        body.classList.add('sidebar-open');
        localStorage.setItem('sidebarCollapsed', 'false');
    } else {
        // Collapse sidebar
        sidebar.classList.add('collapsed');
        body.classList.remove('sidebar-open');
        body.classList.add('sidebar-collapsed');
        localStorage.setItem('sidebarCollapsed', 'true');
    }

    // Dispatch event for ticker to adjust
    document.dispatchEvent(new CustomEvent('sidebarToggled'));
}

function setActiveMenuItem() {
    const currentPath = window.location.pathname;
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach(item => {
        item.classList.remove('active');
        
        const href = item.getAttribute('href');
        if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
            item.classList.add('active');
        }
    });
    
    // Special cases for specific routes
    if (currentPath === '/') {
        document.querySelector('a[href="/"]')?.classList.add('active');
    }
}

function loadNotificationsCount() {
    // Simulate loading notifications count
    // In a real application, this would be an AJAX call
    fetch('/api/notifications/count')
        .then(response => response.json())
        .then(data => {
            const notificationCount = document.querySelector('.notification-count');
            const badgeCount = document.querySelector('.badge');
            
            if (notificationCount) {
                notificationCount.textContent = data.count || 0;
                notificationCount.style.display = data.count > 0 ? 'block' : 'none';
            }
            
            if (badgeCount) {
                badgeCount.textContent = data.due_today || 0;
                badgeCount.style.display = data.due_today > 0 ? 'inline' : 'none';
            }
        })
        .catch(error => {
            console.log('Could not load notifications count');
        });
}

// Mobile sidebar toggle
function toggleMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    
    if (window.innerWidth <= 768) {
        sidebar.classList.toggle('mobile-open');
    }
}

// Close mobile sidebar when clicking outside
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.querySelector('.sidebar-toggle-btn');
    
    if (window.innerWidth <= 768 && 
        !sidebar.contains(event.target) && 
        !toggleBtn.contains(event.target)) {
        sidebar.classList.remove('mobile-open');
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    
    if (window.innerWidth > 768) {
        sidebar.classList.remove('mobile-open');
    }
});

// Smooth scrolling for menu items
document.querySelectorAll('.menu-item').forEach(item => {
    item.addEventListener('click', function(e) {
        // Add loading state
        this.style.opacity = '0.7';
        
        setTimeout(() => {
            this.style.opacity = '1';
        }, 200);
    });
});

// Tooltip for collapsed sidebar
function initTooltips() {
    const sidebar = document.getElementById('sidebar');
    
    if (sidebar.classList.contains('collapsed')) {
        document.querySelectorAll('.menu-item').forEach(item => {
            const text = item.querySelector('span')?.textContent;
            if (text) {
                item.setAttribute('title', text);
            }
        });
    } else {
        document.querySelectorAll('.menu-item').forEach(item => {
            item.removeAttribute('title');
        });
    }
}

// Update tooltips when sidebar is toggled
const originalToggleSidebar = toggleSidebar;
toggleSidebar = function() {
    originalToggleSidebar();
    setTimeout(initTooltips, 300);
};

// Initialize tooltips
setTimeout(initTooltips, 100);
