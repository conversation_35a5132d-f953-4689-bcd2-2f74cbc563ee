#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import shutil
from datetime import datetime

def backup_databases():
    """نسخ احتياطي من قواعد البيانات"""
    print("💾 إنشاء نسخ احتياطية...")
    
    backup_folder = f"backup_ultimate_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    if not os.path.exists(backup_folder):
        os.makedirs(backup_folder)
    
    db_files = ['auction.db', 'working_database.db', 'instance/auction.db']
    for db_file in db_files:
        if os.path.exists(db_file):
            backup_path = os.path.join(backup_folder, os.path.basename(db_file))
            shutil.copy2(db_file, backup_path)
            print(f"✅ نسخ احتياطي: {db_file}")

def create_ultimate_database():
    """إنشاء قاعدة بيانات نهائية مع جميع الأعمدة"""
    print("🏗️ إنشاء قاعدة البيانات النهائية...")
    
    # حذف قواعد البيانات القديمة
    db_files = ['auction.db', 'working_database.db']
    for db_file in db_files:
        if os.path.exists(db_file):
            os.remove(db_file)
            print(f"🗑️ حذف: {db_file}")
    
    # إنشاء مجلد instance
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    # إنشاء قاعدة البيانات
    conn = sqlite3.connect('auction.db')
    cursor = conn.cursor()
    
    print("👤 إنشاء جدول المستخدمين...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(80) UNIQUE NOT NULL,
            email VARCHAR(120),
            password_hash VARCHAR(255) NOT NULL,
            name VARCHAR(100),
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("👥 إنشاء جدول العملاء...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customer (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_ar VARCHAR(100) NOT NULL,
            name_en VARCHAR(100),
            phone VARCHAR(20) NOT NULL,
            email VARCHAR(100),
            id_number VARCHAR(20),
            nationality VARCHAR(50) DEFAULT 'قطري',
            birth_date DATE,
            address TEXT,
            city VARCHAR(50) DEFAULT 'الدوحة',
            postal_code VARCHAR(10),
            profession VARCHAR(100),
            company VARCHAR(100),
            monthly_income REAL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    print("🚗 إنشاء جدول السيارات مع جميع الأعمدة...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS car (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            make VARCHAR(50) NOT NULL,
            model VARCHAR(50) NOT NULL,
            year INTEGER NOT NULL,
            price REAL NOT NULL,
            status VARCHAR(20) DEFAULT 'available',
            color VARCHAR(30),
            fuel_type VARCHAR(20),
            transmission VARCHAR(20),
            engine_size VARCHAR(20),
            mileage INTEGER,
            body_type VARCHAR(30),
            doors INTEGER,
            seats INTEGER,
            vin_number VARCHAR(50),
            license_plate VARCHAR(20),
            insurance_expiry DATE,
            registration_expiry DATE,
            condition VARCHAR(20),
            features TEXT,
            notes TEXT,
            purchase_date DATE,
            purchase_price REAL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("🔢 إنشاء جدول الأرقام المميزة...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS premium_number (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            number VARCHAR(20) UNIQUE NOT NULL,
            category VARCHAR(50),
            price REAL,
            status VARCHAR(20) DEFAULT 'available',
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("📋 إنشاء جدول العقود...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS contract (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER NOT NULL,
            car_id INTEGER,
            premium_number_id INTEGER,
            contract_type VARCHAR(50) DEFAULT 'car_sale',
            payment_method VARCHAR(50) DEFAULT 'cash',
            total_amount REAL NOT NULL,
            down_payment REAL DEFAULT 0,
            monthly_payment REAL DEFAULT 0,
            installment_months INTEGER DEFAULT 0,
            interest_rate REAL DEFAULT 0,
            remaining_amount REAL DEFAULT 0,
            contract_date DATE DEFAULT CURRENT_DATE,
            delivery_date DATE,
            warranty_period INTEGER DEFAULT 12,
            status VARCHAR(20) DEFAULT 'active',
            description TEXT,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customer (id),
            FOREIGN KEY (car_id) REFERENCES car (id),
            FOREIGN KEY (premium_number_id) REFERENCES premium_number (id)
        )
    ''')
    
    print("🎯 إنشاء جدول المزادات...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS auction (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            premium_number_id INTEGER,
            car_id INTEGER,
            starting_price REAL NOT NULL,
            current_price REAL,
            reserve_price REAL,
            bid_increment REAL DEFAULT 1000,
            start_time DATETIME NOT NULL,
            end_time DATETIME NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            auto_extend BOOLEAN DEFAULT 0,
            extend_time INTEGER DEFAULT 300,
            commission_rate REAL DEFAULT 5.0,
            commission_amount REAL DEFAULT 0,
            views INTEGER DEFAULT 0,
            total_bids INTEGER DEFAULT 0,
            winner VARCHAR(100),
            winner_id INTEGER,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
            FOREIGN KEY (car_id) REFERENCES car (id),
            FOREIGN KEY (winner_id) REFERENCES customer (id),
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
    ''')
    
    print("💰 إنشاء جدول المزايدات...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS bid (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            auction_id INTEGER NOT NULL,
            customer_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            bid_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_winning BOOLEAN DEFAULT 0,
            FOREIGN KEY (auction_id) REFERENCES auction (id),
            FOREIGN KEY (customer_id) REFERENCES customer (id)
        )
    ''')
    
    print("💳 إنشاء جدول الأقساط...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS installment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contract_id INTEGER NOT NULL,
            installment_number INTEGER NOT NULL,
            due_date DATE NOT NULL,
            amount REAL NOT NULL,
            paid_amount REAL DEFAULT 0,
            payment_date DATE,
            status VARCHAR(20) DEFAULT 'pending',
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contract_id) REFERENCES contract (id)
        )
    ''')
    
    print("📝 إدراج البيانات التجريبية...")
    
    # إدراج مستخدم admin
    cursor.execute('''
        INSERT OR REPLACE INTO user (id, username, email, password_hash, name, role)
        VALUES (1, 'admin', '<EMAIL>', 'admin', 'مدير النظام', 'admin')
    ''')
    
    # إدراج عملاء
    customers_data = [
        (1, 'أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '50123456', '<EMAIL>', '12345678901'),
        (2, 'فاطمة علي الأنصاري', 'Fatima Ali Al-Ansari', '50123457', '<EMAIL>', '12345678902'),
        (3, 'محمد سالم المري', 'Mohammed Salem Al-Marri', '50123458', '<EMAIL>', '12345678903'),
        (4, 'نورا خالد الثاني', 'Nora Khalid Al-Thani', '50123459', '<EMAIL>', '12345678904'),
        (5, 'عبدالله أحمد الكواري', 'Abdullah Ahmed Al-Kuwari', '50123460', '<EMAIL>', '12345678905')
    ]
    
    for customer in customers_data:
        cursor.execute('''
            INSERT OR REPLACE INTO customer (id, name_ar, name_en, phone, email, id_number, nationality, city)
            VALUES (?, ?, ?, ?, ?, ?, 'قطري', 'الدوحة')
        ''', customer)
    
    # إدراج سيارات مع جميع الحقول
    cars_data = [
        (1, 'تويوتا', 'كامري', 2023, 150000, 'available', 'أبيض', 'بنزين', 'أوتوماتيك', '2.5L', 5000, 'سيدان', 4, 5, 'TOY123456789', 'ABC123', '2024-12-31', '2024-12-31', 'ممتاز', 'مكيف، نظام ملاحة، كاميرا خلفية', 'سيارة بحالة ممتازة', '2023-01-15', 140000),
        (2, 'نيسان', 'التيما', 2023, 140000, 'available', 'أسود', 'بنزين', 'أوتوماتيك', '2.0L', 3000, 'سيدان', 4, 5, 'NIS123456789', 'DEF456', '2024-12-31', '2024-12-31', 'ممتاز', 'مكيف، بلوتوث، مقاعد جلد', 'سيارة حديثة', '2023-02-20', 130000),
        (3, 'هوندا', 'أكورد', 2023, 160000, 'available', 'فضي', 'بنزين', 'أوتوماتيك', '2.4L', 2000, 'سيدان', 4, 5, 'HON123456789', 'GHI789', '2024-12-31', '2024-12-31', 'جديد', 'مكيف، نظام أمان متقدم، شاشة لمس', 'سيارة جديدة بالكامل', '2023-03-10', 150000)
    ]
    
    for car in cars_data:
        cursor.execute('''
            INSERT OR REPLACE INTO car 
            (id, make, model, year, price, status, color, fuel_type, transmission, engine_size, mileage, body_type, doors, seats, vin_number, license_plate, insurance_expiry, registration_expiry, condition, features, notes, purchase_date, purchase_price)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', car)
    
    # إدراج أرقام مميزة
    premium_numbers_data = [
        (1, '777', 'VIP', 300000, 'auction', 'رقم مميز من فئة VIP'),
        (2, '999', 'VIP', 450000, 'auction', 'رقم مميز من فئة VIP'),
        (3, '555', 'Premium', 180000, 'auction', 'رقم مميز من فئة Premium'),
        (4, '123', 'Standard', 50000, 'available', 'رقم مميز من فئة Standard'),
        (5, '456', 'Standard', 75000, 'available', 'رقم مميز من فئة Standard')
    ]
    
    for pn in premium_numbers_data:
        cursor.execute('''
            INSERT OR REPLACE INTO premium_number (id, number, category, price, status, description)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', pn)
    
    # إدراج عقود
    contracts_data = [
        (1, 1, 1, None, 'car_sale', 'cash', 150000, 'بيع سيارة تويوتا كامري'),
        (2, 2, None, 1, 'premium_number', 'installment', 300000, 'بيع رقم مميز 777'),
        (3, 3, 2, None, 'car_sale', 'installment', 140000, 'بيع سيارة نيسان التيما')
    ]
    
    for contract in contracts_data:
        cursor.execute('''
            INSERT OR REPLACE INTO contract (id, customer_id, car_id, premium_number_id, contract_type, payment_method, total_amount, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', contract)
    
    # إدراج مزادات
    now = datetime.now()
    start_time = now.strftime('%Y-%m-%d %H:%M:%S')
    end_time = (now.replace(hour=23, minute=59)).strftime('%Y-%m-%d %H:%M:%S')
    
    auctions_data = [
        (1, 'مزاد الرقم المميز 777', 'مزاد للرقم المميز 777', 1, None, 250000, 303000, start_time, end_time, 'active'),
        (2, 'مزاد الرقم المميز 999', 'مزاد للرقم المميز 999', 2, None, 400000, 450000, start_time, end_time, 'active'),
        (3, 'مزاد الرقم المميز 555', 'مزاد للرقم المميز 555', 3, None, 150000, 180000, start_time, end_time, 'active')
    ]
    
    for auction in auctions_data:
        cursor.execute('''
            INSERT OR REPLACE INTO auction (id, title, description, premium_number_id, car_id, starting_price, current_price, start_time, end_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', auction)
    
    conn.commit()
    
    # نسخ قاعدة البيانات
    shutil.copy2('auction.db', 'working_database.db')
    shutil.copy2('auction.db', 'instance/auction.db')
    
    conn.close()
    
    print("✅ تم إنشاء قاعدة البيانات النهائية بنجاح!")
    return True

def verify_database():
    """التحقق من قاعدة البيانات"""
    print("🔍 التحقق من قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('auction.db')
        cursor = conn.cursor()
        
        # التحقق من جدول السيارات
        cursor.execute("PRAGMA table_info(car)")
        car_columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = ['features', 'notes', 'purchase_date', 'purchase_price']
        missing_columns = [col for col in required_columns if col not in car_columns]
        
        if missing_columns:
            print(f"❌ أعمدة مفقودة في جدول car: {missing_columns}")
            return False
        
        print("✅ جدول car يحتوي على جميع الأعمدة المطلوبة")
        
        # التحقق من البيانات
        cursor.execute("SELECT COUNT(*) FROM customer")
        customers_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM car")
        cars_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM premium_number")
        pn_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM contract")
        contracts_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auction")
        auctions_count = cursor.fetchone()[0]
        
        print(f"📊 إحصائيات قاعدة البيانات:")
        print(f"   👥 العملاء: {customers_count}")
        print(f"   🚗 السيارات: {cars_count}")
        print(f"   🔢 الأرقام المميزة: {pn_count}")
        print(f"   📋 العقود: {contracts_count}")
        print(f"   🎯 المزادات: {auctions_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 الإصلاح النهائي لقاعدة البيانات")
    print("=" * 60)
    
    try:
        # نسخ احتياطي
        backup_databases()
        
        # إنشاء قاعدة البيانات النهائية
        if create_ultimate_database():
            print("=" * 60)
            
            # التحقق من قاعدة البيانات
            if verify_database():
                print("=" * 60)
                print("🎉 تم الإصلاح النهائي بنجاح!")
                print("✅ جميع الأعمدة المطلوبة موجودة")
                print("✅ البيانات التجريبية مُدرجة")
                print("✅ قاعدة البيانات جاهزة للاستخدام")
                print("=" * 60)
                print("🚀 يمكنك الآن تشغيل النظام:")
                print("   python working_app.py")
                print("=" * 60)
                return True
            else:
                print("❌ فشل في التحقق من قاعدة البيانات")
                return False
        else:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    main()
