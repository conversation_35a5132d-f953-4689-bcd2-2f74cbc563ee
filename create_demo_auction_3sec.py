#!/usr/bin/env python3
"""
إنشاء مزاد تجريبي للعرض المستقل مع التحديث كل 3 ثوان
"""

import os
import sys
from datetime import datetime, timedelta

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_demo_auction():
    """إنشاء مزاد تجريبي نشط"""
    
    try:
        from working_app import app, db, Customer, PremiumNumber, Auction, Bid
        
        with app.app_context():
            print("🎯 إنشاء مزاد تجريبي للعرض المستقل...")
            
            # إنشاء عميل تجريبي
            customer = Customer.query.filter_by(name_ar='أحمد محمد الكعبي').first()
            if not customer:
                customer = Customer(
                    name_ar='أحمد محمد الكعبي',
                    name_en='<PERSON>',
                    phone='+974 5555 1234',
                    email='<EMAIL>',
                    id_number='28512345678',
                    nationality='قطري'
                )
                db.session.add(customer)
                db.session.flush()
                print("✅ تم إنشاء العميل التجريبي")
            
            # إنشاء رقم مميز
            premium_number = PremiumNumber.query.filter_by(number='32324').first()
            if not premium_number:
                premium_number = PremiumNumber(
                    number='32324',
                    category='VIP',
                    price=50000,
                    status='auction'
                )
                db.session.add(premium_number)
                db.session.flush()
                print("✅ تم إنشاء الرقم المميز 32324")
            else:
                premium_number.status = 'auction'
            
            # حذف المزاد القديم إن وجد
            old_auction = Auction.query.filter_by(premium_number_id=premium_number.id).first()
            if old_auction:
                Bid.query.filter_by(auction_id=old_auction.id).delete()
                db.session.delete(old_auction)
                print("🗑️ تم حذف المزاد القديم")
            
            # إنشاء مزاد نشط جديد
            start_time = datetime.now()
            end_time = start_time + timedelta(minutes=30)  # مزاد لمدة 30 دقيقة
            
            auction = Auction(
                title=f'مزاد الرقم المميز {premium_number.number}',
                description='مزاد تجريبي للعرض المستقل مع تحديث كل 3 ثوان',
                premium_number_id=premium_number.id,
                starting_price=40000,
                current_price=24500,
                reserve_price=45000,
                bid_increment=500,
                start_time=start_time,
                end_time=end_time,
                status='active',
                auto_extend=True,
                commission_rate=2.0,
                total_bids=7
            )
            db.session.add(auction)
            db.session.flush()
            print("✅ تم إنشاء المزاد النشط")
            
            # إنشاء مزايدات تجريبية
            bid_times = [
                start_time + timedelta(minutes=2),
                start_time + timedelta(minutes=5),
                start_time + timedelta(minutes=8),
                start_time + timedelta(minutes=12),
                start_time + timedelta(minutes=15),
                start_time + timedelta(minutes=18),
                start_time + timedelta(minutes=22)
            ]
            
            bid_amounts = [20000, 21000, 22500, 23000, 23500, 24000, 24500]
            
            for i, (bid_time, amount) in enumerate(zip(bid_times, bid_amounts)):
                bid = Bid(
                    auction_id=auction.id,
                    customer_id=customer.id,
                    bid_amount=amount,
                    bid_time=bid_time
                )
                db.session.add(bid)
            
            # تحديث السعر الحالي
            auction.current_price = max(bid_amounts)
            auction.commission_amount = (auction.current_price * auction.commission_rate) / 100
            
            db.session.commit()
            
            print("=" * 60)
            print("🎉 تم إنشاء المزاد التجريبي بنجاح!")
            print("=" * 60)
            print(f"🔢 الرقم المميز: {premium_number.number}")
            print(f"💰 السعر الحالي: {auction.current_price:,.0f} ر.ق")
            print(f"⏰ ينتهي في: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📊 عدد المزايدات: {len(bid_amounts)}")
            print(f"👤 المزايد الحالي: {customer.name_ar}")
            print("=" * 60)
            print("🌐 رابط العرض المستقل:")
            print(f"   http://127.0.0.1:9898/auction/standalone/{auction.id}")
            print("🔄 التحديث التلقائي: كل 3 ثوان")
            print("=" * 60)
            
            return auction.id
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المزاد: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("🚀 إنشاء مزاد تجريبي للعرض المستقل")
    print("🔄 مع تحديث تلقائي كل 3 ثوان")
    print("=" * 60)
    
    auction_id = create_demo_auction()
    
    if auction_id:
        print("\n🎯 المزاد جاهز للعرض المستقل!")
        print("💡 استخدم OPEN_STANDALONE_AUCTION.bat لفتح العرض")
        
        # فتح المتصفح تلقائياً
        try:
            import webbrowser
            url = f"http://127.0.0.1:9898/auction/standalone/{auction_id}"
            print(f"\n🌐 فتح العرض المستقل: {url}")
            webbrowser.open(url)
        except:
            print("💡 افتح المتصفح يدوياً على الرابط أعلاه")
    else:
        print("❌ فشل في إنشاء المزاد")

if __name__ == '__main__':
    main()
    input("\nاضغط Enter للخروج...")
