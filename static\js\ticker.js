// Scrolling Ticker JavaScript
class ScrollingTicker {
    constructor() {
        this.tickerContainer = null;
        this.tickerScroll = null;
        this.items = [];
        this.isLoading = false;
        this.refreshInterval = 30000; // 30 seconds
        this.animationDuration = 80; // seconds
        this.isPaused = false;
        this.timerPaused = false;
        this.init();
    }

    init() {
        this.createTickerHTML();
        this.loadData();
        this.startAutoRefresh();
        this.bindEvents();
    }

    createTickerHTML() {
        // Create ticker container
        const tickerHTML = `
            <div class="ticker-container" id="ticker-container">
                <div class="ticker-wrapper">
                    <div class="ticker-label">
                        <i class="fas fa-star"></i>
                        <span id="ticker-label-text">العروض المميزة</span>
                    </div>
                    <div class="ticker-content">
                        <div class="ticker-scroll" id="ticker-scroll">
                            <div class="ticker-item">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span class="item-title">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                    <div class="ticker-controls">
                        <button class="ticker-control-btn" id="ticker-pause-btn" title="إيقاف/تشغيل">
                            <i class="fas fa-pause"></i>
                        </button>
                        <button class="ticker-control-btn" id="ticker-refresh-btn" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="ticker-control-btn" id="ticker-color-btn" title="تأثير الألوان">
                            <i class="fas fa-palette"></i>
                        </button>
                        <button class="ticker-control-btn" id="ticker-timer-btn" title="إيقاف/تشغيل الوقت">
                            <i class="fas fa-clock"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Insert ticker at the beginning of body
        document.body.insertAdjacentHTML('afterbegin', tickerHTML);

        this.tickerContainer = document.getElementById('ticker-container');
        this.tickerScroll = document.getElementById('ticker-scroll');

        // Bind control buttons
        this.bindControlButtons();
    }

    bindControlButtons() {
        const pauseBtn = document.getElementById('ticker-pause-btn');
        const refreshBtn = document.getElementById('ticker-refresh-btn');
        const colorBtn = document.getElementById('ticker-color-btn');

        pauseBtn.addEventListener('click', () => {
            const isPaused = this.togglePause();
            pauseBtn.innerHTML = isPaused ? '<i class="fas fa-play"></i>' : '<i class="fas fa-pause"></i>';
        });

        refreshBtn.addEventListener('click', () => {
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.loadData().then(() => {
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
            });
        });

        colorBtn.addEventListener('click', () => {
            this.toggleColorAnimation();
        });

        const timerBtn = document.getElementById('ticker-timer-btn');
        if (timerBtn) {
            timerBtn.addEventListener('click', () => {
                this.toggleTimer();
            });
        }
    }

    toggleColorAnimation() {
        const container = this.tickerContainer;
        if (container.style.animation === 'none') {
            container.style.animation = 'gradientShift 10s ease-in-out infinite';
        } else {
            container.style.animation = 'none';
        }
    }

    toggleTimer() {
        const timerElements = document.querySelectorAll('[id*="countdown"], [id*="timer"], .countdown, .timer');
        const timerBtn = document.getElementById('ticker-timer-btn');
        const icon = timerBtn.querySelector('i');

        // تبديل حالة الوقت
        if (this.timerPaused) {
            // تشغيل الوقت
            timerElements.forEach(element => {
                element.style.animationPlayState = 'running';
                element.style.opacity = '1';
            });
            icon.className = 'fas fa-clock';
            timerBtn.title = 'إيقاف الوقت';
            this.timerPaused = false;

            // إرسال إشارة لاستئناف العدادات
            document.dispatchEvent(new CustomEvent('timerResume'));
        } else {
            // إيقاف الوقت
            timerElements.forEach(element => {
                element.style.animationPlayState = 'paused';
                element.style.opacity = '0.5';
            });
            icon.className = 'fas fa-pause';
            timerBtn.title = 'تشغيل الوقت';
            this.timerPaused = true;

            // إرسال إشارة لإيقاف العدادات
            document.dispatchEvent(new CustomEvent('timerPause'));
        }
    }

    async loadData() {
        if (this.isLoading) return;

        this.isLoading = true;

        try {
            // Load cars and premium numbers
            const [carsResponse, numbersResponse] = await Promise.all([
                fetch('/api/ticker/cars'),
                fetch('/api/ticker/premium-numbers')
            ]);

            const cars = await carsResponse.json();
            const numbers = await numbersResponse.json();

            this.items = [...cars, ...numbers];

            // If no data, add sample data
            if (this.items.length === 0) {
                this.items = this.getSampleData();
            }

            this.renderItems();
        } catch (error) {
            console.error('Error loading ticker data:', error);
            // Show sample data on error
            this.items = this.getSampleData();
            this.renderItems();
        } finally {
            this.isLoading = false;
        }
    }

    getSampleData() {
        return [
            {
                id: 'sample1',
                type: 'car',
                make: 'تويوتا',
                model: 'كامري',
                year: 2023,
                price: 120000,
                status: 'available'
            },
            {
                id: 'sample2',
                type: 'premium_number',
                number: '77777',
                price: 50000,
                status: 'available',
                category: 'VIP'
            },
            {
                id: 'sample3',
                type: 'car',
                make: 'نيسان',
                model: 'التيما',
                year: 2024,
                price: 95000,
                status: 'available'
            },
            {
                id: 'sample4',
                type: 'premium_number',
                number: '12345',
                price: 25000,
                status: 'available',
                category: 'مميز'
            },
            {
                id: 'sample5',
                type: 'car',
                make: 'هوندا',
                model: 'أكورد',
                year: 2023,
                price: 110000,
                status: 'reserved'
            },
            {
                id: 'sample6',
                type: 'premium_number',
                number: '88888',
                price: 75000,
                status: 'available',
                category: 'VIP'
            },
            {
                id: 'sample7',
                type: 'car',
                make: 'لكزس',
                model: 'ES350',
                year: 2024,
                price: 180000,
                status: 'available'
            },
            {
                id: 'sample8',
                type: 'premium_number',
                number: '99999',
                price: 100000,
                status: 'available',
                category: 'VIP'
            },
            {
                id: 'sample9',
                type: 'car',
                make: 'بي إم دبليو',
                model: 'X5',
                year: 2023,
                price: 250000,
                status: 'available'
            },
            {
                id: 'sample10',
                type: 'premium_number',
                number: '11111',
                price: 60000,
                status: 'available',
                category: 'VIP'
            }
        ];
    }

    renderItems() {
        if (this.items.length === 0) {
            this.showNoData();
            return;
        }

        // Shuffle items for variety
        const shuffledItems = this.shuffleArray([...this.items]);

        // Create HTML for items
        const itemsHTML = shuffledItems.map(item => this.createItemHTML(item)).join('');

        // Update ticker content
        this.tickerScroll.innerHTML = itemsHTML;

        // Update label with count
        this.updateLabel();

        // Restart animation
        this.restartAnimation();
    }

    updateLabel() {
        const labelElement = document.getElementById('ticker-label-text');
        if (labelElement) {
            const carCount = this.items.filter(item => item.type === 'car').length;
            const numberCount = this.items.filter(item => item.type === 'premium_number').length;

            if (carCount > 0 && numberCount > 0) {
                labelElement.textContent = `${carCount} سيارة • ${numberCount} رقم مميز`;
            } else if (carCount > 0) {
                labelElement.textContent = `${carCount} سيارة متاحة`;
            } else if (numberCount > 0) {
                labelElement.textContent = `${numberCount} رقم مميز`;
            } else {
                labelElement.textContent = 'العروض المميزة';
            }
        }
    }

    createItemHTML(item) {
        const clickHandler = item.id.toString().startsWith('sample') ? '' :
            (item.type === 'car' ? `onclick="window.open('/cars/${item.id}', '_blank')"` :
             `onclick="window.open('/premium-numbers/${item.id}', '_blank')"`);

        const isVIP = item.category === 'VIP';
        const vipClass = isVIP ? ' vip' : '';

        if (item.type === 'car') {
            return `
                <div class="ticker-item car" ${clickHandler}>
                    <i class="fas fa-car"></i>
                    <span class="item-title">${item.make} ${item.model} ${item.year}</span>
                    <span class="item-price">${this.formatPrice(item.price)} ر.ق</span>
                    <span class="item-status">${this.getStatusText(item.status)}</span>
                    ${item.status === 'available' ? '<i class="fas fa-star" style="color: #ffd700; margin-right: 5px;"></i>' : ''}
                </div>
            `;
        } else if (item.type === 'premium_number') {
            return `
                <div class="ticker-item premium-number${vipClass}" ${clickHandler}>
                    <i class="fas fa-hashtag"></i>
                    <span class="item-title">رقم مميز ${item.number}</span>
                    <span class="item-price">${this.formatPrice(item.price)} ر.ق</span>
                    <span class="item-status">${this.getStatusText(item.status)}</span>
                    ${isVIP ? '<i class="fas fa-crown" style="color: #ffd700; margin-right: 5px;"></i>' : ''}
                </div>
            `;
        }
        return '';
    }

    formatPrice(price) {
        return new Intl.NumberFormat('ar-QA').format(price);
    }

    getStatusText(status) {
        const statusMap = {
            'available': 'متاح',
            'sold': 'مباع',
            'reserved': 'محجوز',
            'maintenance': 'صيانة'
        };
        return statusMap[status] || status;
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    restartAnimation() {
        // Remove and re-add animation class to restart
        this.tickerScroll.style.animation = 'none';
        this.tickerScroll.offsetHeight; // Trigger reflow
        this.tickerScroll.style.animation = `scroll-right ${this.animationDuration}s linear infinite`;

        if (this.isPaused) {
            this.tickerScroll.style.animationPlayState = 'paused';
        }
    }

    showError() {
        this.tickerScroll.innerHTML = `
            <div class="ticker-item">
                <i class="fas fa-exclamation-triangle"></i>
                <span class="item-title">خطأ في تحميل البيانات</span>
            </div>
        `;
    }

    showNoData() {
        this.tickerScroll.innerHTML = `
            <div class="ticker-item">
                <i class="fas fa-info-circle"></i>
                <span class="item-title">لا توجد عروض متاحة حالياً</span>
            </div>
        `;
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadData();
        }, this.refreshInterval);
    }

    bindEvents() {
        // Pause on hover
        this.tickerContainer.addEventListener('mouseenter', () => {
            this.isPaused = true;
            this.tickerScroll.style.animationPlayState = 'paused';
        });

        this.tickerContainer.addEventListener('mouseleave', () => {
            this.isPaused = false;
            this.tickerScroll.style.animationPlayState = 'running';
        });

        // Handle sidebar toggle
        document.addEventListener('sidebarToggled', () => {
            // Small delay to allow CSS transition to complete
            setTimeout(() => {
                this.restartAnimation();
            }, 300);
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            setTimeout(() => {
                this.restartAnimation();
            }, 100);
        });
    }

    // Public method to manually refresh
    refresh() {
        this.loadData();
    }

    // Public method to add custom item
    addCustomItem(item) {
        this.items.unshift(item);
        this.renderItems();
    }

    // Public method to remove item
    removeItem(itemId, itemType) {
        this.items = this.items.filter(item =>
            !(item.id === itemId && item.type === itemType)
        );
        this.renderItems();
    }

    // Public method to pause/resume
    togglePause() {
        this.isPaused = !this.isPaused;
        this.tickerScroll.style.animationPlayState = this.isPaused ? 'paused' : 'running';
        return this.isPaused;
    }

    // Public method to change speed
    setSpeed(duration) {
        this.animationDuration = duration;
        this.restartAnimation();
    }

    // Public method to hide/show ticker
    toggle() {
        this.tickerContainer.style.display =
            this.tickerContainer.style.display === 'none' ? 'block' : 'none';
    }
}

// Initialize ticker when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for sidebar to initialize
    setTimeout(() => {
        window.scrollingTicker = new ScrollingTicker();
    }, 500);
});

// Export for global access
window.ScrollingTicker = ScrollingTicker;
