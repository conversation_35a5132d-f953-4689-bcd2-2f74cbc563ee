#!/usr/bin/env python3
"""
إضافة جداول الصيانة والفحص - نظام معرض قطر للسيارات
"""

import sqlite3
import os
from datetime import datetime, date, timedelta

def add_maintenance_tables():
    """إضافة جداول الصيانة والفحص"""
    db_path = 'working_database.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 إضافة جداول الصيانة والفحص...")
        
        # إنشاء جدول الصيانة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                car_id INTEGER NOT NULL,
                maintenance_type VARCHAR(50) NOT NULL,
                description TEXT NOT NULL,
                cost FLOAT DEFAULT 0,
                start_date DATE NOT NULL,
                end_date DATE,
                status VARCHAR(20) DEFAULT 'pending',
                mechanic_name VARCHAR(100),
                workshop_name VARCHAR(100),
                notes TEXT,
                next_maintenance_date DATE,
                mileage_at_maintenance INTEGER,
                parts_used TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (car_id) REFERENCES car (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')
        print("✅ تم إنشاء جدول الصيانة")
        
        # إنشاء جدول الفحص
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inspection (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                car_id INTEGER NOT NULL,
                inspection_type VARCHAR(50) NOT NULL,
                inspection_date DATE NOT NULL,
                inspector_name VARCHAR(100),
                inspection_center VARCHAR(100),
                result VARCHAR(20) DEFAULT 'pending',
                score INTEGER,
                cost FLOAT DEFAULT 0,
                expiry_date DATE,
                certificate_number VARCHAR(50),
                notes TEXT,
                issues_found TEXT,
                recommendations TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (car_id) REFERENCES car (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')
        print("✅ تم إنشاء جدول الفحص")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إضافة جداول الصيانة والفحص بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الجداول: {e}")
        return False

def add_sample_maintenance_data():
    """إضافة بيانات تجريبية للصيانة والفحص"""
    db_path = 'working_database.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📝 إضافة بيانات تجريبية للصيانة...")
        
        # بيانات صيانة تجريبية
        today = date.today()
        maintenance_data = [
            (1, 'routine', 'صيانة دورية شاملة - تغيير زيت وفلاتر', 350.0, today - timedelta(days=5), today - timedelta(days=3), 'completed', 'أحمد محمد', 'ورشة الخليج', 'تم تغيير الزيت والفلاتر بنجاح', today + timedelta(days=90), 45000, 'زيت محرك، فلتر زيت، فلتر هواء', datetime.now(), 1),
            (2, 'repair', 'إصلاح نظام الفرامل', 800.0, today - timedelta(days=10), today - timedelta(days=8), 'completed', 'سالم أحمد', 'مركز الصيانة المتقدم', 'تم استبدال أقراص الفرامل الأمامية', None, 52000, 'أقراص فرامل أمامية، سائل فرامل', datetime.now(), 1),
            (3, 'oil_change', 'تغيير زيت المحرك', 180.0, today - timedelta(days=2), today - timedelta(days=1), 'completed', 'محمد علي', 'ورشة السرعة', 'تغيير زيت عادي', today + timedelta(days=60), 38000, 'زيت محرك 5W-30', datetime.now(), 1),
            (4, 'inspection', 'فحص شامل قبل البيع', 200.0, today - timedelta(days=1), None, 'in_progress', 'خالد يوسف', 'مركز الفحص الفني', 'فحص شامل لجميع أجزاء السيارة', None, 28000, None, datetime.now(), 1),
            (5, 'routine', 'صيانة دورية', 450.0, today, None, 'pending', None, None, 'صيانة مجدولة', None, 65000, None, datetime.now(), 1)
        ]
        
        for maintenance in maintenance_data:
            cursor.execute('''
                INSERT INTO maintenance (car_id, maintenance_type, description, cost, start_date, end_date, 
                                       status, mechanic_name, workshop_name, notes, next_maintenance_date, 
                                       mileage_at_maintenance, parts_used, created_at, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', maintenance)
        
        print("✅ تم إضافة 5 سجلات صيانة تجريبية")
        
        print("🔍 إضافة بيانات تجريبية للفحص...")
        
        # بيانات فحص تجريبية
        inspection_data = [
            (1, 'annual', today - timedelta(days=30), 'محمد الفحص', 'مركز الفحص الدوري', 'passed', 85, 150.0, today + timedelta(days=335), 'INS-2024-001', 'فحص سنوي ناجح', None, 'الحفاظ على الصيانة الدورية', datetime.now(), 1),
            (2, 'pre_sale', today - timedelta(days=15), 'أحمد المفتش', 'مركز فحص السيارات', 'passed', 78, 200.0, None, 'INS-2024-002', 'فحص ما قبل البيع', 'خدوش طفيفة في الطلاء', 'إصلاح الخدوش قبل البيع', datetime.now(), 1),
            (3, 'damage', today - timedelta(days=7), 'سالم الخبير', 'مركز تقدير الأضرار', 'conditional', 65, 100.0, None, 'INS-2024-003', 'فحص أضرار', 'ضرر في المصد الأمامي', 'استبدال المصد الأمامي', datetime.now(), 1),
            (4, 'technical', today - timedelta(days=3), 'خالد التقني', 'مركز الفحص التقني', 'pending', None, 180.0, None, None, 'فحص تقني شامل', None, None, datetime.now(), 1)
        ]
        
        for inspection in inspection_data:
            cursor.execute('''
                INSERT INTO inspection (car_id, inspection_type, inspection_date, inspector_name, 
                                      inspection_center, result, score, cost, expiry_date, certificate_number,
                                      notes, issues_found, recommendations, created_at, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', inspection)
        
        print("✅ تم إضافة 4 سجلات فحص تجريبية")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إضافة البيانات التجريبية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
        return False

if __name__ == '__main__':
    print("🚀 نظام معرض قطر للسيارات - إضافة نظام الصيانة والفحص")
    print("=" * 80)
    
    # إضافة الجداول
    if add_maintenance_tables():
        print("\n📊 إضافة بيانات تجريبية...")
        add_sample_maintenance_data()
        
        print("\n✅ تم تفعيل نظام الصيانة والفحص بنجاح!")
        print("\n🔧 الميزات الجديدة:")
        print("   🛠️ إدارة الصيانة الشاملة")
        print("   🔍 نظام الفحص المتقدم")
        print("   📊 تتبع تكاليف الصيانة")
        print("   📅 جدولة الصيانة القادمة")
        print("   📋 تقارير الصيانة والفحص")
        print("\n🌐 يمكنك الآن الوصول للنظام من الرابط: /maintenance")
    else:
        print("\n❌ فشل في تفعيل نظام الصيانة والفحص")
