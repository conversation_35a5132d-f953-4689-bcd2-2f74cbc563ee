#!/usr/bin/env python3
"""
تحديث قاعدة البيانات لإضافة نظام المزاد المتطور
"""

import sqlite3
import os
from datetime import datetime, timedelta

def update_auction_tables():
    """إنشاء وتحديث جداول المزاد الجديدة"""
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "working_database.db")
    
    print("🎯 تحديث قاعدة البيانات لنظام المزاد المتطور")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول المزادات الجديد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auction (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                premium_number_id INTEGER NOT NULL,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                starting_price FLOAT NOT NULL,
                reserve_price FLOAT,
                current_price FLOAT DEFAULT 0,
                bid_increment FLOAT DEFAULT 1000,
                start_time DATETIME NOT NULL,
                end_time DATETIME NOT NULL,
                status VARCHAR(20) DEFAULT 'scheduled',
                winner_id INTEGER,
                winning_bid_id INTEGER,
                total_bids INTEGER DEFAULT 0,
                views INTEGER DEFAULT 0,
                auto_extend BOOLEAN DEFAULT 1,
                extend_time INTEGER DEFAULT 300,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
                FOREIGN KEY (winner_id) REFERENCES customer (id),
                FOREIGN KEY (winning_bid_id) REFERENCES bid (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')
        print("✅ تم إنشاء جدول المزادات")
        
        # تحديث جدول المزايدات
        cursor.execute('DROP TABLE IF EXISTS bid')
        cursor.execute('''
            CREATE TABLE bid (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                auction_id INTEGER NOT NULL,
                customer_id INTEGER NOT NULL,
                bid_amount FLOAT NOT NULL,
                bid_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(20) DEFAULT 'active',
                ip_address VARCHAR(45),
                user_agent VARCHAR(255),
                is_auto_bid BOOLEAN DEFAULT 0,
                max_auto_bid FLOAT,
                FOREIGN KEY (auction_id) REFERENCES auction (id),
                FOREIGN KEY (customer_id) REFERENCES customer (id)
            )
        ''')
        print("✅ تم تحديث جدول المزايدات")
        
        # إنشاء جدول متابعة المزادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auction_watch (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                auction_id INTEGER NOT NULL,
                customer_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                notifications_enabled BOOLEAN DEFAULT 1,
                FOREIGN KEY (auction_id) REFERENCES auction (id),
                FOREIGN KEY (customer_id) REFERENCES customer (id),
                UNIQUE(auction_id, customer_id)
            )
        ''')
        print("✅ تم إنشاء جدول متابعة المزادات")
        
        # إنشاء جدول أنشطة المزاد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auction_activity (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                auction_id INTEGER NOT NULL,
                activity_type VARCHAR(30) NOT NULL,
                customer_id INTEGER,
                details TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (auction_id) REFERENCES auction (id),
                FOREIGN KEY (customer_id) REFERENCES customer (id)
            )
        ''')
        print("✅ تم إنشاء جدول أنشطة المزاد")
        
        # إضافة بيانات تجريبية للمزادات
        add_sample_auctions(cursor)
        
        conn.commit()
        print("\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False
    
    finally:
        if conn:
            conn.close()

def add_sample_auctions(cursor):
    """إضافة مزادات تجريبية"""
    
    print("\n📋 إضافة مزادات تجريبية...")
    
    # التحقق من وجود أرقام مميزة
    cursor.execute("SELECT id FROM premium_number WHERE status = 'available' LIMIT 3")
    available_numbers = cursor.fetchall()
    
    if not available_numbers:
        print("⚠️ لا توجد أرقام مميزة متاحة لإنشاء مزادات")
        return
    
    # إنشاء مزادات تجريبية
    sample_auctions = [
        {
            'premium_number_id': available_numbers[0][0],
            'title': 'مزاد رقم مميز VIP',
            'description': 'رقم مميز من فئة VIP للمزاد العلني',
            'starting_price': 50000,
            'reserve_price': 75000,
            'bid_increment': 5000,
            'start_time': datetime.now() + timedelta(minutes=2),
            'end_time': datetime.now() + timedelta(hours=24),
            'status': 'scheduled'
        }
    ]
    
    if len(available_numbers) > 1:
        sample_auctions.append({
            'premium_number_id': available_numbers[1][0],
            'title': 'مزاد سريع - رقم مميز',
            'description': 'مزاد سريع لمدة 6 ساعات',
            'starting_price': 25000,
            'reserve_price': 40000,
            'bid_increment': 2000,
            'start_time': datetime.now() - timedelta(minutes=30),
            'end_time': datetime.now() + timedelta(hours=6),
            'status': 'active'
        })
    
    for auction_data in sample_auctions:
        cursor.execute('''
            INSERT INTO auction (
                premium_number_id, title, description, starting_price, 
                reserve_price, bid_increment, start_time, end_time, status, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            auction_data['premium_number_id'],
            auction_data['title'],
            auction_data['description'],
            auction_data['starting_price'],
            auction_data['reserve_price'],
            auction_data['bid_increment'],
            auction_data['start_time'],
            auction_data['end_time'],
            auction_data['status'],
            1  # Admin user
        ))
        
        # تحديث حالة الرقم المميز
        cursor.execute(
            "UPDATE premium_number SET status = 'auction' WHERE id = ?",
            (auction_data['premium_number_id'],)
        )
    
    print(f"✅ تم إنشاء {len(sample_auctions)} مزاد تجريبي")

if __name__ == "__main__":
    print("🎯 تطوير نظام المزاد المتقدم")
    print("=" * 60)
    
    if update_auction_tables():
        print("\n🎉 تم تحديث النظام بنجاح!")
        print("💡 الميزات الجديدة:")
        print("   - مزادات مجدولة ونشطة")
        print("   - عد تنازلي مباشر")
        print("   - تمديد تلقائي للمزادات")
        print("   - متابعة المزادات")
        print("   - تاريخ مفصل للمزايدات")
        print("   - إحصائيات متقدمة")
        print("   - واجهة تفاعلية")
    else:
        print("\n❌ فشل في تحديث النظام")
    
    input("\nاضغط Enter للخروج...")
