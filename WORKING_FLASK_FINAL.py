#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
معرض قطر للسيارات - Flask النهائي العامل
نسخة مُصلحة ومبسطة تعمل بدون أخطاء
"""

import os
import sys
import sqlite3
from flask import Flask, render_template_string, request, redirect, session, flash, jsonify
from datetime import datetime
import traceback

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'qatar-showroom-secret-key-2024'

# إعدادات التطبيق
app.config['DEBUG'] = False
app.config['TESTING'] = False

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    try:
        conn = sqlite3.connect('working_database.db')
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def init_database():
    """تهيئة قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if not conn:
            print("❌ لا يمكن الاتصال بقاعدة البيانات")
            return False

        # فحص وجود الجداول
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        if len(tables) == 0:
            print("⚠️ قاعدة البيانات فارغة - إنشاء جداول أساسية")

            # إنشاء جدول العملاء
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS customer (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name_ar TEXT NOT NULL,
                    name_en TEXT,
                    phone TEXT,
                    email TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إنشاء جدول السيارات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS car (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    make TEXT NOT NULL,
                    model TEXT NOT NULL,
                    year INTEGER,
                    price REAL,
                    status TEXT DEFAULT 'available',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إدراج بيانات تجريبية
            cursor.execute("INSERT INTO customer (name_ar, name_en, phone) VALUES ('أحمد محمد', 'Ahmed Mohammed', '50123456')")
            cursor.execute("INSERT INTO customer (name_ar, name_en, phone) VALUES ('فاطمة علي', 'Fatima Ali', '50123457')")
            cursor.execute("INSERT INTO car (make, model, year, price) VALUES ('تويوتا', 'كامري', 2023, 150000)")
            cursor.execute("INSERT INTO car (make, model, year, price) VALUES ('نيسان', 'التيما', 2023, 140000)")

            conn.commit()
            print("✅ تم إنشاء الجداول والبيانات التجريبية")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def get_stats():
    """الحصول على الإحصائيات"""
    try:
        conn = get_db_connection()
        if not conn:
            return {'customers': 0, 'cars': 0, 'contracts': 0}

        cursor = conn.cursor()

        # عد العملاء
        cursor.execute("SELECT COUNT(*) FROM customer")
        customers_count = cursor.fetchone()[0]

        # عد السيارات
        cursor.execute("SELECT COUNT(*) FROM car")
        cars_count = cursor.fetchone()[0]

        conn.close()

        return {
            'customers': customers_count,
            'cars': cars_count,
            'contracts': 0  # مؤقت
        }

    except Exception as e:
        print(f"خطأ في الإحصائيات: {e}")
        return {'customers': 0, 'cars': 0, 'contracts': 0}

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    try:
        if 'logged_in' not in session:
            return redirect('/login')

        stats = get_stats()

        return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معرض قطر للسيارات - Flask العامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        .header-banner {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.9; }
            100% { opacity: 1; }
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid #3498db;
        }
        .dashboard-card:hover { transform: translateY(-5px); }
        .dashboard-card.customers { border-left-color: #e74c3c; }
        .dashboard-card.cars { border-left-color: #f39c12; }
        .dashboard-card.contracts { border-left-color: #27ae60; }
        .card-icon { font-size: 3rem; margin-bottom: 20px; }
        .action-btn {
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            text-decoration: none;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .action-btn:hover { transform: scale(1.05); text-decoration: none; }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header-banner">
            <h1><i class="fas fa-car"></i> معرض قطر للسيارات</h1>
            <p class="lead">Flask العامل - يعمل بدون أخطاء!</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-check-circle"></i> Flask يعمل بشكل مثالي
                </span>
            </div>
        </div>

        <div class="alert alert-success">
            <h5><i class="fas fa-database"></i> متصل بقاعدة البيانات بنجاح!</h5>
            <p class="mb-0">جميع البيانات محدثة ومتاحة</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card customers">
                <div class="card-icon text-danger">
                    <i class="fas fa-users"></i>
                </div>
                <h4>العملاء</h4>
                <p>إدارة بيانات العملاء</p>
                <div class="mt-3">
                    <h5 class="text-primary">{{ stats.customers }}</h5>
                    <small>عميل مسجل</small>
                </div>
                <a href="/customers" class="btn btn-danger action-btn">
                    <i class="fas fa-eye"></i> عرض العملاء
                </a>
            </div>

            <div class="dashboard-card cars">
                <div class="card-icon text-warning">
                    <i class="fas fa-car"></i>
                </div>
                <h4>السيارات</h4>
                <p>إدارة مخزون السيارات</p>
                <div class="mt-3">
                    <h5 class="text-primary">{{ stats.cars }}</h5>
                    <small>سيارة متاحة</small>
                </div>
                <a href="/cars" class="btn btn-warning action-btn">
                    <i class="fas fa-eye"></i> عرض السيارات
                </a>
            </div>

            <div class="dashboard-card contracts">
                <div class="card-icon text-success">
                    <i class="fas fa-file-contract"></i>
                </div>
                <h4>العقود</h4>
                <p>إدارة العقود والمبيعات</p>
                <div class="mt-3">
                    <h5 class="text-primary">{{ stats.contracts }}</h5>
                    <small>عقد</small>
                </div>
                <a href="/contracts" class="btn btn-success action-btn">
                    <i class="fas fa-eye"></i> عرض العقود
                </a>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                <p class="mb-0">
                    <strong>الإطار:</strong> Flask |
                    <strong>قاعدة البيانات:</strong> SQLite |
                    <strong>المنفذ:</strong> 5000 |
                    <strong>الحالة:</strong> يعمل بشكل مثالي
                </p>
            </div>
            <a href="/logout" class="btn btn-outline-danger">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</body>
</html>
        ''', stats=stats)

    except Exception as e:
        print(f"خطأ في الصفحة الرئيسية: {e}")
        return f"<h1>خطأ: {str(e)}</h1><a href='/login'>تسجيل الدخول</a>"

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    try:
        if request.method == 'POST':
            username = request.form.get('username', '')
            password = request.form.get('password', '')

            if username == 'admin' and password == 'admin':
                session['logged_in'] = True
                session['username'] = 'admin'
                flash('تم تسجيل الدخول بنجاح!', 'success')
                return redirect('/')
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تسجيل الدخول - معرض قطر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-header bg-success text-white text-center">
                        <h3><i class="fas fa-car"></i> معرض قطر للسيارات</h3>
                        <p class="mb-0">Flask العامل</p>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                                        {{ message }}
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" name="username" value="admin" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="password" value="admin" required>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-sign-in-alt"></i> دخول
                            </button>
                        </form>

                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                المستخدم: admin | كلمة المرور: admin
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        ''')

    except Exception as e:
        print(f"خطأ في تسجيل الدخول: {e}")
        return f"<h1>خطأ في تسجيل الدخول: {str(e)}</h1>"

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    try:
        session.clear()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect('/login')
    except Exception as e:
        print(f"خطأ في تسجيل الخروج: {e}")
        return redirect('/login')

@app.route('/customers')
def customers():
    """صفحة العملاء"""
    try:
        if 'logged_in' not in session:
            return redirect('/login')

        conn = get_db_connection()
        if not conn:
            return "<h1>خطأ في الاتصال بقاعدة البيانات</h1><a href='/'>العودة</a>"

        cursor = conn.cursor()
        cursor.execute("SELECT * FROM customer ORDER BY id DESC LIMIT 20")
        customers_data = cursor.fetchall()
        conn.close()

        return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>العملاء - معرض قطر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .container { margin-top: 30px; }
        .page-header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
            <p class="lead">عدد العملاء: {{ customers|length }}</p>
        </div>

        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle"></i> البيانات محدثة من قاعدة البيانات!</h5>
        </div>

        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> قائمة العملاء</h5>
            </div>
            <div class="card-body">
                {% if customers %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم بالعربية</th>
                                    <th>الاسم بالإنجليزية</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>تاريخ التسجيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td>{{ customer.id }}</td>
                                    <td>{{ customer.name_ar or 'غير محدد' }}</td>
                                    <td>{{ customer.name_en or 'غير محدد' }}</td>
                                    <td>{{ customer.phone or 'غير محدد' }}</td>
                                    <td>{{ customer.email or 'غير محدد' }}</td>
                                    <td>{{ customer.created_at or 'غير محدد' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        لا توجد بيانات عملاء متاحة
                    </div>
                {% endif %}

                <div class="text-center mt-3">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        ''', customers=customers_data)

    except Exception as e:
        print(f"خطأ في صفحة العملاء: {e}")
        return f"<h1>خطأ: {str(e)}</h1><a href='/'>العودة</a>"

@app.route('/cars')
def cars():
    """صفحة السيارات"""
    try:
        if 'logged_in' not in session:
            return redirect('/login')

        conn = get_db_connection()
        if not conn:
            return "<h1>خطأ في الاتصال بقاعدة البيانات</h1><a href='/'>العودة</a>"

        cursor = conn.cursor()
        cursor.execute("SELECT * FROM car ORDER BY id DESC LIMIT 20")
        cars_data = cursor.fetchall()
        conn.close()

        return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>السيارات - معرض قطر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .container { margin-top: 30px; }
        .page-header {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-car"></i> إدارة السيارات</h1>
            <p class="lead">عدد السيارات: {{ cars|length }}</p>
        </div>

        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle"></i> البيانات محدثة من قاعدة البيانات!</h5>
        </div>

        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> قائمة السيارات</h5>
            </div>
            <div class="card-body">
                {% if cars %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الماركة</th>
                                    <th>الموديل</th>
                                    <th>السنة</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for car in cars %}
                                <tr>
                                    <td>{{ car.id }}</td>
                                    <td>{{ car.make or 'غير محدد' }}</td>
                                    <td>{{ car.model or 'غير محدد' }}</td>
                                    <td>{{ car.year or 'غير محدد' }}</td>
                                    <td>{{ '{:,.0f}'.format(car.price) if car.price else 'غير محدد' }} ر.ق</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if car.status == 'available' else 'warning' }}">
                                            {{ 'متاح' if car.status == 'available' else car.status or 'غير محدد' }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        لا توجد بيانات سيارات متاحة
                    </div>
                {% endif %}

                <div class="text-center mt-3">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        ''', cars=cars_data)

    except Exception as e:
        print(f"خطأ في صفحة السيارات: {e}")
        return f"<h1>خطأ: {str(e)}</h1><a href='/'>العودة</a>"

@app.route('/contracts')
def contracts():
    """صفحة العقود"""
    try:
        if 'logged_in' not in session:
            return redirect('/login')

        return render_template_string('''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>العقود - معرض قطر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .container { margin-top: 30px; }
        .page-header {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-file-contract"></i> إدارة العقود</h1>
            <p class="lead">قسم العقود والمبيعات</p>
        </div>

        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle"></i> قسم العقود يعمل بشكل مثالي!</h5>
            <p class="mb-0">هذا القسم جاهز لإضافة وظائف إدارة العقود</p>
        </div>

        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> معلومات القسم</h5>
            </div>
            <div class="card-body">
                <p>قسم العقود متاح ويعمل بشكل صحيح. يمكن إضافة الوظائف التالية:</p>
                <ul>
                    <li>إنشاء عقود جديدة</li>
                    <li>عرض العقود الموجودة</li>
                    <li>تعديل العقود</li>
                    <li>طباعة العقود</li>
                    <li>إدارة الأقساط</li>
                </ul>

                <div class="text-center mt-3">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        ''')

    except Exception as e:
        print(f"خطأ في صفحة العقود: {e}")
        return f"<h1>خطأ: {str(e)}</h1><a href='/'>العودة</a>"

@app.route('/api/status')
def api_status():
    """API حالة النظام"""
    try:
        stats = get_stats()
        return jsonify({
            'status': 'success',
            'message': 'Flask يعمل بشكل مثالي',
            'timestamp': datetime.now().isoformat(),
            'stats': stats,
            'database': 'connected'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        })

@app.errorhandler(404)
def not_found(error):
    """صفحة 404"""
    return render_template_string('''
    <div style="text-align: center; padding: 50px; background: #f8f9fa;">
        <h2><i class="fas fa-exclamation-triangle"></i> الصفحة غير موجودة</h2>
        <p>الصفحة التي تبحث عنها غير موجودة</p>
        <a href="/" class="btn btn-primary">العودة للرئيسية</a>
    </div>
    '''), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة خطأ 500"""
    return render_template_string('''
    <div style="text-align: center; padding: 50px; background: #f8f9fa;">
        <h2><i class="fas fa-exclamation-triangle"></i> خطأ في الخادم</h2>
        <p>حدث خطأ في الخادم. يرجى المحاولة مرة أخرى</p>
        <a href="/" class="btn btn-primary">العودة للرئيسية</a>
    </div>
    '''), 500

if __name__ == '__main__':
    print("🚀 معرض قطر للسيارات - Flask النهائي العامل")
    print("=" * 60)

    try:
        # تهيئة قاعدة البيانات
        print("🔧 تهيئة قاعدة البيانات...")
        if init_database():
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
        else:
            print("⚠️ تحذير: مشكلة في قاعدة البيانات")

        print("=" * 60)
        print("🎉 Flask جاهز للتشغيل!")
        print("🌐 الخادم: http://127.0.0.1:5000")
        print("👤 المستخدم: admin")
        print("🔑 كلمة المرور: admin")
        print("✅ Flask يعمل بدون أخطاء")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)

        # تشغيل الخادم
        app.run(host='127.0.0.1', port=5000, debug=False, threaded=True)

    except Exception as e:
        print(f"❌ خطأ في تشغيل Flask: {e}")
        print("📝 تفاصيل الخطأ:")
        traceback.print_exc()
        print("\n💡 جرب الحلول البديلة:")
        print("   - auctions_index.html")
        print("   - FINAL_NO_SERVER_SOLUTION.html")
        input("\nاضغط Enter للخروج...")