<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}معرض قطر للسيارات{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Sidebar CSS -->
    <link href="{{ url_for('static', filename='css/sidebar.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="{{ url_for('index') }}" class="brand">
                <i class="fas fa-car"></i>
                <span>معرض قطر للسيارات</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <!-- Main Menu -->
            <div class="menu-section">
                <div class="menu-title">القائمة الرئيسية</div>
                <a href="{{ url_for('index') }}" class="menu-item">
                    <i class="fas fa-home"></i>
                    <span>الرئيسية</span>
                </a>
                <a href="{{ url_for('cars') }}" class="menu-item">
                    <i class="fas fa-car"></i>
                    <span>السيارات</span>
                </a>
                <a href="{{ url_for('premium_numbers') }}" class="menu-item">
                    <i class="fas fa-hashtag"></i>
                    <span>الأرقام المميزة</span>
                </a>
                <a href="{{ url_for('auction') }}" class="menu-item">
                    <i class="fas fa-gavel"></i>
                    <span>المزادات</span>
                </a>
                <a href="{{ url_for('customers') }}" class="menu-item">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
                <a href="/notifications" class="menu-item">
                    <i class="fas fa-bell"></i>
                    <span>الإشعارات</span>
                    <span class="badge notification-count">0</span>
                </a>
                <a href="/maintenance" class="menu-item">
                    <i class="fas fa-tools"></i>
                    <span>الصيانة والفحص</span>
                </a>
            </div>
            
            <!-- Contracts & Installments -->
            <div class="menu-section">
                <div class="menu-title">إدارة العقود</div>
                <a href="{{ url_for('contracts') }}" class="menu-item">
                    <i class="fas fa-file-contract"></i>
                    <span>العقود</span>
                </a>
                <a href="{{ url_for('installments_management') }}" class="menu-item">
                    <i class="fas fa-calendar-check"></i>
                    <span>الأقساط</span>
                </a>
                <a href="{{ url_for('installments_due_today') }}" class="menu-item">
                    <i class="fas fa-bell"></i>
                    <span>المستحقة اليوم</span>
                    <span class="badge">{{ due_today_count or 0 }}</span>
                </a>
                <a href="{{ url_for('installments_reports') }}" class="menu-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>تقارير الأقساط</span>
                </a>
            </div>
            
            <!-- Administration -->
            <div class="menu-section">
                <div class="menu-title">الإدارة</div>
                <a href="{{ url_for('user_profile') }}" class="menu-item">
                    <i class="fas fa-user"></i>
                    <span>الملف الشخصي</span>
                </a>
                <a href="{{ url_for('settings') }}" class="menu-item">
                    <i class="fas fa-cogs"></i>
                    <span>الإعدادات</span>
                </a>
                <a href="{{ url_for('admin_users') }}" class="menu-item">
                    <i class="fas fa-users-cog"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                <a href="{{ url_for('admin_panel') }}" class="menu-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </div>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{{ session.get('username', 'مستخدم') }}</div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
            <a href="{{ url_for('logout') }}" class="logout-btn" title="تسجيل الخروج">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- Top Bar -->
    <div class="topbar">
        <div class="topbar-left">
            <button class="sidebar-toggle-btn" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <div class="breadcrumb">
                <span class="current-page">{% block page_title %}الرئيسية{% endblock %}</span>
            </div>
        </div>
        <div class="topbar-right">
            <div class="notifications dropdown">
                <button class="notification-btn dropdown-toggle" type="button" id="notificationsDropdown"
                        data-bs-toggle="dropdown" aria-expanded="false" title="الإشعارات">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">0</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationsDropdown">
                    <li class="dropdown-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">الإشعارات</h6>
                        <a href="/notifications" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li class="notification-loading">
                        <div class="text-center p-3">
                            <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                        </div>
                    </li>
                    <li class="notification-empty" style="display: none;">
                        <div class="text-center p-3 text-muted">
                            <i class="fas fa-check-circle"></i><br>
                            لا توجد إشعارات جديدة
                        </div>
                    </li>
                    <div class="notification-items" style="display: none;">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                    <li><hr class="dropdown-divider notification-divider" style="display: none;"></li>
                    <li class="notification-footer" style="display: none;">
                        <div class="text-center p-2">
                            <a href="/notifications" class="btn btn-sm btn-primary w-100">
                                <i class="fas fa-eye"></i> عرض جميع الإشعارات
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="user-menu dropdown">
                <button class="user-btn dropdown-toggle" type="button" id="userDropdown"
                        data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="user-avatar-small">
                        <i class="fas fa-user"></i>
                    </div>
                    <span class="user-name">المستخدم</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><h6 class="dropdown-header">المستخدم</h6></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="/notifications">
                        <i class="fas fa-bell"></i> الإشعارات
                    </a></li>
                    <li><a class="dropdown-item" href="/settings">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="/logout">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Sidebar JS -->
    <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
    <!-- Scrolling Ticker JS -->
    <script src="{{ url_for('static', filename='js/ticker.js') }}"></script>
    <!-- Notifications JS -->
    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
