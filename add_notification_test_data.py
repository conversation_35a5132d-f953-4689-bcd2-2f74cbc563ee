#!/usr/bin/env python3
"""
إضافة بيانات اختبار للإشعارات - نظام معرض قطر للسيارات
"""

import sqlite3
import os
from datetime import datetime, date, timedelta

def add_notification_test_data():
    """إضافة بيانات اختبار للإشعارات"""
    db_path = 'working_database.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        today = date.today()
        
        print("🔧 إضافة بيانات اختبار للإشعارات...")
        
        # إضافة سيارات في الصيانة
        cursor.execute("""
            UPDATE car SET status = 'maintenance' 
            WHERE id IN (SELECT id FROM car LIMIT 2)
        """)
        print("✅ تم تحديث حالة سيارتين إلى 'في الصيانة'")
        
        # إضافة عقود معلقة (مسودة)
        cursor.execute("""
            UPDATE contract SET status = 'draft' 
            WHERE id IN (SELECT id FROM contract WHERE status != 'draft' LIMIT 1)
        """)
        print("✅ تم تحديث حالة عقد إلى 'مسودة'")
        
        # إضافة أقساط متأخرة
        yesterday = today - timedelta(days=1)
        week_ago = today - timedelta(days=7)
        
        # تحديث بعض الأقساط لتكون متأخرة
        cursor.execute("""
            UPDATE installment_payment 
            SET due_date = ?, status = 'pending'
            WHERE id IN (SELECT id FROM installment_payment WHERE status = 'pending' LIMIT 3)
        """, (yesterday,))
        
        cursor.execute("""
            UPDATE installment_payment 
            SET due_date = ?, status = 'pending'
            WHERE id IN (SELECT id FROM installment_payment WHERE status = 'pending' LIMIT 2)
        """, (week_ago,))
        
        print("✅ تم إضافة أقساط متأخرة")
        
        # إضافة أقساط مستحقة اليوم
        cursor.execute("""
            UPDATE installment_payment 
            SET due_date = ?, status = 'pending'
            WHERE id IN (SELECT id FROM installment_payment WHERE status = 'pending' LIMIT 2)
        """, (today,))
        
        print("✅ تم إضافة أقساط مستحقة اليوم")
        
        # إضافة سيارات ينتهي تأمينها قريباً
        insurance_expiry_soon = today + timedelta(days=15)
        cursor.execute("""
            UPDATE car 
            SET insurance_expiry = ?
            WHERE id IN (SELECT id FROM car LIMIT 2)
        """, (insurance_expiry_soon,))
        
        print("✅ تم إضافة سيارات ينتهي تأمينها قريباً")
        
        # إضافة سيارات ينتهي ترخيصها قريباً
        registration_expiry_soon = today + timedelta(days=20)
        cursor.execute("""
            UPDATE car 
            SET registration_expiry = ?
            WHERE id IN (SELECT id FROM car WHERE id NOT IN (
                SELECT id FROM car WHERE insurance_expiry = ?
            ) LIMIT 2)
        """, (registration_expiry_soon, insurance_expiry_soon))
        
        print("✅ تم إضافة سيارات ينتهي ترخيصها قريباً")
        
        # إضافة جدول المزادات إذا لم يكن موجوداً
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS auction (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    premium_number_id INTEGER NOT NULL,
                    starting_price FLOAT NOT NULL,
                    current_price FLOAT NOT NULL,
                    start_time DATETIME NOT NULL,
                    end_time DATETIME NOT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    winner_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
                    FOREIGN KEY (winner_id) REFERENCES customer (id)
                )
            """)
            
            # إضافة مزاد نشط
            auction_end = datetime.now() + timedelta(hours=24)
            cursor.execute("""
                INSERT INTO auction (premium_number_id, starting_price, current_price, 
                                   start_time, end_time, status)
                SELECT id, price * 0.8, price * 1.1, ?, ?, 'active'
                FROM premium_number 
                WHERE status = 'available' 
                LIMIT 1
            """, (datetime.now(), auction_end))
            
            print("✅ تم إضافة مزاد نشط")
            
        except Exception as e:
            print(f"⚠️ تحذير في إضافة المزاد: {e}")
        
        # إضافة عملاء جدد (آخر أسبوع)
        week_ago_datetime = datetime.now() - timedelta(days=3)
        cursor.execute("""
            UPDATE customer 
            SET created_at = ?
            WHERE id IN (SELECT id FROM customer LIMIT 2)
        """, (week_ago_datetime,))
        
        print("✅ تم إضافة عملاء جدد")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إضافة بيانات اختبار الإشعارات بنجاح!")
        print("\n📊 الإشعارات المتوقعة:")
        print("   🚨 أقساط متأخرة: 5")
        print("   📅 أقساط مستحقة اليوم: 2") 
        print("   🔧 سيارات في الصيانة: 2")
        print("   📋 عقود معلقة: 1")
        print("   🛡️ انتهاء تأمين قريباً: 2")
        print("   🆔 انتهاء ترخيص قريباً: 2")
        print("   🏆 مزادات نشطة: 1")
        print("   👥 عملاء جدد: 2")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة بيانات الاختبار: {e}")
        return False

if __name__ == '__main__':
    print("🚀 نظام معرض قطر للسيارات - إضافة بيانات اختبار الإشعارات")
    print("=" * 80)
    
    if add_notification_test_data():
        print("\n✅ تم إضافة بيانات اختبار الإشعارات بنجاح!")
        print("🔔 يمكنك الآن رؤية الإشعارات في أعلى الصفحة")
        print("🌐 قم بتحديث الصفحة لرؤية الإشعارات الجديدة")
    else:
        print("\n❌ فشل في إضافة بيانات اختبار الإشعارات")
