#!/usr/bin/env python3
"""
إنشاء عقود تجريبية مع أقساط لاختبار إدارة الأقساط المحسنة
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Contract, Car, Customer, InstallmentPayment
from datetime import datetime, timedelta
import random

def create_test_contracts():
    """إنشاء عقود تجريبية مع أقساط متنوعة"""
    with app.app_context():
        try:
            print("🎯 إنشاء عقود تجريبية مع أقساط...")
            
            # التحقق من وجود سيارات وعملاء
            cars = Car.query.filter_by(status='available').limit(5).all()
            customers = Customer.query.limit(5).all()
            
            if len(cars) < 3:
                print("⚠️ إنشاء سيارات تجريبية...")
                test_cars = [
                    Car(make='تويوتا', model='كامري', year=2023, price=120000, status='available'),
                    Car(make='نيسان', model='التيما', year=2022, price=95000, status='available'),
                    Car(make='هوندا', model='أكورد', year=2023, price=110000, status='available'),
                    Car(make='لكزس', model='ES350', year=2023, price=180000, status='available'),
                    Car(make='مرسيدس', model='C200', year=2023, price=200000, status='available'),
                ]
                for car in test_cars:
                    db.session.add(car)
                db.session.commit()
                cars = Car.query.filter_by(status='available').limit(5).all()
            
            if len(customers) < 3:
                print("⚠️ إنشاء عملاء تجريبيين...")
                test_customers = [
                    Customer(name_ar='أحمد محمد الكعبي', phone='+974 5555 1234', email='<EMAIL>'),
                    Customer(name_ar='فاطمة علي النعيمي', phone='+974 5555 2345', email='<EMAIL>'),
                    Customer(name_ar='محمد سالم الثاني', phone='+974 5555 3456', email='<EMAIL>'),
                    Customer(name_ar='نورا خالد المري', phone='+974 5555 4567', email='<EMAIL>'),
                    Customer(name_ar='عبدالله يوسف الدوسري', phone='+974 5555 5678', email='<EMAIL>'),
                ]
                for customer in test_customers:
                    db.session.add(customer)
                db.session.commit()
                customers = Customer.query.limit(5).all()
            
            # إنشاء عقود متنوعة
            contracts_data = [
                {
                    'customer_idx': 0,
                    'car_idx': 0,
                    'total_price': 120000,
                    'down_payment': 30000,
                    'installments_count': 12,
                    'status': 'active',
                    'description': 'عقد مع أقساط منتظمة'
                },
                {
                    'customer_idx': 1,
                    'car_idx': 1,
                    'total_price': 95000,
                    'down_payment': 20000,
                    'installments_count': 18,
                    'status': 'active',
                    'description': 'عقد مع بعض الأقساط المتأخرة'
                },
                {
                    'customer_idx': 2,
                    'car_idx': 2,
                    'total_price': 110000,
                    'down_payment': 25000,
                    'installments_count': 24,
                    'status': 'active',
                    'description': 'عقد مع دفعات جزئية'
                },
                {
                    'customer_idx': 3,
                    'car_idx': 3,
                    'total_price': 180000,
                    'down_payment': 50000,
                    'installments_count': 36,
                    'status': 'completed',
                    'description': 'عقد مكتمل الدفع'
                },
                {
                    'customer_idx': 4,
                    'car_idx': 4,
                    'total_price': 200000,
                    'down_payment': 40000,
                    'installments_count': 30,
                    'status': 'active',
                    'description': 'عقد جديد مع أقساط قادمة'
                }
            ]
            
            created_contracts = []
            
            for i, contract_data in enumerate(contracts_data):
                customer = customers[contract_data['customer_idx']]
                car = cars[contract_data['car_idx']]
                
                # إنشاء العقد
                remaining_amount = contract_data['total_price'] - contract_data['down_payment']
                installment_amount = remaining_amount / contract_data['installments_count']
                
                contract = Contract(
                    contract_number=f"C-2024-{1000 + i}",
                    customer_id=customer.id,
                    car_id=car.id,
                    total_price=contract_data['total_price'],
                    down_payment=contract_data['down_payment'],
                    remaining_amount=remaining_amount,
                    installments_count=contract_data['installments_count'],
                    installment_amount=installment_amount,
                    status=contract_data['status'],
                    notes=contract_data['description'],
                    created_at=datetime.now() - timedelta(days=random.randint(30, 180))
                )
                
                db.session.add(contract)
                db.session.flush()  # للحصول على contract.id
                
                # تحديث حالة السيارة
                car.status = 'sold'
                
                # إنشاء الأقساط
                start_date = contract.created_at.date() + timedelta(days=30)
                
                for installment_num in range(1, contract_data['installments_count'] + 1):
                    due_date = start_date + timedelta(days=30 * (installment_num - 1))
                    
                    installment = InstallmentPayment(
                        contract_id=contract.id,
                        installment_number=installment_num,
                        amount=installment_amount,
                        due_date=due_date,
                        status='pending',
                        paid_amount=0
                    )
                    
                    # محاكاة حالات مختلفة للأقساط
                    today = datetime.now().date()
                    
                    if contract_data['status'] == 'completed':
                        # عقد مكتمل - جميع الأقساط مدفوعة
                        installment.status = 'paid'
                        installment.paid_amount = installment_amount
                        installment.payment_date = due_date + timedelta(days=random.randint(-5, 5))
                        
                    elif due_date < today:
                        # أقساط مستحقة في الماضي
                        if i == 1:  # العقد الثاني - بعض الأقساط متأخرة
                            if installment_num <= 8:
                                installment.status = 'paid'
                                installment.paid_amount = installment_amount
                                installment.payment_date = due_date + timedelta(days=random.randint(-3, 10))
                            elif installment_num <= 10:
                                installment.status = 'partial'
                                installment.paid_amount = installment_amount * 0.6
                                installment.payment_date = due_date + timedelta(days=random.randint(5, 15))
                            # الباقي يبقى معلق (متأخر)
                            
                        elif i == 2:  # العقد الثالث - دفعات جزئية
                            if installment_num <= 6:
                                installment.status = 'paid'
                                installment.paid_amount = installment_amount
                                installment.payment_date = due_date + timedelta(days=random.randint(-2, 7))
                            elif installment_num <= 9:
                                installment.status = 'partial'
                                installment.paid_amount = installment_amount * random.uniform(0.3, 0.8)
                                installment.payment_date = due_date + timedelta(days=random.randint(2, 12))
                            # الباقي معلق
                            
                        else:  # العقود الأخرى - معظم الأقساط مدفوعة
                            if installment_num <= max(1, installment_num - 3):
                                installment.status = 'paid'
                                installment.paid_amount = installment_amount
                                installment.payment_date = due_date + timedelta(days=random.randint(-2, 5))
                    
                    db.session.add(installment)
                
                created_contracts.append(contract)
                print(f"✅ تم إنشاء العقد {contract.contract_number} للعميل {customer.name_ar}")
            
            # تحديث المبالغ المتبقية للعقود
            for contract in created_contracts:
                paid_total = sum(inst.paid_amount for inst in contract.installment_payments)
                contract.remaining_amount = contract.total_price - contract.down_payment - paid_total
            
            db.session.commit()
            
            print("\n" + "="*60)
            print("✅ تم إنشاء العقود التجريبية بنجاح!")
            print("="*60)
            
            for contract in created_contracts:
                installments = contract.installment_payments
                paid_count = len([i for i in installments if i.status == 'paid'])
                pending_count = len([i for i in installments if i.status == 'pending'])
                partial_count = len([i for i in installments if i.status == 'partial'])
                overdue_count = len([i for i in installments if i.status in ['pending', 'partial'] and i.due_date < datetime.now().date()])
                
                print(f"\n📋 العقد: {contract.contract_number}")
                print(f"   👤 العميل: {contract.customer.name_ar}")
                print(f"   🚗 السيارة: {contract.car.make} {contract.car.model}")
                print(f"   💰 إجمالي السعر: {contract.total_price:,.0f} ر.ق")
                print(f"   📊 الأقساط: {paid_count} مدفوع | {pending_count} معلق | {partial_count} جزئي | {overdue_count} متأخر")
                print(f"   💵 المتبقي: {contract.remaining_amount:,.0f} ر.ق")
            
            print(f"\n🌐 رابط إدارة الأقساط: http://127.0.0.1:9898/installments")
            print(f"📊 رابط التقارير: http://127.0.0.1:9898/installments/reports")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء العقود: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🏗️ إنشاء عقود تجريبية مع أقساط...")
    print("="*50)
    
    success = create_test_contracts()
    
    if success:
        print("\n🎉 تم إنشاء العقود بنجاح!")
        print("يمكنك الآن اختبار إدارة الأقساط المحسنة")
    else:
        print("\n❌ فشل في إنشاء العقود")
    
    input("\nاضغط Enter للخروج...")
