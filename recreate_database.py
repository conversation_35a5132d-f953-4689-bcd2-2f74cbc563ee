#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Customer, PremiumNumber, Car, Contract, Auction, Bid, User
from datetime import datetime, <PERSON><PERSON><PERSON>

def recreate_database():
    """إعادة إنشاء قاعدة البيانات بالكامل"""
    
    with app.app_context():
        print("🔧 إعادة إنشاء قاعدة البيانات...")
        
        # حذف جميع الجداول
        print("🗑️ حذف الجداول القديمة...")
        db.drop_all()
        
        # إنشاء جميع الجداول الجديدة
        print("🏗️ إنشاء الجداول الجديدة...")
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        print("👤 إنشاء مستخدم افتراضي...")
        admin_user = User(
            username='admin',
            password='admin',
            name='مدير النظام',
            role='admin'
        )
        db.session.add(admin_user)
        
        # إنشاء عملاء تجريبيين
        print("👥 إنشاء عملاء تجريبيين...")
        customers_data = [
            {
                'name_ar': 'أحمد محمد الكعبي',
                'name_en': 'Ahmed Mohammed Al-Kaabi',
                'phone': '50123456',
                'email': '<EMAIL>',
                'id_number': '12345678901',
                'nationality': 'قطري',
                'city': 'الدوحة'
            },
            {
                'name_ar': 'فاطمة علي الأنصاري',
                'name_en': 'Fatima Ali Al-Ansari',
                'phone': '50123457',
                'email': '<EMAIL>',
                'id_number': '12345678902',
                'nationality': 'قطري',
                'city': 'الدوحة'
            },
            {
                'name_ar': 'محمد سالم المري',
                'name_en': 'Mohammed Salem Al-Marri',
                'phone': '50123458',
                'email': '<EMAIL>',
                'id_number': '12345678903',
                'nationality': 'قطري',
                'city': 'الدوحة'
            }
        ]
        
        for customer_data in customers_data:
            customer = Customer(**customer_data)
            db.session.add(customer)
        
        # إنشاء أرقام مميزة
        print("🔢 إنشاء أرقام مميزة...")
        premium_numbers_data = [
            {'number': '777', 'category': 'VIP', 'price': 300000, 'status': 'auction'},
            {'number': '999', 'category': 'VIP', 'price': 450000, 'status': 'auction'},
            {'number': '555', 'category': 'Premium', 'price': 180000, 'status': 'auction'},
            {'number': '123', 'category': 'Standard', 'price': 50000, 'status': 'available'},
            {'number': '456', 'category': 'Standard', 'price': 75000, 'status': 'available'}
        ]
        
        for pn_data in premium_numbers_data:
            premium_number = PremiumNumber(**pn_data)
            db.session.add(premium_number)
        
        # إنشاء سيارات تجريبية
        print("🚗 إنشاء سيارات تجريبية...")
        cars_data = [
            {
                'make': 'تويوتا',
                'model': 'كامري',
                'year': 2023,
                'price': 150000,
                'color': 'أبيض',
                'fuel_type': 'بنزين',
                'transmission': 'أوتوماتيك',
                'mileage': 5000,
                'status': 'available'
            },
            {
                'make': 'نيسان',
                'model': 'التيما',
                'year': 2023,
                'price': 140000,
                'color': 'أسود',
                'fuel_type': 'بنزين',
                'transmission': 'أوتوماتيك',
                'mileage': 3000,
                'status': 'available'
            }
        ]
        
        for car_data in cars_data:
            car = Car(**car_data)
            db.session.add(car)
        
        # حفظ جميع البيانات
        db.session.commit()
        
        # إنشاء مزادات تجريبية
        print("🎯 إنشاء مزادات تجريبية...")
        
        # الحصول على الأرقام المميزة
        pn_777 = PremiumNumber.query.filter_by(number='777').first()
        pn_999 = PremiumNumber.query.filter_by(number='999').first()
        pn_555 = PremiumNumber.query.filter_by(number='555').first()
        
        if pn_777:
            auction_777 = Auction(
                premium_number_id=pn_777.id,
                start_time=datetime.now() - timedelta(minutes=30),
                end_time=datetime.now() + timedelta(minutes=30),
                starting_price=250000,
                current_price=303000,
                status='active'
            )
            db.session.add(auction_777)
        
        if pn_999:
            auction_999 = Auction(
                premium_number_id=pn_999.id,
                start_time=datetime.now() - timedelta(minutes=20),
                end_time=datetime.now() + timedelta(minutes=40),
                starting_price=400000,
                current_price=450000,
                status='active'
            )
            db.session.add(auction_999)
        
        if pn_555:
            auction_555 = Auction(
                premium_number_id=pn_555.id,
                start_time=datetime.now() - timedelta(minutes=45),
                end_time=datetime.now() + timedelta(minutes=15),
                starting_price=150000,
                current_price=180000,
                status='active'
            )
            db.session.add(auction_555)
        
        # حفظ المزادات
        db.session.commit()
        
        # إنشاء مزايدات تجريبية
        print("💰 إنشاء مزايدات تجريبية...")
        
        customers = Customer.query.all()
        auctions = Auction.query.all()
        
        if customers and auctions:
            for auction in auctions:
                for i, customer in enumerate(customers[:2]):  # أول عميلين فقط
                    bid = Bid(
                        auction_id=auction.id,
                        customer_id=customer.id,
                        amount=auction.current_price - (1000 * (i + 1)),
                        bid_time=datetime.now() - timedelta(minutes=10 - i)
                    )
                    db.session.add(bid)
        
        # حفظ نهائي
        db.session.commit()
        
        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        print(f"👥 العملاء: {Customer.query.count()}")
        print(f"🔢 الأرقام المميزة: {PremiumNumber.query.count()}")
        print(f"🚗 السيارات: {Car.query.count()}")
        print(f"🎯 المزادات: {Auction.query.count()}")
        print(f"💰 المزايدات: {Bid.query.count()}")

if __name__ == '__main__':
    print("🚀 إعادة إنشاء قاعدة البيانات")
    print("=" * 50)
    
    try:
        recreate_database()
        print("=" * 50)
        print("✅ تم الانتهاء بنجاح!")
        print("🌐 يمكنك الآن تشغيل: python working_app.py")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("💡 تأكد من أن جميع الملفات موجودة")
