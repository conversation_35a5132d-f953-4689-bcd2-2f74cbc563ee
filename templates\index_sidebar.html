{% extends "base_sidebar.html" %}

{% block title %}الرئيسية - معرض قطر للسيارات{% endblock %}
{% block page_title %}الرئيسية{% endblock %}

{% block extra_css %}
<!-- رسالة تشخيصية لتأكيد استخدام القالب الصحيح -->
<!-- DEBUG: index_sidebar.html template is being used -->
<style>
    .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 8px;
        overflow: hidden;
        min-height: 100px;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .success-banner {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }

    .feature-card {
        border-radius: 8px;
        border: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        transition: transform 0.2s ease;
    }

    .feature-card:hover {
        transform: translateY(-1px);
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
    }

    .stat-title {
        font-size: 0.85rem;
        margin-bottom: 0.3rem;
    }
</style>
{% endblock %}

{% block content %}

<!-- Welcome Banner -->
<div class="success-banner">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h5><i class="fas fa-check-circle"></i> مرحباً بك في معرض قطر للسيارات!</h5>
            <p class="mb-0 small">✅ النظام الكامل مع دعم الأرقام المميزة والأقساط والمزادات</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('add_contract') }}" class="btn btn-light">
                <i class="fas fa-plus"></i> إضافة عقد جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-3">
    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card stat-card bg-primary text-white">
            <div class="card-body text-center py-2">
                <i class="fas fa-car mb-1" style="font-size: 1.5rem;"></i>
                <div class="stat-number">{{ total_cars }}</div>
                <div class="stat-title">إجمالي السيارات</div>
                <small>متاح: {{ available_cars }}</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card stat-card bg-success text-white">
            <div class="card-body text-center py-2">
                <i class="fas fa-hashtag mb-1" style="font-size: 1.5rem;"></i>
                <div class="stat-number">{{ total_premium_numbers }}</div>
                <div class="stat-title">الأرقام المميزة</div>
                <small>متاح: {{ available_premium_numbers }}</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card stat-card bg-info text-white">
            <div class="card-body text-center py-2">
                <i class="fas fa-file-contract mb-1" style="font-size: 1.5rem;"></i>
                <div class="stat-number">{{ total_contracts }}</div>
                <div class="stat-title">إجمالي العقود</div>
                <small>نشط: {{ total_contracts }}</small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-2">
        <div class="card stat-card bg-warning text-white">
            <div class="card-body text-center py-2">
                <i class="fas fa-gavel mb-1" style="font-size: 1.5rem;"></i>
                <div class="stat-number">{{ total_auctions }}</div>
                <div class="stat-title">إجمالي المزادات</div>
                <small>نشط: {{ active_auctions }}</small>
            </div>
        </div>
    </div>
</div>



<!-- Quick Actions -->
<div class="row mb-3">
    <div class="col-md-6 mb-3">
        <div class="card feature-card">
            <div class="card-header bg-primary text-white py-2">
                <h6 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
            </div>
            <div class="card-body py-2">
                <div class="row">
                    <div class="col-6 mb-2">
                        <a href="{{ url_for('add_contract') }}" class="btn btn-outline-primary w-100 btn-sm">
                            <i class="fas fa-plus"></i> عقد جديد
                        </a>
                    </div>
                    <div class="col-6 mb-2">
                        <a href="{{ url_for('cars') }}" class="btn btn-outline-success w-100 btn-sm">
                            <i class="fas fa-car"></i> السيارات
                        </a>
                    </div>
                    <div class="col-6 mb-2">
                        <a href="{{ url_for('premium_numbers') }}" class="btn btn-outline-info w-100 btn-sm">
                            <i class="fas fa-hashtag"></i> الأرقام المميزة
                        </a>
                    </div>
                    <div class="col-6 mb-2">
                        <a href="/create_car_auction" class="btn btn-outline-warning w-100 btn-sm">
                            <i class="fas fa-gavel"></i> مزاد سيارة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-3">
        <div class="card feature-card">
            <div class="card-header bg-warning text-dark py-2">
                <h6 class="mb-0"><i class="fas fa-bell"></i> تنبيهات مهمة</h6>
            </div>
            <div class="card-body py-2">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <div class="border-end">
                            <h5 class="text-danger mb-0">{{ due_today_count }}</h5>
                            <small class="text-muted">أقساط اليوم</small>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <h5 class="text-info mb-0">{{ cars_in_maintenance|length if cars_in_maintenance else 0 }}</h5>
                        <small class="text-muted">سيارات صيانة</small>
                    </div>
                </div>
                <div class="text-center">
                    <a href="{{ url_for('installments_management') }}" class="btn btn-outline-danger btn-sm me-1">
                        <i class="fas fa-calendar-check"></i> الأقساط
                    </a>
                    <a href="/auction" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-fire"></i> المزادات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Important Notifications (Simplified) -->
{% if overdue_payments or due_today_payments %}
<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-warning py-2">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-1"><i class="fas fa-bell"></i> تنبيهات مهمة</h6>
                    {% if overdue_payments %}
                        <span class="badge bg-danger me-2">{{ overdue_payments|length }} أقساط متأخرة</span>
                    {% endif %}
                    {% if due_today_payments %}
                        <span class="badge bg-warning">{{ due_today_payments|length }} أقساط مستحقة اليوم</span>
                    {% endif %}
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('installments_management') }}" class="btn btn-outline-dark btn-sm">
                        <i class="fas fa-eye"></i> عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}


{% endblock %}

{% block extra_js %}
<script>
// Simple animations for stat cards
document.addEventListener('DOMContentLoaded', function() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(10px)';

        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
