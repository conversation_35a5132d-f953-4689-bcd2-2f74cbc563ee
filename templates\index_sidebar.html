{% extends "base_sidebar.html" %}

{% block title %}الرئيسية - معرض قطر للسيارات{% endblock %}
{% block page_title %}الرئيسية{% endblock %}

{% block extra_css %}
<style>
    .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 15px;
        overflow: hidden;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stat-icon {
        font-size: 3rem;
        opacity: 0.8;
    }
    
    .success-banner {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .feature-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-3px);
    }
    
    .recent-car-card {
        border-radius: 10px;
        transition: transform 0.2s ease;
    }
    
    .recent-car-card:hover {
        transform: scale(1.02);
    }
    
    .quick-action-btn {
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .quick-action-btn:hover {
        transform: translateX(-5px);
    }
</style>
{% endblock %}

{% block content %}
<!-- Success Banner -->
<div class="success-banner">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h3><i class="fas fa-check-circle"></i> مرحباً بك في معرض قطر للسيارات!</h3>
            <p class="mb-0">✅ النظام الكامل مع دعم الأرقام المميزة والأقساط والمزادات</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('add_contract') }}" class="btn btn-light btn-lg">
                <i class="fas fa-plus"></i> إضافة عقد جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-primary text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-car stat-icon mb-3"></i>
                <h3 class="mb-2">{{ total_cars }}</h3>
                <h6 class="mb-3">إجمالي السيارات</h6>
                <div class="row text-center">
                    <div class="col-6">
                        <small>متاح</small>
                        <div class="fw-bold">{{ available_cars }}</div>
                    </div>
                    <div class="col-6">
                        <small>مباع</small>
                        <div class="fw-bold">{{ sold_cars }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-success text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-hashtag stat-icon mb-3"></i>
                <h3 class="mb-2">{{ total_premium_numbers }}</h3>
                <h6 class="mb-3">الأرقام المميزة</h6>
                <div class="text-center">
                    <small>متاح للبيع</small>
                    <div class="fw-bold">{{ available_premium_numbers }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-info text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-file-contract stat-icon mb-3"></i>
                <h3 class="mb-2">{{ total_contracts }}</h3>
                <h6 class="mb-3">إجمالي العقود</h6>
                <div class="text-center">
                    <small>عقود نشطة</small>
                    <div class="fw-bold">{{ total_contracts }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card bg-warning text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check stat-icon mb-3"></i>
                <h3 class="mb-2">{{ due_today_count }}</h3>
                <h6 class="mb-3">أقساط مستحقة اليوم</h6>
                <div class="text-center">
                    {% if due_today_count > 0 %}
                        <a href="{{ url_for('installments_due_today') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </a>
                    {% else %}
                        <small>لا توجد أقساط مستحقة</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Features -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card feature-card h-100">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <a href="{{ url_for('add_contract') }}" class="btn btn-outline-primary w-100 quick-action-btn">
                    <i class="fas fa-plus"></i> إنشاء عقد جديد
                </a>
                <a href="{{ url_for('cars') }}" class="btn btn-outline-success w-100 quick-action-btn">
                    <i class="fas fa-car"></i> إدارة السيارات
                </a>
                <a href="{{ url_for('premium_numbers') }}" class="btn btn-outline-info w-100 quick-action-btn">
                    <i class="fas fa-hashtag"></i> الأرقام المميزة
                </a>
                <a href="{{ url_for('installments_management') }}" class="btn btn-outline-warning w-100 quick-action-btn">
                    <i class="fas fa-calendar-check"></i> إدارة الأقساط
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card feature-card h-100">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-check"></i> الميزات المتاحة</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-check text-success"></i> بيع السيارات</li>
                    <li class="mb-2"><i class="fas fa-check text-success"></i> بيع الأرقام المميزة</li>
                    <li class="mb-2"><i class="fas fa-check text-success"></i> العقود المدمجة</li>
                    <li class="mb-2"><i class="fas fa-check text-success"></i> نظام الأقساط</li>
                    <li class="mb-2"><i class="fas fa-check text-success"></i> المزادات المتقدمة</li>
                    <li class="mb-2"><i class="fas fa-check text-success"></i> التقارير والإحصائيات</li>
                    <li class="mb-2"><i class="fas fa-check text-success"></i> إدارة المستخدمين</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card feature-card h-100">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary">{{ total_cars }}</h4>
                            <small class="text-muted">السيارات</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ total_premium_numbers }}</h4>
                        <small class="text-muted">الأرقام المميزة</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info">{{ total_contracts }}</h4>
                            <small class="text-muted">العقود</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ due_today_count }}</h4>
                        <small class="text-muted">مستحق اليوم</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Cars Section -->
{% if recent_cars %}
<div class="row">
    <div class="col-12">
        <div class="card feature-card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-clock"></i> أحدث السيارات المضافة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for car in recent_cars %}
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card recent-car-card border-primary">
                            <div class="card-body">
                                <h6 class="card-title">{{ car.make }} {{ car.model }}</h6>
                                <p class="card-text">
                                    <small class="text-muted">{{ car.year }}</small><br>
                                    <strong class="text-success">{{ "{:,.0f}".format(car.price) }} ر.ق</strong><br>
                                    {% if car.status == 'available' %}
                                        <span class="badge bg-success">متاح</span>
                                    {% elif car.status == 'sold' %}
                                        <span class="badge bg-danger">مباع</span>
                                    {% elif car.status == 'reserved' %}
                                        <span class="badge bg-warning">محجوز</span>
                                    {% elif car.status == 'maintenance' %}
                                        <span class="badge bg-info">صيانة</span>
                                    {% endif %}
                                </p>
                                <a href="{{ url_for('view_car', car_id=car.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('cars') }}" class="btn btn-primary">
                        <i class="fas fa-car"></i> عرض جميع السيارات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/timer-controls.js') }}"></script>
<script>
// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Animate stat cards on load
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Update notification count every 30 seconds
    setInterval(function() {
        fetch('/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                // Update due today count if it exists
                const dueTodayElements = document.querySelectorAll('.due-today-count');
                dueTodayElements.forEach(element => {
                    element.textContent = data.due_today || 0;
                });
            })
            .catch(error => console.log('Could not update notifications'));
    }, 30000);
});
</script>
{% endblock %}
