/**
 * أدوات التحكم في الوقت للمزادات
 * إيقاف/تشغيل العدادات والمؤقتات
 */

class TimerControls {
    constructor() {
        this.isPaused = false;
        this.intervals = new Map();
        this.timeouts = new Map();
        this.originalIntervals = [];
        this.originalTimeouts = [];
        this.init();
    }

    init() {
        this.createControlButton();
        this.bindEvents();
        this.interceptTimers();
    }

    createControlButton() {
        // تم إزالة زر التحكم في الوقت بناءً على طلب المستخدم
        // لا يتم إنشاء أي أزرار تحكم في الوقت
        return;
    }

    bindEvents() {
        // الاستماع لأحداث الوقت
        document.addEventListener('timerPause', () => {
            this.pauseAllTimers();
        });

        document.addEventListener('timerResume', () => {
            this.resumeAllTimers();
        });

        // تم إزالة اختصار لوحة المفاتيح للتحكم في الوقت
    }

    interceptTimers() {
        // حفظ الدوال الأصلية
        this.originalSetInterval = window.setInterval;
        this.originalSetTimeout = window.setTimeout;
        this.originalClearInterval = window.clearInterval;
        this.originalClearTimeout = window.clearTimeout;

        const self = this;

        // تعديل setInterval
        window.setInterval = function(callback, delay, ...args) {
            const id = self.originalSetInterval.call(window, function() {
                if (!self.isPaused) {
                    callback.apply(this, args);
                }
            }, delay);
            
            self.intervals.set(id, {
                callback: callback,
                delay: delay,
                args: args,
                active: true
            });
            
            return id;
        };

        // تعديل setTimeout
        window.setTimeout = function(callback, delay, ...args) {
            if (self.isPaused) {
                // إذا كان الوقت متوقف، لا تنفذ timeout
                return null;
            }
            
            const id = self.originalSetTimeout.call(window, callback, delay, ...args);
            self.timeouts.set(id, {
                callback: callback,
                delay: delay,
                args: args
            });
            
            return id;
        };

        // تعديل clearInterval
        window.clearInterval = function(id) {
            self.intervals.delete(id);
            return self.originalClearInterval.call(window, id);
        };

        // تعديل clearTimeout
        window.clearTimeout = function(id) {
            self.timeouts.delete(id);
            return self.originalClearTimeout.call(window, id);
        };
    }

    toggleTimer() {
        // تم إزالة وظيفة تبديل الوقت - لا تعمل بعد الآن
        return;
    }

    pauseTimer() {
        this.isPaused = true;

        // إيقاف جميع العدادات المرئية
        this.pauseAllTimers();

        // إشعار المستخدم
        this.showNotification('تم إيقاف جميع المؤقتات', 'warning');
    }

    resumeTimer() {
        this.isPaused = false;

        // تشغيل جميع العدادات
        this.resumeAllTimers();

        // إشعار المستخدم
        this.showNotification('تم تشغيل جميع المؤقتات', 'success');
    }

    pauseAllTimers() {
        // إيقاف العدادات المرئية
        const timers = document.querySelectorAll(
            '[id*="countdown"], [id*="timer"], .countdown, .timer, .timer-display'
        );
        
        timers.forEach(timer => {
            timer.classList.add('timer-paused', 'countdown-paused');
        });

        // إيقاف الحركات
        const animations = document.querySelectorAll('[style*="animation"]');
        animations.forEach(element => {
            element.style.animationPlayState = 'paused';
        });
    }

    resumeAllTimers() {
        // تشغيل العدادات المرئية
        const timers = document.querySelectorAll(
            '[id*="countdown"], [id*="timer"], .countdown, .timer, .timer-display'
        );
        
        timers.forEach(timer => {
            timer.classList.remove('timer-paused', 'countdown-paused');
        });

        // تشغيل الحركات
        const animations = document.querySelectorAll('[style*="animation"]');
        animations.forEach(element => {
            element.style.animationPlayState = 'running';
        });
    }

    showNotification(message, type = 'info') {
        // إنشاء إشعار
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} timer-notification`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'info'}-circle"></i>
            ${message}
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 120px;
            left: 20px;
            z-index: 9999;
            min-width: 250px;
            border-radius: 10px;
            animation: slideInLeft 0.3s ease;
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.animation = 'slideOutLeft 0.3s ease';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    // دالة لإيقاف مزاد محدد
    pauseAuction(auctionId) {
        const auctionElements = document.querySelectorAll(`[data-auction-id="${auctionId}"]`);
        auctionElements.forEach(element => {
            const timers = element.querySelectorAll('.countdown, .timer');
            timers.forEach(timer => {
                timer.classList.add('timer-paused');
            });
        });
    }

    // دالة لتشغيل مزاد محدد
    resumeAuction(auctionId) {
        const auctionElements = document.querySelectorAll(`[data-auction-id="${auctionId}"]`);
        auctionElements.forEach(element => {
            const timers = element.querySelectorAll('.countdown, .timer');
            timers.forEach(timer => {
                timer.classList.remove('timer-paused');
            });
        });
    }

    // دالة للحصول على حالة الوقت
    getTimerStatus() {
        return {
            isPaused: this.isPaused,
            activeIntervals: this.intervals.size,
            activeTimeouts: this.timeouts.size
        };
    }
}

// إضافة CSS للحركات
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideInLeft {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutLeft {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(-100%); opacity: 0; }
    }
`;
document.head.appendChild(animationStyle);

// تهيئة أدوات التحكم في الوقت
let timerControls;
document.addEventListener('DOMContentLoaded', () => {
    timerControls = new TimerControls();
    
    // إضافة إلى النافذة العامة للوصول السهل
    window.timerControls = timerControls;
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimerControls;
}
