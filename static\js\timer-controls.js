/**
 * أدوات التحكم في الوقت للمزادات
 * إيقاف/تشغيل العدادات والمؤقتات
 */

class TimerControls {
    constructor() {
        this.isPaused = false;
        this.intervals = new Map();
        this.timeouts = new Map();
        this.originalIntervals = [];
        this.originalTimeouts = [];
        this.init();
    }

    init() {
        this.createControlButton();
        this.bindEvents();
        this.interceptTimers();
    }

    createControlButton() {
        // إنشاء زر التحكم في الوقت
        const controlBtn = document.createElement('div');
        controlBtn.id = 'timer-control-btn';
        controlBtn.className = 'timer-control-button';
        controlBtn.innerHTML = `
            <button class="btn btn-primary btn-sm" onclick="timerControls.toggleTimer()" title="إيقاف/تشغيل الوقت">
                <i class="fas fa-clock" id="timer-icon"></i>
                <span id="timer-text">إيقاف الوقت</span>
            </button>
        `;
        
        // إضافة CSS
        const style = document.createElement('style');
        style.textContent = `
            .timer-control-button {
                position: fixed;
                top: 60px;
                left: 20px;
                z-index: 1001;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 25px;
                padding: 5px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(10px);
            }
            
            .timer-control-button .btn {
                border-radius: 20px;
                font-weight: bold;
                transition: all 0.3s ease;
            }
            
            .timer-control-button .btn:hover {
                transform: scale(1.05);
            }
            
            .timer-paused {
                animation-play-state: paused !important;
                opacity: 0.5 !important;
                filter: grayscale(50%) !important;
            }
            
            .countdown-paused {
                color: #6c757d !important;
                background: #f8f9fa !important;
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(controlBtn);
    }

    bindEvents() {
        // الاستماع لأحداث الوقت
        document.addEventListener('timerPause', () => {
            this.pauseAllTimers();
        });

        document.addEventListener('timerResume', () => {
            this.resumeAllTimers();
        });

        // اختصار لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === ' ') {
                e.preventDefault();
                this.toggleTimer();
            }
        });
    }

    interceptTimers() {
        // حفظ الدوال الأصلية
        this.originalSetInterval = window.setInterval;
        this.originalSetTimeout = window.setTimeout;
        this.originalClearInterval = window.clearInterval;
        this.originalClearTimeout = window.clearTimeout;

        const self = this;

        // تعديل setInterval
        window.setInterval = function(callback, delay, ...args) {
            const id = self.originalSetInterval.call(window, function() {
                if (!self.isPaused) {
                    callback.apply(this, args);
                }
            }, delay);
            
            self.intervals.set(id, {
                callback: callback,
                delay: delay,
                args: args,
                active: true
            });
            
            return id;
        };

        // تعديل setTimeout
        window.setTimeout = function(callback, delay, ...args) {
            if (self.isPaused) {
                // إذا كان الوقت متوقف، لا تنفذ timeout
                return null;
            }
            
            const id = self.originalSetTimeout.call(window, callback, delay, ...args);
            self.timeouts.set(id, {
                callback: callback,
                delay: delay,
                args: args
            });
            
            return id;
        };

        // تعديل clearInterval
        window.clearInterval = function(id) {
            self.intervals.delete(id);
            return self.originalClearInterval.call(window, id);
        };

        // تعديل clearTimeout
        window.clearTimeout = function(id) {
            self.timeouts.delete(id);
            return self.originalClearTimeout.call(window, id);
        };
    }

    toggleTimer() {
        if (this.isPaused) {
            this.resumeTimer();
        } else {
            this.pauseTimer();
        }
    }

    pauseTimer() {
        this.isPaused = true;
        
        // تحديث واجهة الزر
        const icon = document.getElementById('timer-icon');
        const text = document.getElementById('timer-text');
        if (icon) icon.className = 'fas fa-play';
        if (text) text.textContent = 'تشغيل الوقت';

        // إيقاف جميع العدادات المرئية
        this.pauseAllTimers();
        
        // إشعار المستخدم
        this.showNotification('تم إيقاف جميع المؤقتات', 'warning');
    }

    resumeTimer() {
        this.isPaused = false;
        
        // تحديث واجهة الزر
        const icon = document.getElementById('timer-icon');
        const text = document.getElementById('timer-text');
        if (icon) icon.className = 'fas fa-pause';
        if (text) text.textContent = 'إيقاف الوقت';

        // تشغيل جميع العدادات
        this.resumeAllTimers();
        
        // إشعار المستخدم
        this.showNotification('تم تشغيل جميع المؤقتات', 'success');
    }

    pauseAllTimers() {
        // إيقاف العدادات المرئية
        const timers = document.querySelectorAll(
            '[id*="countdown"], [id*="timer"], .countdown, .timer, .timer-display'
        );
        
        timers.forEach(timer => {
            timer.classList.add('timer-paused', 'countdown-paused');
        });

        // إيقاف الحركات
        const animations = document.querySelectorAll('[style*="animation"]');
        animations.forEach(element => {
            element.style.animationPlayState = 'paused';
        });
    }

    resumeAllTimers() {
        // تشغيل العدادات المرئية
        const timers = document.querySelectorAll(
            '[id*="countdown"], [id*="timer"], .countdown, .timer, .timer-display'
        );
        
        timers.forEach(timer => {
            timer.classList.remove('timer-paused', 'countdown-paused');
        });

        // تشغيل الحركات
        const animations = document.querySelectorAll('[style*="animation"]');
        animations.forEach(element => {
            element.style.animationPlayState = 'running';
        });
    }

    showNotification(message, type = 'info') {
        // إنشاء إشعار
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} timer-notification`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'info'}-circle"></i>
            ${message}
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 120px;
            left: 20px;
            z-index: 9999;
            min-width: 250px;
            border-radius: 10px;
            animation: slideInLeft 0.3s ease;
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.animation = 'slideOutLeft 0.3s ease';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    // دالة لإيقاف مزاد محدد
    pauseAuction(auctionId) {
        const auctionElements = document.querySelectorAll(`[data-auction-id="${auctionId}"]`);
        auctionElements.forEach(element => {
            const timers = element.querySelectorAll('.countdown, .timer');
            timers.forEach(timer => {
                timer.classList.add('timer-paused');
            });
        });
    }

    // دالة لتشغيل مزاد محدد
    resumeAuction(auctionId) {
        const auctionElements = document.querySelectorAll(`[data-auction-id="${auctionId}"]`);
        auctionElements.forEach(element => {
            const timers = element.querySelectorAll('.countdown, .timer');
            timers.forEach(timer => {
                timer.classList.remove('timer-paused');
            });
        });
    }

    // دالة للحصول على حالة الوقت
    getTimerStatus() {
        return {
            isPaused: this.isPaused,
            activeIntervals: this.intervals.size,
            activeTimeouts: this.timeouts.size
        };
    }
}

// إضافة CSS للحركات
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideInLeft {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutLeft {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(-100%); opacity: 0; }
    }
`;
document.head.appendChild(animationStyle);

// تهيئة أدوات التحكم في الوقت
let timerControls;
document.addEventListener('DOMContentLoaded', () => {
    timerControls = new TimerControls();
    
    // إضافة إلى النافذة العامة للوصول السهل
    window.timerControls = timerControls;
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimerControls;
}
