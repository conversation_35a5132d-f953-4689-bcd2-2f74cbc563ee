#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import webbrowser
import threading
import time
import os
import sqlite3
from urllib.parse import parse_qs, urlparse

class QatarShowroomHandler(http.server.SimpleHTTPRequestHandler):
    """معالج طلبات HTTP بسيط لمعرض قطر للسيارات"""
    
    def do_GET(self):
        """معالجة طلبات GET"""
        try:
            path = self.path.split('?')[0]
            
            if path == '/' or path == '/index.html':
                self.send_main_page()
            elif path == '/login':
                self.send_login_page()
            elif path == '/customers':
                self.send_customers_page()
            elif path == '/contracts':
                self.send_contracts_page()
            elif path == '/auctions':
                self.send_auctions_page()
            elif path == '/status':
                self.send_status_page()
            else:
                # محاولة تقديم الملفات الثابتة
                super().do_GET()
                
        except Exception as e:
            self.send_error_page(f"خطأ في الخادم: {e}")
    
    def do_POST(self):
        """معالجة طلبات POST"""
        try:
            if self.path == '/login':
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length).decode('utf-8')
                params = parse_qs(post_data)
                
                username = params.get('username', [''])[0]
                password = params.get('password', [''])[0]
                
                if username == 'admin' and password == 'admin':
                    # إعادة توجيه للصفحة الرئيسية
                    self.send_response(302)
                    self.send_header('Location', '/')
                    self.end_headers()
                else:
                    self.send_login_page(error="اسم المستخدم أو كلمة المرور غير صحيحة")
            else:
                self.send_error_page("طلب غير مدعوم")
                
        except Exception as e:
            self.send_error_page(f"خطأ في معالجة الطلب: {e}")
    
    def get_database_stats(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not os.path.exists('auction.db'):
                return {'customers': 0, 'contracts': 0, 'auctions': 0, 'error': 'قاعدة البيانات غير موجودة'}
            
            conn = sqlite3.connect('auction.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM customer")
            customers_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM contract")
            contracts_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM auction")
            auctions_count = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'customers': customers_count,
                'contracts': contracts_count,
                'auctions': auctions_count,
                'error': None
            }
            
        except Exception as e:
            return {'customers': 0, 'contracts': 0, 'auctions': 0, 'error': str(e)}
    
    def send_main_page(self):
        """إرسال الصفحة الرئيسية"""
        stats = self.get_database_stats()
        
        html = f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>معرض قطر للسيارات - النظام البسيط</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {{ 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                    min-height: 100vh; 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }}
                .main-container {{ 
                    background: rgba(255,255,255,0.95); 
                    border-radius: 20px; 
                    padding: 40px; 
                    margin: 30px auto; 
                    max-width: 1000px; 
                    box-shadow: 0 25px 50px rgba(0,0,0,0.15); 
                }}
                .stat-card {{ 
                    border-radius: 15px; 
                    padding: 25px; 
                    margin: 15px; 
                    text-align: center; 
                    color: white; 
                    transition: transform 0.3s ease;
                }}
                .stat-card:hover {{ transform: translateY(-5px); }}
                .success-alert {{ 
                    background: #d4edda; 
                    border: 1px solid #c3e6cb; 
                    border-radius: 10px; 
                    padding: 20px; 
                    margin: 20px 0; 
                }}
                .btn-custom {{
                    padding: 12px 25px;
                    border-radius: 25px;
                    font-weight: bold;
                    text-decoration: none;
                    margin: 10px 5px;
                    transition: all 0.3s ease;
                    display: inline-block;
                }}
                .btn-custom:hover {{
                    transform: scale(1.05);
                    text-decoration: none;
                }}
            </style>
        </head>
        <body>
            <div class="main-container">
                <div class="text-center mb-4">
                    <h1><i class="fas fa-car text-primary"></i> معرض قطر للسيارات</h1>
                    <p class="lead">النظام البسيط - يعمل بدون أخطاء!</p>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <strong>الخادم البسيط يعمل بشكل مثالي</strong>
                    </div>
                </div>
                
                <div class="success-alert">
                    <h5><i class="fas fa-rocket text-success"></i> النظام البسيط جاهز!</h5>
                    <ul class="mb-0">
                        <li>✅ خادم HTTP بسيط بدون تعقيدات</li>
                        <li>✅ اتصال مباشر بقاعدة البيانات</li>
                        <li>✅ معالجة آمنة للأخطاء</li>
                        <li>✅ واجهات بسيطة وسريعة</li>
                    </ul>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="stat-card bg-primary">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h3>{stats['customers']}</h3>
                            <p>العملاء</p>
                            <a href="/customers" class="btn btn-light btn-sm">عرض</a>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="stat-card bg-success">
                            <i class="fas fa-file-contract fa-3x mb-3"></i>
                            <h3>{stats['contracts']}</h3>
                            <p>العقود</p>
                            <a href="/contracts" class="btn btn-light btn-sm">عرض</a>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="stat-card bg-warning">
                            <i class="fas fa-gavel fa-3x mb-3"></i>
                            <h3>{stats['auctions']}</h3>
                            <p>المزادات</p>
                            <a href="/auctions" class="btn btn-light btn-sm">عرض</a>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <h5>الأقسام المتاحة:</h5>
                    <div class="btn-group-vertical" role="group">
                        <a href="/customers" class="btn-custom btn btn-primary">
                            <i class="fas fa-users"></i> إدارة العملاء
                        </a>
                        <a href="/contracts" class="btn-custom btn btn-success">
                            <i class="fas fa-file-contract"></i> إدارة العقود
                        </a>
                        <a href="/auctions" class="btn-custom btn btn-warning">
                            <i class="fas fa-gavel"></i> إدارة المزادات
                        </a>
                        <a href="auctions_index.html" target="_blank" class="btn-custom btn btn-info">
                            <i class="fas fa-external-link-alt"></i> المزادات المستقلة
                        </a>
                        <a href="/status" class="btn-custom btn btn-secondary">
                            <i class="fas fa-info-circle"></i> حالة النظام
                        </a>
                    </div>
                </div>
                
                {'<div class="alert alert-danger mt-3"><strong>تحذير:</strong> ' + stats['error'] + '</div>' if stats['error'] else ''}
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        خادم HTTP بسيط - معرض قطر للسيارات
                    </small>
                </div>
            </div>
            
            <script>
                // تحديث تلقائي كل 30 ثانية
                setTimeout(() => location.reload(), 30000);
            </script>
        </body>
        </html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_customers_page(self):
        """إرسال صفحة العملاء"""
        try:
            customers_data = []
            if os.path.exists('auction.db'):
                conn = sqlite3.connect('auction.db')
                cursor = conn.cursor()
                cursor.execute("SELECT name_ar, name_en, phone, email FROM customer LIMIT 10")
                customers_data = cursor.fetchall()
                conn.close()
            
            customers_html = ""
            for customer in customers_data:
                customers_html += f'''
                <tr>
                    <td>{customer[0] or 'غير محدد'}</td>
                    <td>{customer[1] or 'غير محدد'}</td>
                    <td>{customer[2] or 'غير محدد'}</td>
                    <td>{customer[3] or 'غير محدد'}</td>
                </tr>
                '''
            
            html = f'''
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>إدارة العملاء</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body {{ background: #f8f9fa; padding: 20px; }}
                    .container {{ max-width: 1000px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
                    <div class="alert alert-success">
                        <strong>✅ قسم العملاء يعمل!</strong> تم العثور على {len(customers_data)} عميل
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة العملاء</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم بالعربية</th>
                                        <th>الاسم بالإنجليزية</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {customers_html}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="/" class="btn btn-primary">العودة للرئيسية</a>
                    </div>
                </div>
            </body>
            </html>
            '''
            
        except Exception as e:
            html = f"<h1>خطأ في العملاء</h1><p>{e}</p><a href='/'>العودة</a>"
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_contracts_page(self):
        """إرسال صفحة العقود"""
        html = '''
        <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
            <h1><i class="fas fa-file-contract"></i> إدارة العقود</h1>
            <div class="alert alert-success">
                <strong>✅ قسم العقود يعمل!</strong>
            </div>
            <p>هذا القسم يعمل بشكل طبيعي</p>
            <a href="/" class="btn btn-primary">العودة للرئيسية</a>
        </div>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_auctions_page(self):
        """إرسال صفحة المزادات"""
        html = '''
        <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
            <h1><i class="fas fa-gavel"></i> إدارة المزادات</h1>
            <div class="alert alert-success">
                <strong>✅ قسم المزادات يعمل!</strong>
            </div>
            <p>هذا القسم يعمل بشكل طبيعي</p>
            <a href="auctions_index.html" target="_blank" class="btn btn-warning">المزادات المستقلة</a>
            <a href="/" class="btn btn-primary">العودة للرئيسية</a>
        </div>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_status_page(self):
        """إرسال صفحة حالة النظام"""
        stats = self.get_database_stats()
        
        html = f'''
        <div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
            <h1><i class="fas fa-info-circle"></i> حالة النظام</h1>
            <div class="alert alert-success">
                <strong>✅ النظام يعمل بشكل مثالي!</strong>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>إحصائيات قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <ul>
                        <li>العملاء: {stats['customers']}</li>
                        <li>العقود: {stats['contracts']}</li>
                        <li>المزادات: {stats['auctions']}</li>
                    </ul>
                    {'<div class="alert alert-danger">خطأ: ' + stats['error'] + '</div>' if stats['error'] else '<div class="alert alert-success">قاعدة البيانات تعمل بشكل طبيعي</div>'}
                </div>
            </div>
            
            <a href="/" class="btn btn-primary">العودة للرئيسية</a>
        </div>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_login_page(self, error=None):
        """إرسال صفحة تسجيل الدخول"""
        error_html = f'<div class="alert alert-danger">{error}</div>' if error else ''
        
        html = f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تسجيل الدخول</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; }}
                .login-card {{ background: rgba(255,255,255,0.95); border-radius: 20px; padding: 40px; max-width: 400px; margin: auto; }}
            </style>
        </head>
        <body>
            <div class="login-card">
                <h3 class="text-center mb-4">تسجيل الدخول</h3>
                {error_html}
                <form method="POST" action="/login">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="username" value="admin" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="password" value="admin" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">دخول</button>
                </form>
                <div class="text-center mt-3">
                    <small>المستخدم: admin | كلمة المرور: admin</small>
                </div>
            </div>
        </body>
        </html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_error_page(self, error_message):
        """إرسال صفحة خطأ"""
        html = f'''
        <div style="text-align: center; padding: 50px; background: #f8f9fa;">
            <h2>حدث خطأ</h2>
            <p>{error_message}</p>
            <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            <a href="auctions_index.html" class="btn btn-warning">المزادات البديلة</a>
        </div>
        '''
        
        self.send_response(500)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

def start_simple_server():
    """تشغيل الخادم البسيط"""
    PORT = 7777
    
    try:
        with socketserver.TCPServer(("", PORT), QatarShowroomHandler) as httpd:
            print("🚀 خادم معرض قطر للسيارات البسيط")
            print("=" * 50)
            print(f"🌐 العنوان: http://127.0.0.1:{PORT}")
            print("👤 المستخدم: admin")
            print("🔑 كلمة المرور: admin")
            print("✅ خادم HTTP بسيط بدون تعقيدات")
            print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
            print("=" * 50)
            
            # فتح المتصفح تلقائياً
            def open_browser():
                time.sleep(2)
                try:
                    webbrowser.open(f'http://127.0.0.1:{PORT}')
                    print("🌐 تم فتح المتصفح تلقائياً")
                except:
                    print(f"💡 افتح المتصفح يدوياً على: http://127.0.0.1:{PORT}")
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print("💡 جرب المزادات المستقلة: auctions_index.html")

if __name__ == '__main__':
    start_simple_server()
