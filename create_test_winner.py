#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta

# إعداد التطبيق
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///auction.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.secret_key = 'your-secret-key-here'

db = SQLAlchemy(app)

# النماذج
class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))

class PremiumNumber(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.String(20), unique=True, nullable=False)
    category = db.Column(db.String(50))
    price = db.Column(db.Float)
    status = db.Column(db.String(20), default='available')

class Auction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    premium_number_id = db.Column(db.Integer, db.ForeignKey('premium_number.id'))
    starting_price = db.Column(db.Float, nullable=False)
    current_price = db.Column(db.Float, default=0)
    reserve_price = db.Column(db.Float)
    bid_increment = db.Column(db.Float, default=1000)
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default='scheduled')
    auto_extend = db.Column(db.Boolean, default=False)
    commission_rate = db.Column(db.Float, default=5.0)
    commission_amount = db.Column(db.Float, default=0)
    total_bids = db.Column(db.Integer, default=0)
    winner = db.Column(db.String(100))
    winner_id = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    premium_number = db.relationship('PremiumNumber', backref='auctions')

class Bid(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    auction_id = db.Column(db.Integer, db.ForeignKey('auction.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    bid_amount = db.Column(db.Float, nullable=False)
    bid_time = db.Column(db.DateTime, default=datetime.utcnow)
    
    auction = db.relationship('Auction', backref='bids')
    customer = db.relationship('Customer', backref='bids')

def create_test_data():
    """إنشاء بيانات تجريبية"""
    with app.app_context():
        print("🔧 إنشاء بيانات تجريبية...")
        
        # إنشاء الجداول
        db.create_all()
        
        # حذف البيانات القديمة
        Bid.query.delete()
        Auction.query.delete()
        Customer.query.delete()
        PremiumNumber.query.delete()
        db.session.commit()
        
        # إنشاء عملاء
        customers = [
            Customer(name_ar="أحمد محمد الكعبي", phone="+974 5555 1234", email="<EMAIL>"),
            Customer(name_ar="سارة أحمد النعيمي", phone="+974 5555 5678", email="<EMAIL>"),
            Customer(name_ar="محمد علي الثاني", phone="+974 5555 9012", email="<EMAIL>"),
            Customer(name_ar="فاطمة حسن المري", phone="+974 5555 3456", email="<EMAIL>"),
            Customer(name_ar="عبدالله سالم الكواري", phone="+974 5555 7890", email="<EMAIL>")
        ]
        
        for customer in customers:
            db.session.add(customer)
        db.session.commit()
        print(f"✅ تم إنشاء {len(customers)} عميل")
        
        # إنشاء رقم مميز
        premium_number = PremiumNumber(
            number="777777",
            category="VIP", 
            price=75000,
            status="auction"
        )
        db.session.add(premium_number)
        db.session.commit()
        print("✅ تم إنشاء رقم مميز: 777777")
        
        # إنشاء مزاد منتهي
        end_time = datetime.now() - timedelta(hours=1)
        start_time = end_time - timedelta(hours=2)
        
        auction = Auction(
            title=f"مزاد الرقم المميز {premium_number.number}",
            description="مزاد تجريبي لاختبار عرض الفائز",
            premium_number_id=premium_number.id,
            starting_price=50000,
            current_price=78850,
            reserve_price=60000,
            bid_increment=1000,
            start_time=start_time,
            end_time=end_time,
            status="ended",
            auto_extend=False,
            commission_rate=5.0,
            commission_amount=3942.5,
            total_bids=8
        )
        db.session.add(auction)
        db.session.commit()
        print(f"✅ تم إنشاء مزاد: {auction.title}")
        
        # إنشاء مزايدات
        bid_data = [
            (customers[0].id, 50000),
            (customers[1].id, 52000), 
            (customers[2].id, 55000),
            (customers[3].id, 58000),
            (customers[4].id, 62000),
            (customers[0].id, 68000),
            (customers[1].id, 72000),
            (customers[0].id, 78850)  # الفائز
        ]
        
        for i, (customer_id, amount) in enumerate(bid_data):
            bid_time = start_time + timedelta(minutes=15 * i)
            bid = Bid(
                auction_id=auction.id,
                customer_id=customer_id,
                bid_amount=amount,
                bid_time=bid_time
            )
            db.session.add(bid)
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(bid_data)} مزايدة")
        
        # تحديث الفائز
        winning_bid = Bid.query.filter_by(auction_id=auction.id).order_by(Bid.bid_amount.desc()).first()
        if winning_bid:
            auction.winner = winning_bid.customer.name_ar
            auction.winner_id = winning_bid.customer_id
            db.session.commit()
            print(f"✅ الفائز: {winning_bid.customer.name_ar} بمبلغ {winning_bid.bid_amount:,.0f} ر.ق")
        
        return auction.id

if __name__ == "__main__":
    print("🏆 إنشاء بيانات تجريبية لاختبار عرض الفائز")
    print("=" * 50)
    
    try:
        auction_id = create_test_data()
        print("\n" + "=" * 50)
        print("✅ تم إنشاء البيانات التجريبية بنجاح!")
        print(f"🌐 اختبر النتيجة: http://127.0.0.1:9898/auction/standalone/{auction_id}")
        print("=" * 50)
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
