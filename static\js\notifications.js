/**
 * نظام إدارة الإشعارات - معرض قطر للسيارات
 */

class NotificationManager {
    constructor() {
        this.notificationCount = 0;
        this.notifications = [];
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.loadNotifications();
        this.setupEventListeners();
        this.startAutoUpdate();
    }

    setupEventListeners() {
        // عند فتح قائمة الإشعارات
        const notificationDropdown = document.getElementById('notificationsDropdown');
        if (notificationDropdown) {
            notificationDropdown.addEventListener('click', () => {
                this.loadDetailedNotifications();
            });
        }

        // تحديث عند النقر على زر التحديث
        document.addEventListener('click', (e) => {
            if (e.target.closest('.refresh-notifications')) {
                this.loadNotifications();
            }
        });
    }

    async loadNotifications() {
        try {
            const response = await fetch('/api/notifications/count');
            const data = await response.json();
            
            this.updateNotificationCount(data.count);
            this.notifications = data;
            
        } catch (error) {
            console.log('Could not load notifications:', error);
        }
    }

    async loadDetailedNotifications() {
        const loadingElement = document.querySelector('.notification-loading');
        const emptyElement = document.querySelector('.notification-empty');
        const itemsContainer = document.querySelector('.notification-items');
        const footerElement = document.querySelector('.notification-footer');
        const dividerElement = document.querySelector('.notification-divider');

        // إظهار التحميل
        if (loadingElement) loadingElement.style.display = 'block';
        if (emptyElement) emptyElement.style.display = 'none';
        if (itemsContainer) itemsContainer.style.display = 'none';
        if (footerElement) footerElement.style.display = 'none';
        if (dividerElement) dividerElement.style.display = 'none';

        try {
            const response = await fetch('/api/notifications/count');
            const data = await response.json();
            
            // إخفاء التحميل
            if (loadingElement) loadingElement.style.display = 'none';
            
            if (data.count === 0) {
                // لا توجد إشعارات
                if (emptyElement) emptyElement.style.display = 'block';
            } else {
                // عرض الإشعارات
                this.renderNotifications(data);
                if (itemsContainer) itemsContainer.style.display = 'block';
                if (footerElement) footerElement.style.display = 'block';
                if (dividerElement) dividerElement.style.display = 'block';
            }
            
        } catch (error) {
            console.log('Could not load detailed notifications:', error);
            if (loadingElement) loadingElement.style.display = 'none';
            if (emptyElement) emptyElement.style.display = 'block';
        }
    }

    renderNotifications(data) {
        const itemsContainer = document.querySelector('.notification-items');
        if (!itemsContainer) return;

        let notificationsHtml = '';

        // الأقساط المتأخرة
        if (data.overdue > 0) {
            notificationsHtml += this.createNotificationItem(
                'urgent',
                'fas fa-exclamation-triangle',
                'أقساط متأخرة',
                `${data.overdue} قسط متأخر عن الدفع`,
                'منذ اليوم',
                '/installments/overdue'
            );
        }

        // الأقساط المستحقة اليوم
        if (data.due_today > 0) {
            notificationsHtml += this.createNotificationItem(
                'warning',
                'fas fa-calendar-day',
                'أقساط مستحقة اليوم',
                `${data.due_today} قسط مستحق اليوم`,
                'اليوم',
                '/installments/due-today'
            );
        }

        // السيارات في الصيانة
        if (data.cars_in_maintenance > 0) {
            notificationsHtml += this.createNotificationItem(
                'info',
                'fas fa-tools',
                'سيارات في الصيانة',
                `${data.cars_in_maintenance} سيارة في الصيانة`,
                'مستمر',
                '/cars?status=maintenance'
            );
        }

        // العقود المعلقة
        if (data.pending_contracts > 0) {
            notificationsHtml += this.createNotificationItem(
                'warning',
                'fas fa-file-contract',
                'عقود معلقة',
                `${data.pending_contracts} عقد في انتظار الاعتماد`,
                'معلق',
                '/contracts?status=draft'
            );
        }

        // انتهاء التأمين قريباً
        if (data.insurance_expiry_soon > 0) {
            notificationsHtml += this.createNotificationItem(
                'warning',
                'fas fa-shield-alt',
                'انتهاء التأمين قريباً',
                `${data.insurance_expiry_soon} سيارة ينتهي تأمينها قريباً`,
                'خلال 30 يوم',
                '/cars?insurance_expiry=soon'
            );
        }

        // انتهاء الترخيص قريباً
        if (data.registration_expiry_soon > 0) {
            notificationsHtml += this.createNotificationItem(
                'warning',
                'fas fa-id-card',
                'انتهاء الترخيص قريباً',
                `${data.registration_expiry_soon} سيارة ينتهي ترخيصها قريباً`,
                'خلال 30 يوم',
                '/cars?registration_expiry=soon'
            );
        }

        // المزادات النشطة
        if (data.active_auctions > 0) {
            notificationsHtml += this.createNotificationItem(
                'success',
                'fas fa-gavel',
                'مزادات نشطة',
                `${data.active_auctions} مزاد نشط حالياً`,
                'نشط',
                '/auction'
            );
        }

        // العملاء الجدد
        if (data.new_customers > 0) {
            notificationsHtml += this.createNotificationItem(
                'success',
                'fas fa-user-plus',
                'عملاء جدد',
                `${data.new_customers} عميل جديد هذا الأسبوع`,
                'آخر 7 أيام',
                '/customers?filter=new'
            );
        }

        itemsContainer.innerHTML = notificationsHtml;
    }

    createNotificationItem(type, icon, title, text, time, link = '#') {
        return `
            <li class="notification-item ${type}" onclick="window.location.href='${link}'">
                <div class="d-flex align-items-center">
                    <div class="notification-icon">
                        <i class="${icon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="notification-title">${title}</div>
                        <div class="notification-text">${text}</div>
                        <div class="notification-time">${time}</div>
                    </div>
                </div>
            </li>
        `;
    }

    updateNotificationCount(count) {
        this.notificationCount = count;
        
        const countElements = document.querySelectorAll('.notification-count');
        countElements.forEach(element => {
            element.textContent = count;
            if (count === 0) {
                element.classList.add('zero');
            } else {
                element.classList.remove('zero');
            }
        });

        // تحديث عنوان الصفحة
        this.updatePageTitle(count);
    }

    updatePageTitle(count) {
        const originalTitle = document.title.replace(/^\(\d+\)\s*/, '');
        if (count > 0) {
            document.title = `(${count}) ${originalTitle}`;
        } else {
            document.title = originalTitle;
        }
    }

    startAutoUpdate() {
        // تحديث كل 30 ثانية
        this.updateInterval = setInterval(() => {
            this.loadNotifications();
        }, 30000);
    }

    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    showToast(message, type = 'info') {
        // إنشاء إشعار منبثق
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        // إضافة إلى الصفحة
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // إظهار الإشعار
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // إزالة الإشعار بعد إخفائه
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// تهيئة نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.notificationManager = new NotificationManager();
});

// تصدير للاستخدام العام
window.NotificationManager = NotificationManager;
