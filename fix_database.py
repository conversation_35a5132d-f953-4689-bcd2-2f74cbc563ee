import sqlite3

try:
    conn = sqlite3.connect('working_database.db')
    cursor = conn.cursor()
    
    # إضافة الحقول الجديدة
    try:
        cursor.execute('ALTER TABLE user ADD COLUMN email TEXT')
        print("تم إضافة حقل email")
    except:
        print("حقل email موجود بالفعل")
    
    try:
        cursor.execute('ALTER TABLE user ADD COLUMN full_name TEXT')
        print("تم إضافة حقل full_name")
    except:
        print("حقل full_name موجود بالفعل")
    
    try:
        cursor.execute('ALTER TABLE user ADD COLUMN phone TEXT')
        print("تم إضافة حقل phone")
    except:
        print("حقل phone موجود بالفعل")
    
    try:
        cursor.execute('ALTER TABLE user ADD COLUMN avatar TEXT')
        print("تم إضافة حقل avatar")
    except:
        print("حقل avatar موجود بالفعل")
    
    try:
        cursor.execute('ALTER TABLE user ADD COLUMN created_at DATETIME')
        print("تم إضافة حقل created_at")
    except:
        print("حقل created_at موجود بالفعل")
    
    try:
        cursor.execute('ALTER TABLE user ADD COLUMN last_login DATETIME')
        print("تم إضافة حقل last_login")
    except:
        print("حقل last_login موجود بالفعل")
    
    conn.commit()
    conn.close()
    print("تم تحديث قاعدة البيانات بنجاح!")
    
except Exception as e:
    print(f"خطأ: {e}")
