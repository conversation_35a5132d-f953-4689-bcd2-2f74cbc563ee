@echo off
chcp 65001 > nul
title اختبار التحديث التلقائي البسيط

echo.
echo ========================================
echo   اختبار التحديث التلقائي البسيط
echo   نظام مبسط وفعال
echo ========================================
echo.

cd /d "%~dp0"

echo 🧪 إنشاء مزاد اختبار بسيط...
python -c "
import sys, os
sys.path.insert(0, '.')
from working_app import app, db, Customer, PremiumNumber, Auction, Bid
from datetime import datetime, timedelta

with app.app_context():
    # حذف البيانات القديمة
    Bid.query.delete()
    Auction.query.delete()
    Customer.query.delete()
    PremiumNumber.query.delete()
    
    # إنشاء عميل
    customer = Customer(name_ar='عميل اختبار', phone='+974 1111 1111')
    db.session.add(customer)
    db.session.flush()
    
    # إنشاء رقم مميز
    number = PremiumNumber(number='99999', category='اختبار', price=10000, status='auction')
    db.session.add(number)
    db.session.flush()
    
    # إنشاء مزاد نشط
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=5)
    
    auction = Auction(
        title='مزاد اختبار التحديث',
        premium_number_id=number.id,
        starting_price=5000,
        current_price=7500,
        start_time=start_time,
        end_time=end_time,
        status='active',
        commission_rate=2.0,
        total_bids=1
    )
    db.session.add(auction)
    db.session.flush()
    
    # إنشاء مزايدة
    bid = Bid(
        auction_id=auction.id,
        customer_id=customer.id,
        bid_amount=7500,
        bid_time=start_time
    )
    db.session.add(bid)
    db.session.commit()
    
    print('✅ تم إنشاء مزاد اختبار نشط')
    print(f'🌐 الرابط: http://127.0.0.1:9898/auction/standalone/{auction.id}')
"

echo.
echo 🚀 تشغيل النظام...
start /B python working_app.py

echo ⏳ انتظار...
timeout /t 3 /nobreak > nul

echo 🌐 فتح العرض المستقل...
start http://127.0.0.1:9898/auction/standalone/1

echo.
echo ✅ تم فتح اختبار التحديث التلقائي!
echo.
echo 🔍 ما يجب أن تراه:
echo    1. مؤشر في أعلى يمين الصفحة
echo    2. نص: "تحديث تلقائي كل 3 ثوان"
echo    3. عداد تنازلي: (3s) → (2s) → (1s)
echo    4. تحديث الصفحة كل 3 ثوان
echo.
echo 🛠️ للتحقق من عمل JavaScript:
echo    - اضغط F12 لفتح Developer Tools
echo    - اذهب إلى تبويب Console
echo    - يجب أن ترى رسائل التحديث
echo.
echo 💡 إذا لم يعمل:
echo    - تأكد من أن المزاد نشط (active)
echo    - أعد تحميل الصفحة (F5)
echo    - تحقق من رسائل الأخطاء في Console
echo.
echo ⏹️ اضغط أي مفتاح لإيقاف النظام...
pause > nul

echo 🛑 إيقاف النظام...
taskkill /f /im python.exe > nul 2>&1
echo ✅ تم إيقاف النظام
