#!/usr/bin/env python3
"""
إضافة بيانات تجريبية - نظام معرض قطر للسيارات
"""

import sqlite3
import os
from datetime import datetime, date, timedelta
import random

def add_demo_data():
    """إضافة بيانات تجريبية شاملة"""
    db_path = 'working_database.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🚗 إضافة بيانات السيارات التجريبية...")
        
        # بيانات السيارات
        cars_data = [
            ('تويوتا', 'كامري', 2023, 95000, 'available', 'أبيض', 'بنزين', 'أوتوماتيك', '2.5L', 15000, 'سيدان', 4, 5, 'JTDKN3DP5E3123456', '123456', '2025-12-31', '2025-06-30', 'ممتازة', 'مثبت سرعة، نظام ملاحة، كاميرا خلفية', 'سيارة نظيفة جداً', '2023-01-15', 85000),
            ('نيسان', 'التيما', 2022, 78000, 'available', 'فضي', 'بنزين', 'أوتوماتيك', '2.0L', 25000, 'سيدان', 4, 5, 'JTDKN3DP5E3123457', '234567', '2025-08-15', '2025-04-20', 'جيدة جداً', 'شاشة لمس، بلوتوث، USB', 'صيانة دورية منتظمة', '2022-03-10', 70000),
            ('لكزس', 'ES350', 2024, 165000, 'available', 'أسود', 'بنزين', 'أوتوماتيك', '3.5L', 8000, 'سيدان', 4, 5, 'JTDKN3DP5E3123458', '345678', '2026-01-31', '2025-12-15', 'ممتازة', 'جلد، فتحة سقف، نظام صوتي متقدم', 'سيارة فاخرة بحالة ممتازة', '2024-01-01', 155000),
            ('بي إم دبليو', 'X5', 2023, 285000, 'sold', 'أزرق', 'بنزين', 'أوتوماتيك', '3.0L', 12000, 'SUV', 5, 7, 'JTDKN3DP5E3123459', '456789', '2025-10-31', '2025-07-30', 'ممتازة', 'دفع رباعي، نظام ملاحة، مقاعد جلد', 'SUV فاخر', '2023-02-20', 275000),
            ('مرسيدس', 'C-Class', 2022, 195000, 'reserved', 'رمادي', 'بنزين', 'أوتوماتيك', '2.0L', 18000, 'سيدان', 4, 5, 'JTDKN3DP5E3123460', '567890', '2025-09-30', '2025-05-15', 'جيدة جداً', 'نظام أمان متقدم، شاشة كبيرة', 'سيارة ألمانية موثوقة', '2022-05-12', 185000),
            ('هوندا', 'أكورد', 2023, 88000, 'available', 'أحمر', 'بنزين', 'أوتوماتيك', '1.5L', 22000, 'سيدان', 4, 5, 'JTDKN3DP5E3123461', '678901', '2025-11-30', '2025-08-20', 'جيدة', 'اقتصادية في الوقود، موثوقة', 'سيارة عملية ومريحة', '2023-03-08', 80000),
            ('أودي', 'Q7', 2024, 320000, 'available', 'أبيض', 'بنزين', 'أوتوماتيك', '3.0L', 5000, 'SUV', 5, 7, 'JTDKN3DP5E3123462', '789012', '2026-02-28', '2025-11-10', 'ممتازة', 'دفع رباعي، مقاعد جلد، نظام ترفيهي', 'SUV فاخر جديد', '2024-02-15', 310000),
            ('كيا', 'سورينتو', 2022, 125000, 'available', 'بني', 'بنزين', 'أوتوماتيك', '2.5L', 35000, 'SUV', 5, 7, 'JTDKN3DP5E3123463', '890123', '2025-07-31', '2025-03-25', 'جيدة', 'مساحة واسعة، اقتصادية', 'مناسبة للعائلات', '2022-06-20', 115000),
            ('فورد', 'موستانج', 2023, 180000, 'available', 'أصفر', 'بنزين', 'يدوي', '5.0L', 8500, 'كوبيه', 2, 4, 'JTDKN3DP5E3123464', '901234', '2025-12-31', '2025-09-15', 'ممتازة', 'سيارة رياضية، صوت محرك قوي', 'للمحبين السرعة', '2023-04-10', 170000),
            ('جيب', 'رانجلر', 2023, 155000, 'maintenance', 'أخضر', 'بنزين', 'أوتوماتيك', '3.6L', 28000, 'SUV', 5, 5, 'JTDKN3DP5E3123465', '012345', '2025-08-31', '2025-06-10', 'جيدة', 'دفع رباعي، مناسب للطرق الوعرة', 'في الصيانة حالياً', '2023-01-25', 145000)
        ]
        
        for car in cars_data:
            cursor.execute('''
                INSERT INTO car (make, model, year, price, status, color, fuel_type, transmission, 
                               engine_size, mileage, body_type, doors, seats, vin_number, license_plate,
                               insurance_expiry, registration_expiry, condition, features, notes, 
                               purchase_date, purchase_price)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', car)
        
        print("✅ تم إضافة 10 سيارات")
        
        print("🔢 إضافة الأرقام المميزة...")
        
        # بيانات الأرقام المميزة
        premium_numbers = [
            ('11111', 'VIP', 50000, 'available'),
            ('22222', 'VIP', 45000, 'available'),
            ('33333', 'VIP', 40000, 'sold'),
            ('77777', 'VIP', 55000, 'available'),
            ('88888', 'VIP', 60000, 'reserved'),
            ('99999', 'VIP', 65000, 'available'),
            ('12345', 'مميز', 25000, 'available'),
            ('54321', 'مميز', 22000, 'available'),
            ('11122', 'مميز', 18000, 'available'),
            ('99911', 'مميز', 20000, 'available'),
            ('123', 'ثلاثي', 35000, 'available'),
            ('777', 'ثلاثي', 40000, 'available'),
            ('888', 'ثلاثي', 38000, 'sold'),
            ('999', 'ثلاثي', 42000, 'available'),
            ('100', 'ثلاثي', 30000, 'available')
        ]
        
        for number in premium_numbers:
            cursor.execute('''
                INSERT INTO premium_number (number, category, price, status)
                VALUES (?, ?, ?, ?)
            ''', number)
        
        print("✅ تم إضافة 15 رقم مميز")
        
        print("👥 إضافة العملاء...")
        
        # بيانات العملاء
        customers_data = [
            ('أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '55123456', '<EMAIL>', '28501234567', 'قطري', '1985-03-15', 'الدوحة، منطقة الخليج الغربي', 'الدوحة', '12345', 'مهندس', 'شركة قطر للبترول', 25000, 'عميل مميز'),
            ('فاطمة علي النعيمي', 'Fatima Ali Al-Naimi', '55234567', '<EMAIL>', '29012345678', 'قطرية', '1990-07-22', 'الدوحة، اللؤلؤة', 'الدوحة', '23456', 'طبيبة', 'مستشفى حمد', 30000, 'عميلة جديدة'),
            ('محمد سالم الثاني', 'Mohammed Salem Al-Thani', '55345678', '<EMAIL>', '27512345679', 'قطري', '1982-12-10', 'الدوحة، كتارا', 'الدوحة', '34567', 'رجل أعمال', 'شركة خاصة', 50000, 'عميل VIP'),
            ('سارة أحمد المري', 'Sara Ahmed Al-Marri', '55456789', '<EMAIL>', '29212345680', 'قطرية', '1988-05-18', 'الريان، مدينة خليفة', 'الريان', '45678', 'محاسبة', 'وزارة المالية', 22000, 'عميلة منتظمة'),
            ('خالد يوسف الأنصاري', 'Khalid Youssef Al-Ansari', '55567890', '<EMAIL>', '28012345681', 'قطري', '1975-09-25', 'الوكرة، الوكرة الجديدة', 'الوكرة', '56789', 'مدير', 'بنك قطر الوطني', 35000, 'عميل موثوق'),
            ('نورا سعد الكواري', 'Nora Saad Al-Kuwari', '55678901', '<EMAIL>', '29512345682', 'قطرية', '1992-11-08', 'الدوحة، الزمالك', 'الدوحة', '67890', 'مدرسة', 'وزارة التعليم', 18000, 'عميلة شابة'),
            ('عبدالله راشد المناعي', 'Abdullah Rashid Al-Mannai', '55789012', '<EMAIL>', '27012345683', 'قطري', '1980-02-14', 'أم صلال، أم صلال محمد', 'أم صلال', '78901', 'مقاول', 'شركة المناعي للمقاولات', 40000, 'عميل كبير'),
            ('مريم حسن الدرهم', 'Mariam Hassan Al-Dirham', '55890123', '<EMAIL>', '29812345684', 'قطرية', '1987-06-30', 'الدوحة، أسباير', 'الدوحة', '89012', 'صيدلانية', 'صيدلية الدوحة', 26000, 'عميلة مهنية'),
            ('سعد محمد الهاجري', 'Saad Mohammed Al-Hajri', '55901234', '<EMAIL>', '28512345685', 'قطري', '1983-04-12', 'الخور، الخور الشمالي', 'الخور', '90123', 'مهندس بترول', 'قطر غاز', 45000, 'عميل متميز'),
            ('عائشة علي السليطي', 'Aisha Ali Al-Sulaiti', '55012345', '<EMAIL>', '29112345686', 'قطرية', '1991-08-05', 'الدوحة، المطار القديم', 'الدوحة', '01234', 'محامية', 'مكتب السليطي للمحاماة', 32000, 'عميلة قانونية')
        ]
        
        for customer in customers_data:
            cursor.execute('''
                INSERT INTO customer (name_ar, name_en, phone, email, id_number, nationality, birth_date,
                                    address, city, postal_code, profession, company, monthly_income, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', customer)
        
        print("✅ تم إضافة 10 عملاء")
        
        print("📋 إضافة العقود...")
        
        # بيانات العقود
        today = date.today()
        contracts_data = [
            ('CON-2024-06-0001', 'car_sale', 'cash', 1, 1, None, 95000, 0, 0, 0, 0, 0, today, today + timedelta(days=7), 12, True, True, 'ضمان شامل لمدة سنة', 'عقد بيع نقدي', 0, 0, 2000, 'completed', True, True, 'محمد أحمد', '12345678', None, True, False, 1),
            ('CON-2024-06-0002', 'car_sale', 'installment', 2, 2, None, 78000, 20000, 58000, 2900, 20, 5.5, today - timedelta(days=15), today + timedelta(days=10), 6, True, False, 'تقسيط على 20 شهر', 'عقد تقسيط', 3000, 0, 1500, 'active', True, True, 'سالم محمد', '23456789', None, False, False, 1),
            ('CON-2024-06-0003', 'premium_number_sale', 'cash', 3, None, 3, 40000, 0, 0, 0, 0, 0, today - timedelta(days=30), today - timedelta(days=25), 0, False, False, 'رقم مميز VIP', 'بيع رقم 33333', 0, 0, 800, 'completed', True, True, 'أحمد سالم', '34567890', None, True, False, 1),
            ('CON-2024-06-0004', 'car_sale', 'installment', 4, 5, None, 195000, 50000, 145000, 8055, 18, 6.0, today - timedelta(days=10), today + timedelta(days=5), 24, True, True, 'تقسيط طويل المدى', 'مرسيدس C-Class', 5000, 0, 3500, 'active', True, False, None, None, None, False, False, 1),
            ('CON-2024-06-0005', 'combined_sale', 'cash', 5, 4, 5, 240000, 0, 0, 0, 0, 0, today - timedelta(days=5), today + timedelta(days=2), 12, True, True, 'عقد مدمج سيارة ورقم', 'BMW X5 + رقم 88888', 10000, 0, 5000, 'draft', False, False, None, None, None, False, False, 1)
        ]
        
        for contract in contracts_data:
            cursor.execute('''
                INSERT INTO contract (contract_number, contract_type, payment_type, customer_id, car_id, 
                                    premium_number_id, total_amount, down_payment, remaining_amount, 
                                    monthly_payment, installment_months, interest_rate, contract_date, 
                                    delivery_date, warranty_months, insurance_required, registration_included,
                                    special_conditions, notes, discount_amount, tax_amount, commission_amount,
                                    status, signed_by_customer, signed_by_dealer, witness_name, witness_id,
                                    contract_file_path, pdf_generated, word_generated, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', contract)
        
        print("✅ تم إضافة 5 عقود")
        
        print("💳 إضافة دفعات الأقساط...")
        
        # إضافة دفعات الأقساط للعقود التقسيطية
        # العقد الثاني (تقسيط 20 شهر)
        contract_2_start = today - timedelta(days=15)
        for i in range(1, 21):
            due_date = contract_2_start + timedelta(days=30*i)
            status = 'paid' if i <= 3 else ('overdue' if due_date < today else 'pending')
            paid_amount = 2900 if status == 'paid' else 0
            payment_date = due_date if status == 'paid' else None
            
            cursor.execute('''
                INSERT INTO installment_payment (contract_id, installment_number, due_date, amount, 
                                                paid_amount, payment_date, status, late_fee, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (2, i, due_date, 2900, paid_amount, payment_date, status, 0, f'القسط رقم {i}'))
        
        # العقد الرابع (تقسيط 18 شهر)
        contract_4_start = today - timedelta(days=10)
        for i in range(1, 19):
            due_date = contract_4_start + timedelta(days=30*i)
            status = 'paid' if i <= 1 else ('pending' if due_date >= today else 'overdue')
            paid_amount = 8055 if status == 'paid' else 0
            payment_date = due_date if status == 'paid' else None
            late_fee = 100 if status == 'overdue' else 0
            
            cursor.execute('''
                INSERT INTO installment_payment (contract_id, installment_number, due_date, amount, 
                                                paid_amount, payment_date, status, late_fee, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (4, i, due_date, 8055, paid_amount, payment_date, status, late_fee, f'القسط رقم {i}'))
        
        print("✅ تم إضافة دفعات الأقساط")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إضافة جميع البيانات التجريبية بنجاح!")
        print("\n📊 ملخص البيانات المضافة:")
        print("   🚗 10 سيارات متنوعة")
        print("   🔢 15 رقم مميز")
        print("   👥 10 عملاء")
        print("   📋 5 عقود")
        print("   💳 39 دفعة قسط")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        return False

if __name__ == '__main__':
    print("🚀 نظام معرض قطر للسيارات - إضافة بيانات تجريبية")
    print("=" * 70)
    
    if add_demo_data():
        print("\n✅ تم إضافة البيانات التجريبية بنجاح!")
        print("🌐 يمكنك الآن تصفح النظام والتجربة مع البيانات الجديدة")
    else:
        print("\n❌ فشل في إضافة البيانات التجريبية")
