#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import time
import webbrowser
import os

def quick_test():
    """اختبار سريع للصفحة المحسنة"""
    
    print("🔧 إعداد البيانات...")
    
    # إنشاء البيانات
    try:
        subprocess.run(['sqlite3', 'auction.db'], input='''
DROP TABLE IF EXISTS bid;
DROP TABLE IF EXISTS auction;
DROP TABLE IF EXISTS customer;
DROP TABLE IF EXISTS premium_number;

CREATE TABLE customer (id INTEGER PRIMARY KEY, name_ar TEXT, phone TEXT, email TEXT);
CREATE TABLE premium_number (id INTEGER PRIMARY KEY, number TEXT, category TEXT, price REAL, status TEXT);
CREATE TABLE auction (id INTEGER PRIMARY KEY, title TEXT, description TEXT, premium_number_id INTEGER, starting_price REAL, current_price REAL, status TEXT, start_time TEXT, end_time TEXT, commission_rate REAL, total_bids INTEGER);
CREATE TABLE bid (id INTEGER PRIMARY KEY, auction_id INTEGER, customer_id INTEGER, bid_amount REAL, bid_time TEXT);

INSERT INTO customer VALUES (1, 'أحمد محمد الكعبي', '+974 5555 1234', '<EMAIL>');
INSERT INTO premium_number VALUES (1, '777777', 'VIP', 75000, 'auction');
INSERT INTO auction VALUES (1, 'مزاد الرقم المميز 777777', 'مزاد تجريبي', 1, 50000, 78850, 'ended', '2024-01-01 10:00:00', '2024-01-01 12:00:00', 5.0, 1);
INSERT INTO bid VALUES (1, 1, 1, 78850, '2024-01-01 11:55:00');
        ''', text=True, check=True)
        print("✅ تم إنشاء البيانات")
    except:
        print("⚠️ تم تخطي إنشاء البيانات")
    
    print("🚀 تشغيل الخادم...")
    
    # تشغيل الخادم
    try:
        subprocess.Popen(['python', 'working_app.py'])
        print("✅ تم تشغيل الخادم")
    except:
        print("❌ فشل في تشغيل الخادم")
        return
    
    # انتظار
    print("⏳ انتظار 3 ثوان...")
    time.sleep(3)
    
    # فتح المتصفح
    print("🌐 فتح المتصفح...")
    try:
        webbrowser.open('http://127.0.0.1:9898/auction/standalone/1')
        print("✅ تم فتح المتصفح")
    except:
        print("❌ فشل في فتح المتصفح")
    
    print("\n" + "="*50)
    print("✅ تم الاختبار السريع!")
    print("🔴 اسم الفائز يجب أن يظهر باللون الأحمر")
    print("📋 الصفحة منظمة ومرتبة")
    print("🌐 الرابط: http://127.0.0.1:9898/auction/standalone/1")
    print("="*50)

if __name__ == "__main__":
    quick_test()
