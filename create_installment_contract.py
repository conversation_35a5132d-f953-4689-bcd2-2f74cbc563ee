#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Contract, Customer, Car, InstallmentPayment
from datetime import datetime, timedelta
import random

def create_installment_contract():
    """إنشاء عقد تقسيط تجريبي مع جدول أقساط"""
    with app.app_context():
        try:
            # البحث عن عميل موجود أو إنشاء عميل جديد
            customer = Customer.query.first()
            if not customer:
                customer = Customer(
                    name_ar="أحمد محمد الكعبي",
                    name_en="Ahmed <PERSON> Al-Ka<PERSON>",
                    phone="55123456",
                    email="<EMAIL>",
                    id_number="12345678901",
                    address="الدوحة، قطر"
                )
                db.session.add(customer)
                db.session.flush()
            
            # البحث عن سيارة متاحة
            car = Car.query.filter_by(status='available').first()
            if not car:
                car = Car(
                    make="تويوتا",
                    model="كامري",
                    year=2023,
                    color="أبيض",
                    price=120000,
                    status='available',
                    mileage=0,
                    fuel_type="بنزين",
                    transmission="أوتوماتيك"
                )
                db.session.add(car)
                db.session.flush()
            
            # إنشاء عقد تقسيط
            contract = Contract(
                customer_id=customer.id,
                car_id=car.id,
                contract_type='car',
                payment_method='installment',
                total_amount=car.price,
                down_payment=30000,  # دفعة مقدمة 30,000
                remaining_amount=car.price - 30000,  # المتبقي 90,000
                installment_count=12,  # 12 قسط
                interest_rate=5.0,  # فائدة 5%
                contract_date=datetime.now().date(),
                status='active',
                notes="عقد تقسيط تجريبي لاختبار النظام"
            )
            
            db.session.add(contract)
            db.session.flush()
            
            # حساب مبلغ القسط الشهري مع الفائدة
            principal = contract.remaining_amount
            monthly_rate = contract.interest_rate / 100 / 12
            monthly_payment = principal * (monthly_rate * (1 + monthly_rate)**contract.installment_count) / ((1 + monthly_rate)**contract.installment_count - 1)
            monthly_payment = round(monthly_payment, 2)
            
            # إنشاء جدول الأقساط
            start_date = datetime.now().date()
            for i in range(contract.installment_count):
                due_date = start_date + timedelta(days=30 * (i + 1))
                
                installment = InstallmentPayment(
                    contract_id=contract.id,
                    installment_number=i + 1,
                    due_date=due_date,
                    amount=monthly_payment,
                    paid_amount=0,
                    status='pending'
                )
                db.session.add(installment)
            
            # تحديث حالة السيارة
            car.status = 'sold'
            
            db.session.commit()
            
            print(f"✅ تم إنشاء عقد تقسيط بنجاح!")
            print(f"🔢 رقم العقد: {contract.contract_number}")
            print(f"👤 العميل: {customer.name_ar}")
            print(f"🚗 السيارة: {car.make} {car.model} {car.year}")
            print(f"💰 إجمالي المبلغ: {contract.total_amount:,.0f} ر.ق")
            print(f"💳 الدفعة المقدمة: {contract.down_payment:,.0f} ر.ق")
            print(f"📊 المتبقي: {contract.remaining_amount:,.0f} ر.ق")
            print(f"📅 عدد الأقساط: {contract.installment_count}")
            print(f"💹 معدل الفائدة: {contract.interest_rate}%")
            print(f"💵 القسط الشهري: {monthly_payment:,.0f} ر.ق")
            print(f"🌐 يمكنك إدارة الأقساط على: http://127.0.0.1:9898/installments")
            
            return contract.id
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء العقد: {e}")
            db.session.rollback()
            return False

def create_sample_payments():
    """إنشاء بعض الدفعات التجريبية"""
    with app.app_context():
        try:
            # البحث عن أقساط معلقة
            pending_installments = InstallmentPayment.query.filter_by(status='pending').limit(3).all()
            
            for installment in pending_installments:
                # دفع جزئي أو كامل عشوائي
                if random.choice([True, False]):
                    # دفع كامل
                    installment.paid_amount = installment.amount
                    installment.status = 'paid'
                    installment.payment_date = datetime.now().date()
                    installment.notes = "دفعة تجريبية - مدفوع كاملاً"
                else:
                    # دفع جزئي
                    installment.paid_amount = installment.amount * 0.5  # 50%
                    installment.status = 'partial'
                    installment.payment_date = datetime.now().date()
                    installment.notes = "دفعة تجريبية - دفع جزئي"
                
                # تحديث المبلغ المتبقي في العقد
                installment.contract.remaining_amount -= installment.paid_amount
            
            db.session.commit()
            print(f"✅ تم إنشاء {len(pending_installments)} دفعة تجريبية")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الدفعات: {e}")
            db.session.rollback()

if __name__ == "__main__":
    print("🚀 إنشاء عقد تقسيط تجريبي...")
    contract_id = create_installment_contract()
    
    if contract_id:
        print("\n📊 إنشاء دفعات تجريبية...")
        create_sample_payments()
        
        print(f"\n🎯 تم الانتهاء! يمكنك الآن:")
        print(f"   📋 عرض العقد: http://127.0.0.1:9898/contracts/view/{contract_id}")
        print(f"   💳 إدارة الأقساط: http://127.0.0.1:9898/installments")
        print(f"   📊 تقارير الأقساط: http://127.0.0.1:9898/installments/reports")
        print(f"   🔔 الأقساط المستحقة: http://127.0.0.1:9898/installments/due-today")
