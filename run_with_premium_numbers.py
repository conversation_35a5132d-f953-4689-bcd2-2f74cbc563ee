#!/usr/bin/env python3
"""
Qatar Car Showroom - Enhanced with Premium Numbers Support
Run script with premium numbers integration in contracts
"""

import os
import sys
import subprocess
import time
import webbrowser
from datetime import datetime

def print_banner():
    """Print startup banner"""
    print("🚀 نظام معرض قطر للسيارات - مع دعم الأرقام المميزة في العقود")
    print("=" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)

def check_python():
    """Check Python version"""
    print(f"✅ Python {sys.version.split()[0]}")
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    return True

def install_package(package):
    """Install a single package"""
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', package
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except:
        return False

def install_requirements():
    """Install required packages"""
    print("📦 فحص وتثبيت المكتبات المطلوبة...")
    
    essential_packages = [
        'Flask',
        'Flask-SQLAlchemy', 
        'Flask-Login',
        'Werkzeug',
        'SQLAlchemy'
    ]
    
    for package in essential_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✅ {package}")
        except ImportError:
            print(f"📦 تثبيت {package}...")
            if install_package(package):
                print(f"✅ تم تثبيت {package}")
            else:
                print(f"⚠️ فشل في تثبيت {package}")
    
    print("✅ تم فحص جميع المكتبات")
    return True

def setup_database():
    """Setup database with premium numbers support"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        from werkzeug.security import generate_password_hash
        
        app = create_app()
        
        with app.app_context():
            # Create tables
            db.create_all()
            
            # Create admin user if not exists
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role='admin',
                    is_active=True
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء مستخدم admin")
            else:
                print("✅ مستخدم admin موجود")
        
        print("✅ تم إعداد قاعدة البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def start_server():
    """Start the Flask server"""
    print("🌐 بدء تشغيل الخادم...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        print("=" * 60)
        print("🎉 نظام معرض قطر للسيارات - مع دعم الأرقام المميزة")
        print("=" * 60)
        print("🌐 الخادم يعمل على: http://127.0.0.1:1414")
        print("🔐 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("✨ الميزات الجديدة:")
        print("   🚗 بيع السيارات")
        print("   🔢 بيع الأرقام المميزة")
        print("   🎯 بيع السيارات والأرقام المميزة معاً")
        print("   💳 طرق دفع متعددة (نقدي، تقسيط، إيجار، استبدال)")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)
        
        # Open browser after 3 seconds
        import threading
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://127.0.0.1:1414')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("💡 افتح المتصفح يدوياً على: http://127.0.0.1:1414")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        # Start server
        app.run(host='127.0.0.1', port=1414, debug=False, use_reloader=False)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        return False

def main():
    """Main function"""
    try:
        print_banner()
        
        if not check_python():
            input("\nاضغط Enter للخروج...")
            return
        
        if not install_requirements():
            print("⚠️ تحذير: بعض المكتبات قد لا تكون مثبتة")
        
        if not setup_database():
            input("\nاضغط Enter للخروج...")
            return
        
        start_server()
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    finally:
        input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
