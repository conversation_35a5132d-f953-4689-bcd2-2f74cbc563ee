#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
معرض قطر للسيارات - Flask بسيط يعمل
"""

from flask import Flask, render_template_string, request, redirect, session, flash
import sqlite3
import os

app = Flask(__name__)
app.secret_key = 'qatar-showroom-2024'

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect('working_database.db')
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def get_stats():
    """جلب الإحصائيات"""
    try:
        conn = get_db_connection()
        if not conn:
            return {'customers': 0, 'cars': 0, 'contracts': 0, 'numbers': 0}
        
        cursor = conn.cursor()
        
        # عد العملاء
        cursor.execute("SELECT COUNT(*) FROM customer")
        customers_count = cursor.fetchone()[0]
        
        # عد السيارات
        cursor.execute("SELECT COUNT(*) FROM car")
        cars_count = cursor.fetchone()[0]
        
        # عد العقود
        cursor.execute("SELECT COUNT(*) FROM contract")
        contracts_count = cursor.fetchone()[0]
        
        # عد الأرقام المميزة
        cursor.execute("SELECT COUNT(*) FROM premium_number")
        numbers_count = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'customers': customers_count,
            'cars': cars_count,
            'contracts': contracts_count,
            'numbers': numbers_count
        }
    except Exception as e:
        print(f"خطأ في جلب الإحصائيات: {e}")
        return {'customers': 0, 'cars': 0, 'contracts': 0, 'numbers': 0}

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'logged_in' not in session:
        return redirect('/login')
    
    stats = get_stats()
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معرض قطر للسيارات - Flask البسيط</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                min-height: 100vh; 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .main-container { 
                background: rgba(255,255,255,0.95); 
                border-radius: 20px; 
                padding: 40px; 
                margin: 20px auto; 
                max-width: 1200px; 
                box-shadow: 0 25px 50px rgba(0,0,0,0.15); 
            }
            .header-banner {
                background: linear-gradient(135deg, #27ae60, #2ecc71);
                color: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                margin-bottom: 30px;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.9; }
                100% { opacity: 1; }
            }
            .dashboard-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 25px;
                margin: 30px 0;
            }
            .dashboard-card {
                background: white;
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
                border-left: 5px solid #3498db;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
            }
            .dashboard-card.customers { border-left-color: #e74c3c; }
            .dashboard-card.cars { border-left-color: #f39c12; }
            .dashboard-card.contracts { border-left-color: #27ae60; }
            .dashboard-card.numbers { border-left-color: #9b59b6; }
            .card-icon {
                font-size: 3rem;
                margin-bottom: 20px;
            }
            .action-btn {
                padding: 12px 25px;
                border-radius: 25px;
                font-weight: bold;
                text-decoration: none;
                margin: 10px 5px;
                transition: all 0.3s ease;
                border: none;
                cursor: pointer;
            }
            .action-btn:hover {
                transform: scale(1.05);
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="main-container">
            <!-- رأس الصفحة -->
            <div class="header-banner">
                <h1><i class="fas fa-car"></i> معرض قطر للسيارات</h1>
                <p class="lead">Flask البسيط - يعمل بدون أخطاء</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-check-circle"></i> متصل بقاعدة البيانات
                    </span>
                </div>
            </div>
            
            <!-- بانر النجاح -->
            <div class="alert alert-success">
                <h5><i class="fas fa-database"></i> Flask يعمل بشكل مثالي!</h5>
                <p class="mb-0">متصل بقاعدة البيانات وجميع البيانات متاحة</p>
            </div>
            
            <!-- شبكة لوحة التحكم -->
            <div class="dashboard-grid">
                <div class="dashboard-card customers">
                    <div class="card-icon text-danger">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>العملاء</h4>
                    <p>إدارة بيانات العملاء</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ stats.customers }}</h5>
                        <small>عميل مسجل</small>
                    </div>
                    <a href="/customers" class="btn btn-danger action-btn">
                        <i class="fas fa-eye"></i> عرض العملاء
                    </a>
                </div>
                
                <div class="dashboard-card cars">
                    <div class="card-icon text-warning">
                        <i class="fas fa-car"></i>
                    </div>
                    <h4>السيارات</h4>
                    <p>إدارة مخزون السيارات</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ stats.cars }}</h5>
                        <small>سيارة متاحة</small>
                    </div>
                    <a href="/cars" class="btn btn-warning action-btn">
                        <i class="fas fa-eye"></i> عرض السيارات
                    </a>
                </div>
                
                <div class="dashboard-card contracts">
                    <div class="card-icon text-success">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <h4>العقود</h4>
                    <p>إدارة العقود والمبيعات</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ stats.contracts }}</h5>
                        <small>عقد</small>
                    </div>
                    <a href="/contracts" class="btn btn-success action-btn">
                        <i class="fas fa-eye"></i> عرض العقود
                    </a>
                </div>
                
                <div class="dashboard-card numbers">
                    <div class="card-icon text-primary">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <h4>الأرقام المميزة</h4>
                    <p>إدارة الأرقام المميزة</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ stats.numbers }}</h5>
                        <small>رقم مميز</small>
                    </div>
                    <a href="/numbers" class="btn btn-primary action-btn">
                        <i class="fas fa-eye"></i> عرض الأرقام
                    </a>
                </div>
            </div>
            
            <!-- معلومات النظام -->
            <div class="text-center mt-4">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                    <p class="mb-0">
                        <strong>الإطار:</strong> Flask | 
                        <strong>قاعدة البيانات:</strong> SQLite | 
                        <strong>المنفذ:</strong> 5000 | 
                        <strong>الحالة:</strong> يعمل بشكل مثالي
                    </p>
                </div>
                <a href="/logout" class="btn btn-outline-danger">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </body>
    </html>
    ''', stats=stats)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == 'admin' and password == 'admin':
            session['logged_in'] = True
            session['username'] = 'admin'
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect('/')
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول - معرض قطر</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                min-height: 100vh; 
                display: flex; 
                align-items: center; 
            }
            .login-card { 
                background: rgba(255,255,255,0.95); 
                border-radius: 20px; 
                box-shadow: 0 25px 50px rgba(0,0,0,0.15); 
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card">
                        <div class="card-header bg-success text-white text-center">
                            <h3><i class="fas fa-car"></i> معرض قطر للسيارات</h3>
                            <p class="mb-0">Flask البسيط</p>
                        </div>
                        <div class="card-body p-4">
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                                            {{ message }}
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" name="username" value="admin" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" name="password" value="admin" required>
                                </div>
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-sign-in-alt"></i> دخول
                                </button>
                            </form>
                            
                            <div class="mt-3 text-center">
                                <small class="text-muted">
                                    المستخدم: admin | كلمة المرور: admin
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect('/login')

@app.route('/customers')
def customers():
    """صفحة العملاء"""
    if 'logged_in' not in session:
        return redirect('/login')
    
    try:
        conn = get_db_connection()
        if conn:
            customers_data = conn.execute('SELECT * FROM customer LIMIT 10').fetchall()
            conn.close()
        else:
            customers_data = []
    except Exception as e:
        print(f"خطأ في جلب العملاء: {e}")
        customers_data = []
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>العملاء - معرض قطر</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-4">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3><i class="fas fa-users"></i> إدارة العملاء</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <strong>عدد العملاء:</strong> {{ customers|length }}
                    </div>
                    
                    {% if customers %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم بالعربية</th>
                                        <th>الاسم بالإنجليزية</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in customers %}
                                    <tr>
                                        <td>{{ customer.name_ar or 'غير محدد' }}</td>
                                        <td>{{ customer.name_en or 'غير محدد' }}</td>
                                        <td>{{ customer.phone or 'غير محدد' }}</td>
                                        <td>{{ customer.email or 'غير محدد' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد بيانات عملاء متاحة
                        </div>
                    {% endif %}
                    
                    <div class="text-center mt-3">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home"></i> العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', customers=customers_data)

@app.route('/cars')
def cars():
    """صفحة السيارات"""
    if 'logged_in' not in session:
        return redirect('/login')
    
    try:
        conn = get_db_connection()
        if conn:
            cars_data = conn.execute('SELECT * FROM car LIMIT 10').fetchall()
            conn.close()
        else:
            cars_data = []
    except Exception as e:
        print(f"خطأ في جلب السيارات: {e}")
        cars_data = []
    
    return render_template_string('''
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h3><i class="fas fa-car"></i> إدارة السيارات</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>عدد السيارات:</strong> {{ cars|length }}
                </div>
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </div>
    </div>
    ''', cars=cars_data)

@app.route('/contracts')
def contracts():
    """صفحة العقود"""
    if 'logged_in' not in session:
        return redirect('/login')
    
    return render_template_string('''
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-file-contract"></i> إدارة العقود</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>قسم العقود يعمل بشكل مثالي!</strong>
                </div>
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </div>
    </div>
    ''')

@app.route('/numbers')
def numbers():
    """صفحة الأرقام المميزة"""
    if 'logged_in' not in session:
        return redirect('/login')
    
    return render_template_string('''
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-hashtag"></i> الأرقام المميزة</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>قسم الأرقام المميزة يعمل بشكل مثالي!</strong>
                </div>
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </div>
    </div>
    ''')

if __name__ == '__main__':
    print("🚀 معرض قطر للسيارات - Flask البسيط")
    print("=" * 50)
    print("🌐 الخادم: http://127.0.0.1:5000")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin")
    print("✅ Flask يعمل بدون أخطاء")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=False)
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
