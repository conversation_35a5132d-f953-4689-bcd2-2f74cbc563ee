# Qatar Car Showroom - PowerShell Launcher
Write-Host "Qatar Car Showroom System" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Change to script directory
Set-Location $PSScriptRoot

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python found: $pythonVersion" -ForegroundColor Green
    
    # Run the application
    Write-Host "Starting application..." -ForegroundColor Yellow
    python working_app.py
    
} catch {
    Write-Host "Python not found, trying 'py' command..." -ForegroundColor Yellow
    try {
        $pyVersion = py --version 2>&1
        Write-Host "Python found: $pyVersion" -ForegroundColor Green
        py working_app.py
    } catch {
        Write-Host "ERROR: Python is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Python from python.org" -ForegroundColor Red
    }
}

Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
