#!/usr/bin/env python3
"""
اختبار بسيط لـ API - نظام معرض قطر للسيارات
"""

import requests
import time

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    base_url = "http://127.0.0.1:9898"
    
    print("🔗 اختبار الاتصال الأساسي...")
    
    try:
        # اختبار الصفحة الرئيسية
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ الصفحة الرئيسية: {response.status_code}")
        
        # اختبار API العداد
        response = requests.get(f"{base_url}/api/notifications/count", timeout=5)
        print(f"✅ API العداد: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 عدد الإشعارات: {data.get('count', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_dismiss_api_simple():
    """اختبار بسيط لـ API الحذف"""
    base_url = "http://127.0.0.1:9898"
    
    print("\n🗑️ اختبار API الحذف...")
    
    try:
        # اختبار بدون تسجيل دخول
        response = requests.post(
            f"{base_url}/api/notifications/dismiss",
            json={'type': 'test', 'id': '123'},
            timeout=5
        )
        
        print(f"✅ استجابة API: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   📝 الرسالة: {data.get('message', 'لا توجد رسالة')}")
            print(f"   ✅ النجاح: {data.get('success', False)}")
        elif response.status_code == 404:
            print("❌ API غير موجود (404)")
        else:
            print(f"❌ خطأ غير متوقع: {response.status_code}")
            print(f"   📄 المحتوى: {response.text[:200]}")
        
        return response.status_code != 404
        
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")
        return False

def test_with_session():
    """اختبار مع جلسة مسجلة"""
    base_url = "http://127.0.0.1:9898"
    
    print("\n🔐 اختبار مع تسجيل الدخول...")
    
    try:
        session = requests.Session()
        
        # تسجيل الدخول
        login_response = session.post(
            f"{base_url}/login",
            data={'username': 'admin', 'password': 'admin123'},
            timeout=5
        )
        
        print(f"✅ تسجيل الدخول: {login_response.status_code}")
        
        if login_response.status_code in [200, 302]:
            # اختبار API الحذف مع الجلسة
            dismiss_response = session.post(
                f"{base_url}/api/notifications/dismiss",
                json={'type': 'test_session', 'id': '456'},
                timeout=5
            )
            
            print(f"✅ API الحذف مع الجلسة: {dismiss_response.status_code}")
            
            if dismiss_response.status_code == 200:
                data = dismiss_response.json()
                print(f"   📝 الرسالة: {data.get('message', 'لا توجد رسالة')}")
                print(f"   ✅ النجاح: {data.get('success', False)}")
                return True
            elif dismiss_response.status_code == 404:
                print("❌ API غير موجود حتى مع الجلسة")
                return False
            else:
                print(f"❌ خطأ: {dismiss_response.status_code}")
                return False
        else:
            print("❌ فشل تسجيل الدخول")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def list_available_routes():
    """محاولة اكتشاف الروابط المتاحة"""
    base_url = "http://127.0.0.1:9898"
    
    print("\n🔍 اختبار الروابط المتاحة...")
    
    routes_to_test = [
        '/api/notifications/count',
        '/api/notifications/dismiss',
        '/api/notifications/restore',
        '/notifications',
        '/login',
        '/logout'
    ]
    
    for route in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=3)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {route}: متاح")
            elif status == 302:
                print(f"🔄 {route}: تحويل")
            elif status == 404:
                print(f"❌ {route}: غير موجود")
            elif status == 405:
                print(f"⚠️ {route}: طريقة غير مسموحة (موجود لكن يحتاج POST)")
            else:
                print(f"❓ {route}: {status}")
                
        except Exception as e:
            print(f"❌ {route}: خطأ في الاتصال")

if __name__ == '__main__':
    print("🚀 اختبار بسيط لـ API")
    print("=" * 50)
    
    # اختبار الاتصال الأساسي
    basic_test = test_basic_connection()
    
    if basic_test:
        # اختبار الروابط المتاحة
        list_available_routes()
        
        # اختبار API الحذف
        dismiss_test = test_dismiss_api_simple()
        
        # اختبار مع جلسة
        session_test = test_with_session()
        
        print("\n" + "=" * 50)
        print("📊 النتائج:")
        print(f"🔗 الاتصال الأساسي: {'✅' if basic_test else '❌'}")
        print(f"🗑️ API الحذف: {'✅' if dismiss_test else '❌'}")
        print(f"🔐 الاختبار مع الجلسة: {'✅' if session_test else '❌'}")
        
        if all([basic_test, dismiss_test, session_test]):
            print("\n🎉 جميع الاختبارات نجحت!")
        else:
            print("\n⚠️ هناك مشاكل تحتاج إلى حل")
    else:
        print("\n❌ فشل الاتصال الأساسي")
    
    print("\n" + "=" * 50)
