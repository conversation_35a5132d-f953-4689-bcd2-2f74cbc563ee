#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
import traceback
from datetime import datetime

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def check_python_environment():
    """فحص بيئة Python"""
    print_header("فحص بيئة Python")
    
    print(f"✅ إصدار Python: {sys.version}")
    print(f"✅ مسار Python: {sys.executable}")
    print(f"✅ مجلد العمل: {os.getcwd()}")
    
    # فحص المكتبات المطلوبة
    required_packages = ['flask', 'flask_sqlalchemy', 'werkzeug']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: متوفر")
        except ImportError:
            print(f"❌ {package}: غير متوفر")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("💡 لتثبيتها: pip install flask flask-sqlalchemy")
    
    return len(missing_packages) == 0

def check_database_files():
    """فحص ملفات قاعدة البيانات"""
    print_header("فحص ملفات قاعدة البيانات")
    
    db_files = [
        'auction.db',
        'working_database.db',
        'instance/auction.db'
    ]
    
    existing_files = []
    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f"✅ {db_file}: موجود ({size} بايت)")
            existing_files.append(db_file)
        else:
            print(f"❌ {db_file}: غير موجود")
    
    return existing_files

def check_database_structure(db_file):
    """فحص هيكل قاعدة البيانات"""
    print_header(f"فحص هيكل قاعدة البيانات: {db_file}")
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # جلب قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 عدد الجداول: {len(tables)}")
        
        table_info = {}
        for table in tables:
            table_name = table[0]
            print(f"\n📋 جدول: {table_name}")
            
            # جلب معلومات الأعمدة
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"   📝 عدد الأعمدة: {len(columns)}")
            table_info[table_name] = columns
            
            # جلب عدد الصفوف
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   📊 عدد الصفوف: {count}")
            except Exception as e:
                print(f"   ❌ خطأ في عد الصفوف: {e}")
        
        conn.close()
        return table_info
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return {}

def check_required_columns():
    """فحص الأعمدة المطلوبة"""
    print_header("فحص الأعمدة المطلوبة")
    
    required_columns = {
        'customer': ['name_en'],
        'car': ['features', 'notes', 'purchase_date', 'purchase_price'],
        'contract': [
            'warranty_months', 'insurance_required', 'registration_included',
            'special_conditions', 'discount_amount', 'tax_amount',
            'commission_amount', 'signed_by_customer', 'signed_by_dealer',
            'witness_name', 'witness_id', 'contract_file_path',
            'pdf_generated', 'word_generated'
        ]
    }
    
    # فحص قاعدة البيانات الرئيسية
    db_files = ['working_database.db', 'auction.db']
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            continue
            
        print(f"\n🔍 فحص: {db_file}")
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            for table_name, required_cols in required_columns.items():
                print(f"\n📋 جدول {table_name}:")
                
                # جلب أعمدة الجدول
                cursor.execute(f"PRAGMA table_info({table_name})")
                existing_columns = [col[1] for col in cursor.fetchall()]
                
                missing_columns = []
                for col in required_cols:
                    if col in existing_columns:
                        print(f"   ✅ {col}: موجود")
                    else:
                        print(f"   ❌ {col}: مفقود")
                        missing_columns.append(col)
                
                if missing_columns:
                    print(f"   ⚠️ أعمدة مفقودة: {', '.join(missing_columns)}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في فحص {db_file}: {e}")

def check_flask_app():
    """فحص تطبيق Flask"""
    print_header("فحص تطبيق Flask")
    
    try:
        # محاولة استيراد التطبيق
        sys.path.insert(0, os.getcwd())
        
        print("🔄 محاولة استيراد working_app...")
        import working_app
        print("✅ تم استيراد working_app بنجاح")
        
        # فحص التطبيق
        app = working_app.app
        print(f"✅ تطبيق Flask: {app}")
        print(f"✅ قاعدة البيانات: {app.config.get('SQLALCHEMY_DATABASE_URI')}")
        
        # فحص النماذج
        models = ['User', 'Customer', 'Car', 'Contract', 'PremiumNumber']
        for model_name in models:
            if hasattr(working_app, model_name):
                print(f"✅ نموذج {model_name}: موجود")
            else:
                print(f"❌ نموذج {model_name}: مفقود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print(f"📝 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def create_fixed_database():
    """إنشاء قاعدة بيانات مُصلحة"""
    print_header("إنشاء قاعدة بيانات مُصلحة")
    
    try:
        # حذف قواعد البيانات القديمة
        for db_file in ['working_database.db', 'auction.db']:
            if os.path.exists(db_file):
                os.remove(db_file)
                print(f"🗑️ حذف: {db_file}")
        
        # إنشاء مجلد instance
        if not os.path.exists('instance'):
            os.makedirs('instance')
        
        # إنشاء قاعدة البيانات الجديدة
        conn = sqlite3.connect('working_database.db')
        cursor = conn.cursor()
        
        print("🏗️ إنشاء الجداول...")
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                password_hash VARCHAR(120) NOT NULL,
                role VARCHAR(20) DEFAULT 'admin',
                is_active BOOLEAN DEFAULT 1,
                email VARCHAR(120),
                full_name VARCHAR(100),
                phone VARCHAR(20),
                avatar VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE customer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name_ar VARCHAR(100) NOT NULL,
                name_en VARCHAR(100),
                phone VARCHAR(20) NOT NULL,
                email VARCHAR(100),
                id_number VARCHAR(20),
                nationality VARCHAR(50),
                birth_date DATE,
                address TEXT,
                city VARCHAR(50),
                postal_code VARCHAR(10),
                profession VARCHAR(100),
                company VARCHAR(100),
                monthly_income REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            )
        ''')
        
        # جدول السيارات
        cursor.execute('''
            CREATE TABLE car (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                make VARCHAR(50) NOT NULL,
                model VARCHAR(50) NOT NULL,
                year INTEGER NOT NULL,
                price REAL NOT NULL,
                status VARCHAR(20) DEFAULT 'available',
                color VARCHAR(30),
                fuel_type VARCHAR(20),
                transmission VARCHAR(20),
                engine_size VARCHAR(20),
                mileage INTEGER,
                body_type VARCHAR(30),
                doors INTEGER,
                seats INTEGER,
                vin_number VARCHAR(50),
                license_plate VARCHAR(20),
                insurance_expiry DATE,
                registration_expiry DATE,
                condition VARCHAR(20),
                features TEXT,
                notes TEXT,
                purchase_date DATE,
                purchase_price REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الأرقام المميزة
        cursor.execute('''
            CREATE TABLE premium_number (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                number VARCHAR(20) NOT NULL,
                category VARCHAR(20) NOT NULL,
                price REAL NOT NULL,
                status VARCHAR(20) DEFAULT 'available'
            )
        ''')
        
        # جدول العقود
        cursor.execute('''
            CREATE TABLE contract (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_number VARCHAR(50) UNIQUE NOT NULL,
                contract_type VARCHAR(30) NOT NULL,
                payment_type VARCHAR(30),
                customer_id INTEGER NOT NULL,
                car_id INTEGER,
                premium_number_id INTEGER,
                total_amount REAL NOT NULL,
                down_payment REAL DEFAULT 0,
                remaining_amount REAL DEFAULT 0,
                monthly_payment REAL DEFAULT 0,
                installment_months INTEGER DEFAULT 0,
                interest_rate REAL DEFAULT 0,
                contract_date DATE NOT NULL,
                delivery_date DATE,
                warranty_months INTEGER DEFAULT 0,
                insurance_required BOOLEAN DEFAULT 0,
                registration_included BOOLEAN DEFAULT 0,
                special_conditions TEXT,
                notes TEXT,
                discount_amount REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                commission_amount REAL DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft',
                signed_by_customer BOOLEAN DEFAULT 0,
                signed_by_dealer BOOLEAN DEFAULT 0,
                witness_name VARCHAR(100),
                witness_id VARCHAR(20),
                contract_file_path VARCHAR(255),
                pdf_generated BOOLEAN DEFAULT 0,
                word_generated BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (customer_id) REFERENCES customer (id),
                FOREIGN KEY (car_id) REFERENCES car (id),
                FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')
        
        # جدول دفعات التقسيط
        cursor.execute('''
            CREATE TABLE installment_payment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                installment_number INTEGER NOT NULL,
                due_date DATE NOT NULL,
                amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                payment_date DATE,
                status VARCHAR(20) DEFAULT 'pending',
                late_fee REAL DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (contract_id) REFERENCES contract (id)
            )
        ''')
        
        # جدول مستندات العقد
        cursor.execute('''
            CREATE TABLE contract_document (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                document_type VARCHAR(30) NOT NULL,
                document_name VARCHAR(100) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_size INTEGER,
                mime_type VARCHAR(50),
                uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                uploaded_by INTEGER,
                FOREIGN KEY (contract_id) REFERENCES contract (id),
                FOREIGN KEY (uploaded_by) REFERENCES user (id)
            )
        ''')
        
        print("📝 إدراج البيانات التجريبية...")
        
        # إدراج مستخدم admin
        cursor.execute('''
            INSERT INTO user (username, password_hash, role, email, full_name)
            VALUES ('admin', 'pbkdf2:sha256:260000$salt$hash', 'admin', '<EMAIL>', 'مدير النظام')
        ''')
        
        # إدراج عملاء
        customers = [
            ('أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '50123456', '<EMAIL>', '12345678901'),
            ('فاطمة علي الأنصاري', 'Fatima Ali Al-Ansari', '50123457', '<EMAIL>', '12345678902'),
            ('محمد سالم المري', 'Mohammed Salem Al-Marri', '50123458', '<EMAIL>', '12345678903')
        ]
        
        for customer in customers:
            cursor.execute('''
                INSERT INTO customer (name_ar, name_en, phone, email, id_number, nationality, city)
                VALUES (?, ?, ?, ?, ?, 'قطري', 'الدوحة')
            ''', customer)
        
        # إدراج سيارات
        cars = [
            ('تويوتا', 'كامري', 2023, 150000, 'available', 'أبيض', 'بنزين', 'أوتوماتيك', '2.5L', 5000, 'سيدان', 4, 5, 'TOY123456789', 'ABC123', '2024-12-31', '2024-12-31', 'ممتاز', 'مكيف، نظام ملاحة، كاميرا خلفية', 'سيارة بحالة ممتازة', '2023-01-15', 140000),
            ('نيسان', 'التيما', 2023, 140000, 'available', 'أسود', 'بنزين', 'أوتوماتيك', '2.0L', 3000, 'سيدان', 4, 5, 'NIS123456789', 'DEF456', '2024-12-31', '2024-12-31', 'ممتاز', 'مكيف، بلوتوث، مقاعد جلد', 'سيارة حديثة', '2023-02-20', 130000)
        ]
        
        for car in cars:
            cursor.execute('''
                INSERT INTO car 
                (make, model, year, price, status, color, fuel_type, transmission, engine_size, mileage, body_type, doors, seats, vin_number, license_plate, insurance_expiry, registration_expiry, condition, features, notes, purchase_date, purchase_price)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', car)
        
        # إدراج أرقام مميزة
        premium_numbers = [
            ('777', 'VIP', 300000, 'available'),
            ('999', 'VIP', 450000, 'available'),
            ('555', 'Premium', 180000, 'available')
        ]
        
        for pn in premium_numbers:
            cursor.execute('''
                INSERT INTO premium_number (number, category, price, status)
                VALUES (?, ?, ?, ?)
            ''', pn)
        
        conn.commit()
        
        # نسخ قاعدة البيانات
        import shutil
        shutil.copy2('working_database.db', 'auction.db')
        shutil.copy2('working_database.db', 'instance/auction.db')
        
        conn.close()
        
        print("✅ تم إنشاء قاعدة البيانات المُصلحة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def test_flask_app():
    """اختبار تطبيق Flask"""
    print_header("اختبار تطبيق Flask")
    
    try:
        # محاولة تشغيل التطبيق
        import working_app
        
        with working_app.app.app_context():
            # اختبار الاتصال بقاعدة البيانات
            from working_app import db, User, Customer, Car
            
            print("🔄 اختبار الاتصال بقاعدة البيانات...")
            
            # عد المستخدمين
            user_count = User.query.count()
            print(f"✅ عدد المستخدمين: {user_count}")
            
            # عد العملاء
            customer_count = Customer.query.count()
            print(f"✅ عدد العملاء: {customer_count}")
            
            # عد السيارات
            car_count = Car.query.count()
            print(f"✅ عدد السيارات: {car_count}")
            
            print("✅ تطبيق Flask يعمل بشكل صحيح!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للتشخيص والإصلاح"""
    print("🚀 أداة التشخيص والإصلاح الشاملة")
    print("=" * 60)
    
    # 1. فحص بيئة Python
    python_ok = check_python_environment()
    
    # 2. فحص ملفات قاعدة البيانات
    existing_dbs = check_database_files()
    
    # 3. فحص هيكل قاعدة البيانات
    for db_file in existing_dbs:
        check_database_structure(db_file)
    
    # 4. فحص الأعمدة المطلوبة
    check_required_columns()
    
    # 5. فحص تطبيق Flask
    flask_ok = check_flask_app()
    
    # 6. الإصلاح إذا لزم الأمر
    if not flask_ok or not existing_dbs:
        print_header("تطبيق الإصلاح")
        
        if create_fixed_database():
            print("✅ تم إصلاح قاعدة البيانات")
            
            # اختبار التطبيق بعد الإصلاح
            if test_flask_app():
                print_header("النتيجة النهائية")
                print("🎉 تم إصلاح جميع المشاكل بنجاح!")
                print("🚀 يمكنك الآن تشغيل النظام:")
                print("   python working_app.py")
            else:
                print("❌ ما زالت هناك مشاكل في التطبيق")
        else:
            print("❌ فشل في إصلاح قاعدة البيانات")
    else:
        print_header("النتيجة النهائية")
        print("✅ النظام يبدو سليماً!")
        print("🚀 يمكنك تشغيل النظام:")
        print("   python working_app.py")

if __name__ == '__main__':
    main()
