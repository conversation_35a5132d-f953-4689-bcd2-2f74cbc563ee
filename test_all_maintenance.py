#!/usr/bin/env python3
"""
اختبار شامل لنظام الصيانة والفحص - نظام معرض قطر للسيارات
"""

import requests
import json

def login_session():
    """تسجيل الدخول وإرجاع الجلسة"""
    base_url = "http://127.0.0.1:9898"
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        return session, base_url
    else:
        return None, None

def test_maintenance_pages():
    """اختبار جميع صفحات الصيانة"""
    session, base_url = login_session()
    
    if not session:
        print("❌ فشل تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # قائمة الصفحات للاختبار
    pages = [
        ('/maintenance', 'لوحة تحكم الصيانة'),
        ('/maintenance/add', 'إضافة صيانة'),
        ('/maintenance/add_inspection', 'إضافة فحص'),
        ('/maintenance/all', 'جميع الصيانات'),
        ('/maintenance/inspections', 'جميع الفحوصات'),
        ('/maintenance/reports', 'تقارير الصيانة'),
        ('/maintenance/calendar', 'تقويم الصيانة')
    ]
    
    results = {}
    
    for url, name in pages:
        try:
            response = session.get(f"{base_url}{url}")
            status = response.status_code
            
            if status == 200:
                print(f"✅ {name}: يعمل بنجاح")
                results[url] = True
            else:
                print(f"❌ {name}: خطأ {status}")
                results[url] = False
                
        except Exception as e:
            print(f"❌ {name}: خطأ في الاتصال - {e}")
            results[url] = False
    
    return results

def test_maintenance_features():
    """اختبار ميزات الصيانة المحددة"""
    session, base_url = login_session()
    
    if not session:
        return False
    
    print("\n🔍 اختبار الميزات المحددة...")
    
    # اختبار لوحة تحكم الصيانة
    try:
        response = session.get(f"{base_url}/maintenance")
        content = response.text
        
        features = [
            ('إدارة الصيانة والفحص', 'العنوان الرئيسي'),
            ('إضافة صيانة', 'زر إضافة الصيانة'),
            ('إضافة فحص', 'زر إضافة الفحص'),
            ('إجمالي الصيانات', 'إحصائيات الصيانة'),
            ('في الانتظار', 'حالة الصيانة'),
            ('قيد التنفيذ', 'حالة الصيانة'),
            ('مكتملة', 'حالة الصيانة'),
            ('السيارات في الصيانة', 'قسم السيارات'),
            ('صيانة مستحقة قريباً', 'التنبيهات'),
            ('آخر الصيانات', 'قائمة الصيانات'),
            ('إجراءات سريعة', 'الروابط السريعة')
        ]
        
        for feature, description in features:
            if feature in content:
                print(f"✅ {description}: موجود")
            else:
                print(f"❌ {description}: غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")
        return False

def test_api_integration():
    """اختبار تكامل API"""
    base_url = "http://127.0.0.1:9898"
    
    print("\n🔗 اختبار تكامل API...")
    
    try:
        # اختبار API الإشعارات
        response = requests.get(f"{base_url}/api/notifications/count")
        
        if response.status_code == 200:
            data = response.json()
            
            # التحقق من وجود بيانات الصيانة في API
            maintenance_fields = ['upcoming_maintenance', 'expiring_inspections']
            
            for field in maintenance_fields:
                if field in data:
                    print(f"✅ {field}: {data[field]}")
                else:
                    print(f"❌ {field}: غير موجود في API")
            
            return True
        else:
            print(f"❌ API الإشعارات: خطأ {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

if __name__ == '__main__':
    print("🚀 اختبار شامل لنظام الصيانة والفحص")
    print("=" * 60)
    
    # اختبار الصفحات
    print("📄 اختبار صفحات الصيانة...")
    page_results = test_maintenance_pages()
    
    # اختبار الميزات
    feature_test = test_maintenance_features()
    
    # اختبار API
    api_test = test_api_integration()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print("=" * 60)
    
    if page_results:
        working_pages = sum(1 for result in page_results.values() if result)
        total_pages = len(page_results)
        print(f"📄 الصفحات: {working_pages}/{total_pages} تعمل")
        
        for url, working in page_results.items():
            status = "✅" if working else "❌"
            print(f"   {status} {url}")
    
    print(f"🎯 الميزات: {'✅ تعمل' if feature_test else '❌ لا تعمل'}")
    print(f"🔗 API: {'✅ يعمل' if api_test else '❌ لا يعمل'}")
    
    # النتيجة النهائية
    if page_results and all(page_results.values()) and feature_test and api_test:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✨ نظام الصيانة والفحص يعمل بشكل مثالي!")
        print("🔧 جميع الصفحات والميزات متاحة")
        print("📊 API مدمج بنجاح مع نظام الإشعارات")
    else:
        print("\n⚠️ هناك بعض المشاكل:")
        if page_results and not all(page_results.values()):
            print("   📄 بعض الصفحات لا تعمل")
        if not feature_test:
            print("   🎯 بعض الميزات غير متاحة")
        if not api_test:
            print("   🔗 مشكلة في تكامل API")
    
    print("\n" + "=" * 60)
