#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Customer, PremiumNumber, Auction, Bid
from datetime import datetime, timedelta

def create_simple_test():
    with app.app_context():
        print("🔧 إنشاء بيانات تجريبية بسيطة...")
        
        # إنشاء الجداول
        db.create_all()
        
        # حذف البيانات القديمة
        Bid.query.delete()
        Auction.query.delete()
        Customer.query.delete()
        PremiumNumber.query.delete()
        db.session.commit()
        
        # إنشاء عميل
        customer = Customer(
            name_ar="أحمد محمد الكعبي",
            phone="+974 5555 1234",
            email="<EMAIL>"
        )
        db.session.add(customer)
        db.session.commit()
        print(f"✅ تم إنشاء العميل: {customer.name_ar}")
        
        # إنشاء رقم مميز
        premium_number = PremiumNumber(
            number="777777",
            category="VIP",
            price=75000,
            status="auction"
        )
        db.session.add(premium_number)
        db.session.commit()
        print(f"✅ تم إنشاء الرقم المميز: {premium_number.number}")
        
        # إنشاء مزاد منتهي
        end_time = datetime.now() - timedelta(hours=1)
        auction = Auction(
            title=f"مزاد الرقم المميز {premium_number.number}",
            description="مزاد تجريبي لاختبار عرض الفائز",
            premium_number_id=premium_number.id,
            starting_price=50000,
            current_price=78850,
            reserve_price=60000,
            bid_increment=1000,
            start_time=end_time - timedelta(hours=2),
            end_time=end_time,
            status="ended",
            auto_extend=False,
            commission_rate=5.0,
            commission_amount=3942.5,
            total_bids=1
        )
        db.session.add(auction)
        db.session.commit()
        print(f"✅ تم إنشاء المزاد: {auction.title}")
        
        # إنشاء مزايدة فائزة
        bid = Bid(
            auction_id=auction.id,
            customer_id=customer.id,
            bid_amount=78850,
            bid_time=end_time - timedelta(minutes=5)
        )
        db.session.add(bid)
        db.session.commit()
        print(f"✅ تم إنشاء المزايدة الفائزة: {bid.bid_amount:,.0f} ر.ق")
        
        print("\n" + "="*50)
        print("✅ تم إنشاء البيانات التجريبية بنجاح!")
        print(f"🏆 الفائز: {customer.name_ar}")
        print(f"💰 المبلغ: {bid.bid_amount:,.0f} ر.ق")
        print(f"🌐 اختبر: http://127.0.0.1:9898/auction/standalone/{auction.id}")
        print("="*50)

if __name__ == "__main__":
    create_simple_test()
