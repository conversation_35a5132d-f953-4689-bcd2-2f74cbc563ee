#!/usr/bin/env python3
"""
تحديث قاعدة البيانات لإضافة الحقول الجديدة للسيارات
"""

import sqlite3
import os

def update_car_table():
    """تحديث جدول السيارات بإضافة الحقول الجديدة"""
    
    # الحصول على مسار قاعدة البيانات
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "working_database.db")
    
    print("🔧 تحديث قاعدة البيانات لإضافة الحقول الجديدة للسيارات")
    print("=" * 60)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # قائمة الحقول الجديدة المراد إضافتها
        new_columns = [
            ("color", "VARCHAR(30)"),
            ("fuel_type", "VARCHAR(20)"),
            ("transmission", "VARCHAR(20)"),
            ("engine_size", "VARCHAR(20)"),
            ("mileage", "INTEGER"),
            ("body_type", "VARCHAR(30)"),
            ("doors", "INTEGER"),
            ("seats", "INTEGER"),
            ("vin_number", "VARCHAR(50)"),
            ("license_plate", "VARCHAR(20)"),
            ("insurance_expiry", "DATE"),
            ("registration_expiry", "DATE"),
            ("condition", "VARCHAR(20)"),
            ("features", "TEXT"),
            ("notes", "TEXT"),
            ("purchase_date", "DATE"),
            ("purchase_price", "FLOAT"),
            ("created_at", "DATETIME")
        ]
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='car'")
        if not cursor.fetchone():
            print("❌ جدول السيارات غير موجود")
            return False
        
        # الحصول على الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(car)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        print(f"📋 الأعمدة الموجودة: {existing_columns}")
        
        # إضافة الأعمدة الجديدة
        added_count = 0
        for column_name, column_type in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE car ADD COLUMN {column_name} {column_type}"
                    cursor.execute(sql)
                    print(f"✅ تم إضافة العمود: {column_name}")
                    added_count += 1
                except Exception as e:
                    print(f"❌ خطأ في إضافة العمود {column_name}: {e}")
            else:
                print(f"⚠️ العمود {column_name} موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        
        print("=" * 60)
        print(f"🎉 تم تحديث قاعدة البيانات بنجاح!")
        print(f"📊 تم إضافة {added_count} عمود جديد")
        
        # عرض هيكل الجدول المحدث
        cursor.execute("PRAGMA table_info(car)")
        columns = cursor.fetchall()
        print("\n📋 هيكل جدول السيارات المحدث:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False
    
    finally:
        if conn:
            conn.close()

def update_sample_data():
    """تحديث البيانات النموذجية بقيم افتراضية للحقول الجديدة"""
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "working_database.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # تحديث السيارات الموجودة بقيم افتراضية
        updates = [
            ("color", "أبيض"),
            ("fuel_type", "بنزين"),
            ("transmission", "أوتوماتيك"),
            ("body_type", "سيدان"),
            ("doors", 4),
            ("seats", 5),
            ("condition", "جيدة"),
            ("created_at", "datetime('now')")
        ]
        
        for column, default_value in updates:
            if column == "created_at":
                sql = f"UPDATE car SET {column} = {default_value} WHERE {column} IS NULL"
            else:
                sql = f"UPDATE car SET {column} = ? WHERE {column} IS NULL"
                cursor.execute(sql, (default_value,))
                continue
            cursor.execute(sql)
        
        conn.commit()
        print("✅ تم تحديث البيانات النموذجية")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث البيانات: {e}")
    
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚗 تحديث نظام إدارة السيارات")
    print("=" * 60)
    
    if update_car_table():
        update_sample_data()
        print("\n🎉 تم تحديث النظام بنجاح!")
        print("💡 يمكنك الآن تشغيل النظام مع الميزات الجديدة")
    else:
        print("\n❌ فشل في تحديث النظام")
    
    input("\nاضغط Enter للخروج...")
