#!/usr/bin/env python3

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <html dir="rtl">
    <head><title>معرض قطر للسيارات</title><meta charset="utf-8"></head>
    <body style="font-family: Arial; text-align: center; padding: 50px;">
        <h1>🎉 نظام معرض قطر للسيارات</h1>
        <h2>✅ تم حل مشكلة config.py بنجاح!</h2>
        <div style="background: #e8f5e8; padding: 20px; margin: 20px; border-radius: 10px;">
            <h3>🔢 الميزة الجديدة: دعم الأرقام المميزة في العقود</h3>
            <p>✅ تم إضافة إمكانية بيع الأرقام المميزة</p>
            <p>✅ دعم العقود المدمجة (سيارة + رقم مميز)</p>
            <p>✅ فصل نوع العقد عن طريقة الدفع</p>
            <p>✅ تحديث قاعدة البيانات بنجاح</p>
        </div>
        <p><strong>النظام جاهز للاستخدام!</strong></p>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 نظام معرض قطر للسيارات - اختبار سريع")
    print("🌐 http://127.0.0.1:1414")
    print("✅ تم حل مشكلة config.py")
    print("🔢 الميزة الجديدة: دعم الأرقام المميزة في العقود")
    app.run(host='127.0.0.1', port=1414, debug=False)
