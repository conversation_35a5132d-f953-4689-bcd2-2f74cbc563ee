#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template_string
from datetime import datetime
import random

app = Flask(__name__)
app.secret_key = 'qatar-auction-2024'

# قالب HTML للمزاد
AUCTION_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مزاد الرقم المميز {{ number }} - عرض مستقل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .auction-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(231, 76, 60, 0.9);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .countdown-timer {
            font-weight: bold;
            color: #f1c40f;
        }
        .number-display {
            font-size: 5rem;
            font-weight: bold;
            color: #e74c3c;
            margin: 20px 0;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.2);
        }
        .timer-section {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
        }
        .timer {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .winning-bid {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
        }
        .bid-amount {
            font-size: 3rem;
            font-weight: bold;
            margin: 15px 0;
        }
        .winner-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: #e74c3c !important;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .stats-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
        }
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        .current-time {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(52, 73, 94, 0.9);
            color: white;
            padding: 12px 18px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="refresh-indicator" id="refreshIndicator">
        <i class="fas fa-sync-alt fa-spin"></i> 
        <span id="refreshText">عرض مستقل - تحديث تلقائي كل 3 ثوان</span>
        <span id="refreshCountdown" class="countdown-timer"></span>
    </div>

    <div class="current-time" id="currentTime"></div>

    <div class="auction-card">
        <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 8px 16px; border-radius: 20px; margin-bottom: 20px; display: inline-block;">
            <i class="fas fa-fire"></i> مزاد نشط
        </div>
        <div class="number-display">{{ number }}</div>
        <div style="font-size: 1.8rem; color: #2c3e50; margin-bottom: 30px;">مزاد الرقم المميز {{ number }}</div>

        <div class="timer-section">
            <div class="timer">
                <i class="fas fa-hourglass-half"></i>
                <span>{{ time_remaining }}</span>
            </div>
            <div style="font-size: 1.1rem;">متبقي على انتهاء المزاد</div>
        </div>

        <div class="winning-bid">
            <div style="font-size: 1.2rem; margin-bottom: 10px;">
                <i class="fas fa-trophy"></i> المزايدة الرابحة
            </div>
            <div class="bid-amount">{{ current_price:,.0f }} ر.ق</div>
            <div class="winner-name">
                <i class="fas fa-user"></i>
                <strong>{{ winner_name }}</strong>
            </div>
            <small>آخر مزايدة: {{ last_bid_time }}</small>
        </div>

        <div class="stats-row">
            <div class="stat-item">
                <div class="stat-number">{{ total_bids }}</div>
                <div class="stat-label">مزايدة</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ viewers }}</div>
                <div class="stat-label">مشاهدة</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ followers }}</div>
                <div class="stat-label">متابع</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5.0%</div>
                <div class="stat-label">عمولة المحل</div>
            </div>
        </div>
    </div>

    <script>
        let refreshInterval;
        let countdownInterval;
        let countdownTime = 3;

        function updateCountdown() {
            const countdownElement = document.getElementById('refreshCountdown');
            if (countdownElement) {
                countdownElement.textContent = `(${countdownTime}s)`;
                countdownTime--;
                if (countdownTime < 0) {
                    countdownTime = 3;
                }
            }
        }

        function updateCurrentTime() {
            const timeElement = document.getElementById('currentTime');
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-QA');
            timeElement.textContent = `الوقت الحالي: ${timeString}`;
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                location.reload();
            }, 3000);
            
            countdownInterval = setInterval(updateCountdown, 1000);
            updateCountdown();
        }

        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();
        startAutoRefresh();
    </script>
</body>
</html>
"""

@app.route('/')
def home():
    return '''
    <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white;">
        <h1>🎉 خادم المزادات يعمل!</h1>
        <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 20px; margin: 20px auto; max-width: 500px;">
            <h2>المزادات المتاحة:</h2>
            <p><a href="/auction/777" style="color: #f1c40f; text-decoration: none; font-size: 1.2rem;">🔢 مزاد الرقم 777</a></p>
            <p><a href="/auction/999" style="color: #f1c40f; text-decoration: none; font-size: 1.2rem;">🔢 مزاد الرقم 999</a></p>
            <p><a href="/auction/555" style="color: #f1c40f; text-decoration: none; font-size: 1.2rem;">🔢 مزاد الرقم 555</a></p>
        </div>
    </div>
    '''

@app.route('/auction/<number>')
def auction_standalone(number):
    current_time = datetime.now()
    
    # بيانات تجريبية متغيرة
    prices = {
        '777': 303000 + random.randint(-5000, 10000),
        '999': 450000 + random.randint(-10000, 15000),
        '555': 180000 + random.randint(-3000, 8000)
    }
    
    winners = ['أحمد محمد الكعبي', 'فاطمة علي الأنصاري', 'محمد سالم المري', 'نورا خالد الثاني']
    
    data = {
        'number': number,
        'current_price': prices.get(number, 250000),
        'winner_name': random.choice(winners),
        'total_bids': random.randint(20, 50),
        'viewers': random.randint(1, 8),
        'followers': random.randint(15, 35),
        'time_remaining': f"{random.randint(3, 8)}:{random.randint(10, 59):02d}",
        'last_bid_time': current_time.strftime('%H:%M:%S')
    }
    
    return render_template_string(AUCTION_TEMPLATE, **data)

if __name__ == '__main__':
    print("🚀 خادم المزادات البسيط - بدون أخطاء")
    print("=" * 50)
    print("🌐 الخادم: http://127.0.0.1:8080")
    print("🔢 مزاد 777: http://127.0.0.1:8080/auction/777")
    print("🔢 مزاد 999: http://127.0.0.1:8080/auction/999")
    print("🔢 مزاد 555: http://127.0.0.1:8080/auction/555")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    app.run(host='127.0.0.1', port=8080, debug=False)
