#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Auction, PremiumNumber
from datetime import datetime, timedelta

def create_scheduled_auction():
    """Create a scheduled auction for testing the new date/time feature"""
    with app.app_context():
        # Find an available premium number
        available_number = PremiumNumber.query.filter_by(status='available').first()
        
        if not available_number:
            print("❌ لا توجد أرقام متاحة")
            return
        
        # Create a scheduled auction (starts in 2 minutes)
        start_time = datetime.now() + timedelta(minutes=2)
        end_time = start_time + timedelta(minutes=10)  # 10 minutes duration
        
        auction = Auction(
            title=f"مزاد مجدول للرقم المميز {available_number.number}",
            premium_number_id=available_number.id,
            starting_price=available_number.price,
            current_price=available_number.price,
            start_time=start_time,
            end_time=end_time,
            status='scheduled',  # Scheduled status
            commission_rate=6.0,  # 6% commission
            total_bids=0
        )
        
        # Update number status
        available_number.status = 'auction'
        
        # Save to database
        db.session.add(auction)
        db.session.commit()
        
        print(f"✅ تم إنشاء مزاد مجدول للرقم {available_number.number}")
        print(f"🕐 وقت البداية: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🕐 وقت النهاية: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔗 رابط تفاصيل المزاد: http://127.0.0.1:9898/auction/details/{auction.id}")
        print(f"🔗 رابط الصفحة المستقلة: http://127.0.0.1:9898/auction/{auction.id}/standalone")
        
        return auction.id

if __name__ == '__main__':
    auction_id = create_scheduled_auction()
    if auction_id:
        print(f"\n🎯 يمكنك الآن مراقبة المزاد المجدول:")
        print(f"http://127.0.0.1:9898/auction/details/{auction_id}")
        print(f"\n⏰ سيبدأ المزاد تلقائياً خلال دقيقتين!")
