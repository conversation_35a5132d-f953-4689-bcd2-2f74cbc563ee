#!/usr/bin/env python3
"""
اختبار وظيفة حذف الإشعارات في الصفحة الرئيسية
"""

import requests
import json

def test_dismiss_all_api():
    """اختبار API حذف جميع الإشعارات"""
    base_url = "http://127.0.0.1:9898"
    
    print("🧪 اختبار API حذف جميع الإشعارات")
    print("=" * 50)
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        # تسجيل الدخول أولاً
        print("🔐 تسجيل الدخول...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code in [200, 302]:
            print("✅ تم تسجيل الدخول بنجاح")
            
            # اختبار API حذف جميع الإشعارات
            print("\n🗑️ اختبار حذف جميع الإشعارات...")
            
            dismiss_data = {
                'action': 'dismiss_all_homepage'
            }
            
            response = session.post(
                f"{base_url}/api/notifications/dismiss-all",
                json=dismiss_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📊 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ النجاح: {data.get('success')}")
                print(f"📝 الرسالة: {data.get('message')}")
                print(f"🔢 عدد الإشعارات المحذوفة: {data.get('dismissed_count', 0)}")
                
                if data.get('success'):
                    print("\n🎉 تم اختبار API بنجاح!")
                    return True
                else:
                    print(f"\n❌ فشل في حذف الإشعارات: {data.get('message')}")
                    return False
            else:
                print(f"❌ خطأ في API: {response.status_code}")
                print(f"📄 المحتوى: {response.text[:200]}")
                return False
                
        else:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_notifications_count():
    """اختبار عداد الإشعارات"""
    base_url = "http://127.0.0.1:9898"
    
    print("\n📊 اختبار عداد الإشعارات...")
    
    session = requests.Session()
    
    try:
        # تسجيل الدخول
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post(f"{base_url}/login", data=login_data)
        
        # جلب عداد الإشعارات
        response = session.get(f"{base_url}/api/notifications/count")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ إجمالي الإشعارات: {data.get('count', 0)}")
            print(f"📅 أقساط مستحقة اليوم: {data.get('due_today', 0)}")
            print(f"⚠️ أقساط متأخرة: {data.get('overdue', 0)}")
            print(f"🔧 سيارات في الصيانة: {data.get('cars_in_maintenance', 0)}")
            return True
        else:
            print(f"❌ خطأ في جلب العداد: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار العداد: {e}")
        return False

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🚀 اختبار وظائف الإشعارات")
    print("=" * 60)
    
    # اختبار عداد الإشعارات أولاً
    count_success = test_notifications_count()
    
    # اختبار حذف جميع الإشعارات
    dismiss_success = test_dismiss_all_api()
    
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    print(f"   📊 عداد الإشعارات: {'✅ نجح' if count_success else '❌ فشل'}")
    print(f"   🗑️ حذف الإشعارات: {'✅ نجح' if dismiss_success else '❌ فشل'}")
    
    if count_success and dismiss_success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن استخدام زر حذف الإشعارات في الصفحة الرئيسية")
    else:
        print("\n⚠️ بعض الاختبارات فشلت - تحقق من الأخطاء أعلاه")

if __name__ == '__main__':
    main()
