#!/usr/bin/env python3
"""
إنشاء مزاد تجريبي سريع
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Auction, PremiumNumber
from datetime import datetime, timed<PERSON><PERSON>

def create_demo_auction():
    """إنشاء مزاد تجريبي سريع"""
    with app.app_context():
        try:
            # البحث عن رقم متاح
            number = PremiumNumber.query.filter_by(status='available').first()
            if not number:
                print("❌ لا توجد أرقام متاحة")
                return False

            # حساب الأوقات - مزاد لمدة 2 دقيقة
            start_time = datetime.now() + timedelta(seconds=15)  # يبدأ خلال 15 ثانية
            end_time = start_time + timedelta(minutes=2)  # ينتهي بعد دقيقتين

            # إنشاء المزاد
            auction = Auction(
                premium_number_id=number.id,
                title=f"مزاد تجريبي للرقم {number.number}",
                description=f"مزاد تجريبي سريع لمدة دقيقتين للرقم المميز {number.number}",
                starting_price=number.price,
                bid_increment=500,
                start_time=start_time,
                end_time=end_time,
                commission_rate=5.0,
                auto_extend=True,
                created_by=1
            )

            db.session.add(auction)
            
            # تحديث حالة الرقم
            number.status = 'auction'
            
            db.session.commit()

            print("✅ تم إنشاء مزاد تجريبي بنجاح!")
            print(f"   📋 الرقم: {number.number}")
            print(f"   🕐 يبدأ في: {start_time.strftime('%H:%M:%S')}")
            print(f"   ⏰ ينتهي في: {end_time.strftime('%H:%M:%S')}")
            print(f"   💰 السعر الابتدائي: {number.price:,.0f} ر.ق")
            print(f"   🌐 رابط المزاد: http://127.0.0.1:9898/auction/details/{auction.id}")
            print(f"   📺 العرض المستقل: http://127.0.0.1:9898/auction/standalone/{auction.id}")
            
            return auction.id

        except Exception as e:
            print(f"❌ خطأ في إنشاء المزاد: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("🎯 إنشاء مزاد تجريبي سريع...")
    create_demo_auction()
    input("\nاضغط Enter للخروج...")
