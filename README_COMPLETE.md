# 🚀 نظام معرض قطر للسيارات - النسخة الكاملة

## 📋 نظرة عامة
نظام شامل لإدارة معرض السيارات مع دعم كامل للأرقام المميزة والمزادات ونظام الأقساط المتقدم.

## ✨ الميزات الرئيسية

### 🚗 إدارة السيارات
- إضافة وتعديل وحذف السيارات
- بحث متقدم وتصفية
- إدارة الصور والمواصفات
- تتبع حالة السيارة (متاح، مباع، محجوز)

### 🔢 إدارة الأرقام المميزة
- إضافة وإدارة الأرقام المميزة
- تصنيف الأرقام (VIP، مميز، عادي)
- تتبع حالة الأرقام
- ربط الأرقام بالعقود

### 🎯 نظام المزادات المتقدم
- إنشاء مزادات للأرقام المميزة
- مزادات مجدولة ومزادات فورية
- نظام مزايدة تفاعلي
- تمديد تلقائي للمزادات
- إشعارات المتابعين

### 📋 إدارة العقود
- عقود بيع السيارات
- عقود بيع الأرقام المميزة
- عقود مختلطة (سيارة + رقم مميز)
- طرق دفع متعددة (نقدي، تقسيط، إيجار، استبدال)

### 💳 نظام الأقساط الكامل
- إنشاء جداول أقساط تلقائية
- تتبع المدفوعات والمتبقي
- تقارير الأقساط المفصلة
- تذكيرات الأقساط المستحقة
- إدارة الأقساط المتأخرة
- تصدير البيانات

### 📊 التقارير والإحصائيات
- تقارير المبيعات
- تقارير الأقساط
- إحصائيات المزادات
- تقارير العملاء

## 🚀 كيفية التشغيل

### الطريقة الأولى: ملف التشغيل
```bash
START_COMPLETE_SYSTEM.bat
```

### الطريقة الثانية: Python مباشرة
```bash
python working_app.py
```

### الطريقة الثالثة: ملف app.py
```bash
python app.py
```

## 🔐 بيانات الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 🌐 الوصول للنظام
بعد التشغيل، افتح المتصفح على: http://127.0.0.1:9898

## 📱 الصفحات الرئيسية

### إدارة السيارات
- `/cars` - قائمة السيارات
- `/add_car` - إضافة سيارة جديدة

### إدارة الأرقام المميزة
- `/premium_numbers` - قائمة الأرقام المميزة
- `/add_premium_number` - إضافة رقم مميز

### نظام المزادات
- `/auctions` - قائمة المزادات
- `/create_auction` - إنشاء مزاد جديد
- `/auction/<id>` - تفاصيل المزاد

### إدارة العقود
- `/contracts` - قائمة العقود
- `/add_contract` - إنشاء عقد جديد

### نظام الأقساط
- `/installments` - إدارة الأقساط
- `/installments/due-today` - الأقساط المستحقة
- `/installments/reports` - تقارير الأقساط

## 🛠️ المتطلبات التقنية
- Python 3.7+
- Flask
- SQLAlchemy
- SQLite (قاعدة البيانات)

## 📁 هيكل الملفات
```
048/
├── working_app.py          # الملف الرئيسي للتطبيق
├── app.py                  # نقطة دخول بديلة
├── working_database.db     # قاعدة البيانات
├── START_COMPLETE_SYSTEM.bat  # ملف التشغيل
└── README_COMPLETE.md      # هذا الملف
```

## 🎯 الاستخدام السريع

1. **تشغيل النظام:** انقر على `START_COMPLETE_SYSTEM.bat`
2. **تسجيل الدخول:** استخدم admin/admin123
3. **إضافة سيارة:** اذهب إلى "السيارات" > "إضافة سيارة"
4. **إضافة رقم مميز:** اذهب إلى "الأرقام المميزة" > "إضافة رقم"
5. **إنشاء مزاد:** من صفحة الأرقام المميزة، اضغط "إنشاء مزاد"
6. **إنشاء عقد:** اذهب إلى "العقود" > "إضافة عقد"
7. **إدارة الأقساط:** اذهب إلى "الأقساط" لمتابعة المدفوعات

## 🔧 استكشاف الأخطاء

### إذا لم يعمل التطبيق:
1. تأكد من تثبيت Python
2. تأكد من تثبيت المكتبات المطلوبة
3. تحقق من أن المنفذ 9898 غير مستخدم

### إذا ظهرت أخطاء في قاعدة البيانات:
1. احذف ملف `working_database.db`
2. أعد تشغيل التطبيق

## 📞 الدعم
للمساعدة أو الاستفسارات، راجع الكود أو اتصل بالمطور.

---
**نظام معرض قطر للسيارات - النسخة الكاملة مع الأقساط** 🇶🇦
