@echo off
chcp 65001 >nul
title خادم الطوارئ - معرض قطر للسيارات

echo.
echo ========================================
echo 🚨 خادم الطوارئ - معرض قطر للسيارات
echo ========================================
echo.

echo 🛡️ تشغيل خادم الطوارئ الآمن...
echo 🌐 العنوان: http://127.0.0.1:8080
echo 💾 يعمل بدون قاعدة بيانات
echo ✅ محمي من جميع الأخطاء
echo.
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo ========================================
echo.

python EMERGENCY_WORKING_SERVER.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل خادم الطوارئ
    echo.
    echo 💡 الحلول البديلة المتاحة:
    echo.
    echo 1. المزادات المستقلة (تعمل بدون خادم):
    echo    - افتح الملف: auctions_index.html
    echo.
    echo 2. نظام الإدارة التفاعلي:
    echo    - افتح الملف: FINAL_NO_SERVER_SOLUTION.html
    echo.
    echo 3. الحل الكامل العملي:
    echo    - افتح الملف: COMPLETE_WORKING_SOLUTION.html
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
)
