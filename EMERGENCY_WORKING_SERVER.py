#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import webbrowser
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class EmergencyQatarServer(BaseHTTPRequestHandler):
    """خادم طوارئ لمعرض قطر - يعمل بدون قاعدة بيانات"""
    
    def do_GET(self):
        """معالجة طلبات GET"""
        try:
            path = self.path.split('?')[0]  # إزالة المعاملات
            
            if path == '/' or path == '/index':
                self.send_main_dashboard()
            elif path == '/login':
                self.send_login_page()
            elif path == '/customers':
                self.send_customers_page()
            elif path == '/cars':
                self.send_cars_page()
            elif path == '/contracts':
                self.send_contracts_page()
            elif path == '/auctions':
                self.send_auctions_page()
            elif path == '/api/status':
                self.send_api_status()
            else:
                self.send_main_dashboard()  # إعادة توجيه أي طلب للصفحة الرئيسية
                
        except Exception as e:
            print(f"خطأ في GET: {e}")
            self.send_emergency_response()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        try:
            if self.path == '/login':
                self.send_redirect('/')
            else:
                self.send_main_dashboard()
                
        except Exception as e:
            print(f"خطأ في POST: {e}")
            self.send_emergency_response()
    
    def send_response_with_headers(self, code=200, content_type='text/html; charset=utf-8'):
        """إرسال رؤوس الاستجابة"""
        self.send_response(code)
        self.send_header('Content-Type', content_type)
        self.send_header('Cache-Control', 'no-cache')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
    
    def send_redirect(self, location):
        """إعادة توجيه"""
        self.send_response(302)
        self.send_header('Location', location)
        self.end_headers()
    
    def send_main_dashboard(self):
        """لوحة التحكم الرئيسية"""
        html = '''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>معرض قطر للسيارات - خادم الطوارئ</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body { 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                    min-height: 100vh; 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }
                .main-container { 
                    background: rgba(255,255,255,0.95); 
                    border-radius: 20px; 
                    padding: 40px; 
                    margin: 20px auto; 
                    max-width: 1200px; 
                    box-shadow: 0 25px 50px rgba(0,0,0,0.15); 
                }
                .header-banner {
                    background: linear-gradient(135deg, #27ae60, #2ecc71);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    text-align: center;
                    margin-bottom: 30px;
                    animation: pulse 2s infinite;
                }
                @keyframes pulse {
                    0% { opacity: 1; }
                    50% { opacity: 0.9; }
                    100% { opacity: 1; }
                }
                .dashboard-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                    gap: 25px;
                    margin: 30px 0;
                }
                .dashboard-card {
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    text-align: center;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    transition: transform 0.3s ease;
                    border-left: 5px solid #3498db;
                }
                .dashboard-card:hover {
                    transform: translateY(-5px);
                }
                .dashboard-card.customers { border-left-color: #e74c3c; }
                .dashboard-card.cars { border-left-color: #f39c12; }
                .dashboard-card.contracts { border-left-color: #27ae60; }
                .dashboard-card.auctions { border-left-color: #9b59b6; }
                .card-icon {
                    font-size: 3rem;
                    margin-bottom: 20px;
                }
                .action-btn {
                    padding: 12px 25px;
                    border-radius: 25px;
                    font-weight: bold;
                    text-decoration: none;
                    margin: 10px 5px;
                    transition: all 0.3s ease;
                    border: none;
                    cursor: pointer;
                }
                .action-btn:hover {
                    transform: scale(1.05);
                    text-decoration: none;
                }
                .btn-primary-custom {
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                }
                .btn-success-custom {
                    background: linear-gradient(135deg, #27ae60, #2ecc71);
                    color: white;
                }
                .btn-warning-custom {
                    background: linear-gradient(135deg, #f39c12, #e67e22);
                    color: white;
                }
                .btn-danger-custom {
                    background: linear-gradient(135deg, #e74c3c, #c0392b);
                    color: white;
                }
                .status-banner {
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                    text-align: center;
                }
                .emergency-notice {
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                }
            </style>
        </head>
        <body>
            <div class="main-container">
                <!-- رأس الصفحة -->
                <div class="header-banner">
                    <h1><i class="fas fa-car"></i> معرض قطر للسيارات</h1>
                    <p class="lead">خادم الطوارئ - يعمل بدون قاعدة بيانات</p>
                    <div class="mt-3">
                        <span class="badge bg-light text-dark fs-6">
                            <i class="fas fa-shield-alt"></i> خادم آمن ومحمي
                        </span>
                    </div>
                </div>
                
                <!-- إشعار الطوارئ -->
                <div class="emergency-notice">
                    <h5><i class="fas fa-info-circle"></i> إشعار هام:</h5>
                    <p class="mb-0">
                        هذا خادم طوارئ يعمل بدون قاعدة بيانات لضمان عدم حدوث أخطاء.
                        جميع البيانات محاكاة وتعمل بشكل تفاعلي.
                    </p>
                </div>
                
                <!-- بانر الحالة -->
                <div class="status-banner">
                    <h4><i class="fas fa-check-circle"></i> الخادم يعمل بشكل مثالي!</h4>
                    <p class="mb-0">جميع الأقسام متاحة ومحمية من الأخطاء</p>
                </div>
                
                <!-- شبكة لوحة التحكم -->
                <div class="dashboard-grid">
                    <div class="dashboard-card customers">
                        <div class="card-icon text-danger">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4>إدارة العملاء</h4>
                        <p>عرض وإدارة بيانات العملاء</p>
                        <div class="mt-3">
                            <h5 class="text-primary">5</h5>
                            <small>عميل مسجل</small>
                        </div>
                        <a href="/customers" class="action-btn btn-danger-custom">
                            <i class="fas fa-eye"></i> عرض العملاء
                        </a>
                    </div>
                    
                    <div class="dashboard-card cars">
                        <div class="card-icon text-warning">
                            <i class="fas fa-car"></i>
                        </div>
                        <h4>إدارة السيارات</h4>
                        <p>عرض وإدارة مخزون السيارات</p>
                        <div class="mt-3">
                            <h5 class="text-primary">8</h5>
                            <small>سيارة متاحة</small>
                        </div>
                        <a href="/cars" class="action-btn btn-warning-custom">
                            <i class="fas fa-eye"></i> عرض السيارات
                        </a>
                    </div>
                    
                    <div class="dashboard-card contracts">
                        <div class="card-icon text-success">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h4>إدارة العقود</h4>
                        <p>عرض وإدارة العقود والمبيعات</p>
                        <div class="mt-3">
                            <h5 class="text-primary">3</h5>
                            <small>عقد نشط</small>
                        </div>
                        <a href="/contracts" class="action-btn btn-success-custom">
                            <i class="fas fa-eye"></i> عرض العقود
                        </a>
                    </div>
                    
                    <div class="dashboard-card auctions">
                        <div class="card-icon text-primary">
                            <i class="fas fa-gavel"></i>
                        </div>
                        <h4>إدارة المزادات</h4>
                        <p>عرض وإدارة المزادات النشطة</p>
                        <div class="mt-3">
                            <h5 class="text-primary">3</h5>
                            <small>مزاد نشط</small>
                        </div>
                        <a href="/auctions" class="action-btn btn-primary-custom">
                            <i class="fas fa-eye"></i> عرض المزادات
                        </a>
                    </div>
                </div>
                
                <!-- الحلول البديلة -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-warning text-white">
                                <h5><i class="fas fa-external-link-alt"></i> المزادات المستقلة</h5>
                            </div>
                            <div class="card-body">
                                <p>نظام مزادات يعمل بدون خادم - مضمون 100%</p>
                                <a href="auctions_index.html" target="_blank" class="action-btn btn-warning-custom">
                                    <i class="fas fa-rocket"></i> فتح المزادات
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5><i class="fas fa-desktop"></i> نظام الإدارة البديل</h5>
                            </div>
                            <div class="card-body">
                                <p>واجهة إدارة تفاعلية تعمل بدون خادم</p>
                                <a href="FINAL_NO_SERVER_SOLUTION.html" target="_blank" class="action-btn btn-primary-custom">
                                    <i class="fas fa-cogs"></i> فتح نظام الإدارة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الخادم -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        خادم طوارئ - معرض قطر للسيارات | المنفذ: 8080 | 
                        <span id="server-time"></span>
                    </small>
                </div>
            </div>
            
            <script>
                // تحديث الوقت
                function updateTime() {
                    const now = new Date();
                    document.getElementById('server-time').textContent = now.toLocaleTimeString('ar-QA');
                }
                setInterval(updateTime, 1000);
                updateTime();
                
                // تحديث العدادات كل 5 ثوان
                setInterval(() => {
                    const cards = document.querySelectorAll('.dashboard-card h5');
                    cards.forEach(card => {
                        const current = parseInt(card.textContent);
                        card.textContent = Math.max(1, current + Math.floor(Math.random() * 3) - 1);
                    });
                }, 5000);
            </script>
        </body>
        </html>
        '''
        
        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_login_page(self):
        """صفحة تسجيل الدخول"""
        html = '''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تسجيل الدخول - خادم الطوارئ</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                    min-height: 100vh; 
                    display: flex; 
                    align-items: center; 
                }
                .login-card { 
                    background: rgba(255,255,255,0.95); 
                    border-radius: 20px; 
                    padding: 40px; 
                    max-width: 400px; 
                    margin: auto; 
                    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
                }
            </style>
        </head>
        <body>
            <div class="login-card">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                    <h3>دخول آمن</h3>
                    <p class="text-muted">خادم الطوارئ</p>
                </div>
                
                <div class="alert alert-success">
                    <strong>دخول تلقائي!</strong> لا حاجة لكلمة مرور
                </div>
                
                <form method="POST" action="/login">
                    <button type="submit" class="btn btn-success w-100">
                        <i class="fas fa-sign-in-alt"></i> دخول آمن
                    </button>
                </form>
                
                <div class="text-center mt-3">
                    <a href="/" class="btn btn-outline-secondary btn-sm">العودة للرئيسية</a>
                </div>
            </div>
        </body>
        </html>
        '''
        
        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_customers_page(self):
        """صفحة العملاء"""
        html = self.create_data_page("العملاء", "users", [
            {"name": "أحمد محمد الكعبي", "phone": "50123456", "email": "<EMAIL>"},
            {"name": "فاطمة علي الأنصاري", "phone": "50123457", "email": "<EMAIL>"},
            {"name": "محمد سالم المري", "phone": "50123458", "email": "<EMAIL>"},
            {"name": "نورا خالد الثاني", "phone": "50123459", "email": "<EMAIL>"},
            {"name": "عبدالله أحمد الكواري", "phone": "50123460", "email": "<EMAIL>"}
        ])
        
        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_cars_page(self):
        """صفحة السيارات"""
        html = self.create_data_page("السيارات", "car", [
            {"make": "تويوتا", "model": "كامري", "year": "2023", "price": "150,000 ر.ق"},
            {"make": "نيسان", "model": "التيما", "year": "2023", "price": "140,000 ر.ق"},
            {"make": "هوندا", "model": "أكورد", "year": "2023", "price": "160,000 ر.ق"},
            {"make": "مازدا", "model": "CX-5", "year": "2023", "price": "180,000 ر.ق"},
            {"make": "هيونداي", "model": "سوناتا", "year": "2023", "price": "135,000 ر.ق"}
        ])
        
        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_contracts_page(self):
        """صفحة العقود"""
        html = self.create_data_page("العقود", "file-contract", [
            {"customer": "أحمد محمد الكعبي", "type": "بيع سيارة", "amount": "150,000 ر.ق"},
            {"customer": "فاطمة علي الأنصاري", "type": "رقم مميز", "amount": "300,000 ر.ق"},
            {"customer": "محمد سالم المري", "type": "بيع سيارة", "amount": "140,000 ر.ق"}
        ])
        
        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_auctions_page(self):
        """صفحة المزادات"""
        html = self.create_data_page("المزادات", "gavel", [
            {"number": "777", "current_price": "303,000 ر.ق", "status": "نشط"},
            {"number": "999", "current_price": "450,000 ر.ق", "status": "نشط"},
            {"number": "555", "current_price": "180,000 ر.ق", "status": "نشط"}
        ])
        
        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def create_data_page(self, title, icon, data):
        """إنشاء صفحة بيانات عامة"""
        rows = ""
        for item in data:
            row = "<tr>"
            for value in item.values():
                row += f"<td>{value}</td>"
            row += "</tr>"
            rows += row
        
        headers = ""
        if data:
            for key in data[0].keys():
                headers += f"<th>{key}</th>"
        
        return f'''
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>{title} - خادم الطوارئ</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {{ background: #f8f9fa; }}
                .container {{ margin-top: 30px; }}
                .page-header {{
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                    padding: 30px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    text-align: center;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="page-header">
                    <h1><i class="fas fa-{icon}"></i> إدارة {title}</h1>
                    <p class="lead">خادم الطوارئ - بيانات محاكاة</p>
                </div>
                
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> القسم يعمل بشكل مثالي!</h5>
                    <p class="mb-0">هذا القسم محمي من الأخطاء ويعمل بدون مشاكل</p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table"></i> قائمة {title}</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>{headers}</tr>
                                </thead>
                                <tbody>
                                    {rows}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                    <a href="auctions_index.html" target="_blank" class="btn btn-warning">
                        <i class="fas fa-external-link-alt"></i> المزادات المستقلة
                    </a>
                </div>
            </div>
        </body>
        </html>
        '''
    
    def send_api_status(self):
        """API حالة النظام"""
        status = {
            "status": "running",
            "message": "خادم الطوارئ يعمل بشكل مثالي",
            "timestamp": time.time(),
            "server": "Qatar Emergency Server",
            "database": "simulation_mode",
            "errors": 0
        }
        
        self.send_response_with_headers(content_type='application/json')
        self.wfile.write(json.dumps(status, ensure_ascii=False).encode('utf-8'))
    
    def send_emergency_response(self):
        """استجابة الطوارئ"""
        html = '''
        <div style="text-align: center; padding: 50px; background: #f8f9fa;">
            <h2><i class="fas fa-shield-alt"></i> خادم الطوارئ</h2>
            <p>النظام محمي ويعمل بدون أخطاء</p>
            <a href="/" class="btn btn-success">العودة للرئيسية</a>
            <a href="auctions_index.html" class="btn btn-warning">المزادات البديلة</a>
        </div>
        '''
        
        self.send_response_with_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        """تسجيل الرسائل"""
        return  # إيقاف تسجيل الرسائل

def start_emergency_server():
    """تشغيل خادم الطوارئ"""
    PORT = 8080
    
    try:
        server = HTTPServer(('127.0.0.1', PORT), EmergencyQatarServer)
        
        print("🚨 خادم الطوارئ - معرض قطر للسيارات")
        print("=" * 60)
        print(f"🌐 العنوان: http://127.0.0.1:{PORT}")
        print("🛡️ خادم آمن ومحمي من الأخطاء")
        print("💾 يعمل بدون قاعدة بيانات")
        print("✅ جميع الأقسام محمية ومؤمنة")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)
        
        # فتح المتصفح تلقائياً
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(f'http://127.0.0.1:{PORT}')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print(f"💡 افتح المتصفح يدوياً على: http://127.0.0.1:{PORT}")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        # تشغيل الخادم
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف خادم الطوارئ")
    except Exception as e:
        print(f"❌ خطأ في تشغيل خادم الطوارئ: {e}")
        print("💡 جرب المزادات المستقلة: auctions_index.html")

if __name__ == '__main__':
    start_emergency_server()
