#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime

def fix_customer_table():
    """إصلاح جدول العملاء بإضافة الأعمدة المفقودة"""
    
    db_files = ['auction.db', 'working_database.db', 'instance/auction.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"🔧 إصلاح {db_file}...")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # التحقق من وجود جدول العملاء
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='customer'")
                if not cursor.fetchone():
                    print(f"⚠️ جدول customer غير موجود في {db_file}")
                    continue
                
                # الحصول على أعمدة الجدول الحالية
                cursor.execute("PRAGMA table_info(customer)")
                existing_columns = [column[1] for column in cursor.fetchall()]
                print(f"📋 الأعمدة الموجودة: {existing_columns}")
                
                # الأعمدة المطلوبة
                required_columns = [
                    ('name_en', 'TEXT'),
                    ('id_number', 'TEXT'),
                    ('nationality', 'TEXT DEFAULT "قطري"'),
                    ('birth_date', 'DATE'),
                    ('address', 'TEXT'),
                    ('city', 'TEXT DEFAULT "الدوحة"'),
                    ('postal_code', 'TEXT'),
                    ('profession', 'TEXT'),
                    ('company', 'TEXT'),
                    ('monthly_income', 'REAL DEFAULT 0'),
                    ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
                    ('notes', 'TEXT')
                ]
                
                # إضافة الأعمدة المفقودة
                for column_name, column_type in required_columns:
                    if column_name not in existing_columns:
                        try:
                            cursor.execute(f"ALTER TABLE customer ADD COLUMN {column_name} {column_type}")
                            print(f"✅ تم إضافة العمود: {column_name}")
                        except sqlite3.OperationalError as e:
                            print(f"❌ خطأ في إضافة {column_name}: {e}")
                    else:
                        print(f"⏭️ العمود {column_name} موجود بالفعل")
                
                # تحديث البيانات الموجودة
                print("🔄 تحديث البيانات الموجودة...")
                
                # تحديث name_en للصفوف التي لا تحتوي على قيمة
                cursor.execute("UPDATE customer SET name_en = 'Customer Name' WHERE name_en IS NULL OR name_en = ''")
                
                # تحديث الحقول الأخرى
                cursor.execute("UPDATE customer SET nationality = 'قطري' WHERE nationality IS NULL OR nationality = ''")
                cursor.execute("UPDATE customer SET city = 'الدوحة' WHERE city IS NULL OR city = ''")
                cursor.execute("UPDATE customer SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL")
                cursor.execute("UPDATE customer SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL")
                
                # التحقق من النتيجة
                cursor.execute("PRAGMA table_info(customer)")
                final_columns = [column[1] for column in cursor.fetchall()]
                print(f"✅ الأعمدة النهائية: {final_columns}")
                
                conn.commit()
                conn.close()
                print(f"✅ تم إصلاح {db_file} بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في {db_file}: {e}")
                
        else:
            print(f"⚠️ الملف {db_file} غير موجود")

def test_insert():
    """اختبار إدراج بيانات جديدة"""
    
    db_files = ['auction.db', 'working_database.db', 'instance/auction.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"\n🧪 اختبار الإدراج في {db_file}...")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # محاولة إدراج عميل تجريبي
                test_data = (
                    'عميل تجريبي',  # name_ar
                    'Test Customer',  # name_en
                    '50123456',  # phone
                    '<EMAIL>',  # email
                    '12345678901',  # id_number
                    'قطري',  # nationality
                    '1990-01-01',  # birth_date
                    'الدوحة',  # address
                    'الدوحة',  # city
                    '12345',  # postal_code
                    'مهندس',  # profession
                    'شركة تجريبية',  # company
                    5000.0,  # monthly_income
                    datetime.now().isoformat(),  # created_at
                    datetime.now().isoformat(),  # updated_at
                    'عميل تجريبي للاختبار'  # notes
                )
                
                cursor.execute('''
                    INSERT INTO customer (
                        name_ar, name_en, phone, email, id_number, nationality, 
                        birth_date, address, city, postal_code, profession, 
                        company, monthly_income, created_at, updated_at, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', test_data)
                
                conn.commit()
                print(f"✅ تم إدراج البيانات التجريبية بنجاح في {db_file}")
                
                # التحقق من البيانات
                cursor.execute("SELECT COUNT(*) FROM customer")
                count = cursor.fetchone()[0]
                print(f"📊 إجمالي العملاء: {count}")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ فشل الاختبار في {db_file}: {e}")

if __name__ == '__main__':
    print("🚨 إصلاح عاجل لجدول العملاء")
    print("=" * 50)
    
    # إصلاح الجداول
    fix_customer_table()
    
    print("\n" + "=" * 50)
    print("🧪 اختبار الإصلاح")
    
    # اختبار الإدراج
    test_insert()
    
    print("\n" + "=" * 50)
    print("✅ تم الانتهاء من الإصلاح العاجل")
    print("💡 يمكنك الآن تشغيل النظام بدون أخطاء")
