{% extends "base.html" %}

{% block title %}إعدادات الشركة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-building me-2"></i>إعدادات الشركة</h2>
                <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة للإعدادات
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-edit me-2"></i>تعديل معلومات الشركة</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <!-- Company Name -->
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">
                                            <i class="fas fa-building me-1"></i>اسم الشركة *
                                        </label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="{{ settings.get('company_name', '') }}" required>
                                    </div>

                                    <!-- Showroom Name -->
                                    <div class="col-md-6 mb-3">
                                        <label for="showroom_name" class="form-label">
                                            <i class="fas fa-car me-1"></i>اسم المعرض *
                                        </label>
                                        <input type="text" class="form-control" id="showroom_name" name="showroom_name" 
                                               value="{{ settings.get('showroom_name', '') }}" required>
                                    </div>

                                    <!-- Phone -->
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="fas fa-phone me-1"></i>رقم الهاتف
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="{{ settings.get('company_phone', '') }}" 
                                               placeholder="+974 1234 5678">
                                    </div>

                                    <!-- Email -->
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope me-1"></i>البريد الإلكتروني
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="{{ settings.get('company_email', '') }}" 
                                               placeholder="<EMAIL>">
                                    </div>

                                    <!-- Website -->
                                    <div class="col-md-12 mb-3">
                                        <label for="website" class="form-label">
                                            <i class="fas fa-globe me-1"></i>الموقع الإلكتروني
                                        </label>
                                        <input type="url" class="form-control" id="website" name="website" 
                                               value="{{ settings.get('company_website', '') }}" 
                                               placeholder="https://www.company.com">
                                    </div>

                                    <!-- Address Arabic -->
                                    <div class="col-md-6 mb-3">
                                        <label for="address_ar" class="form-label">
                                            <i class="fas fa-map-marker-alt me-1"></i>العنوان (عربي)
                                        </label>
                                        <textarea class="form-control" id="address_ar" name="address_ar" rows="3" 
                                                  placeholder="العنوان بالعربية">{{ settings.get('company_address_ar', '') }}</textarea>
                                    </div>

                                    <!-- Address English -->
                                    <div class="col-md-6 mb-3">
                                        <label for="address_en" class="form-label">
                                            <i class="fas fa-map-marker-alt me-1"></i>العنوان (إنجليزي)
                                        </label>
                                        <textarea class="form-control" id="address_en" name="address_en" rows="3" 
                                                  placeholder="Address in English">{{ settings.get('company_address_en', '') }}</textarea>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>حفظ التغييرات
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Preview Card -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة</h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-building fa-3x text-primary mb-2"></i>
                                <h5 id="preview_company_name">{{ settings.get('company_name', 'اسم الشركة') }}</h5>
                                <p class="text-muted mb-1" id="preview_showroom_name">{{ settings.get('showroom_name', 'اسم المعرض') }}</p>
                            </div>
                            
                            <hr>
                            
                            <div class="small">
                                <div class="mb-2" id="preview_phone">
                                    <i class="fas fa-phone text-muted me-2"></i>
                                    {{ settings.get('company_phone', 'رقم الهاتف') }}
                                </div>
                                <div class="mb-2" id="preview_email">
                                    <i class="fas fa-envelope text-muted me-2"></i>
                                    {{ settings.get('company_email', 'البريد الإلكتروني') }}
                                </div>
                                <div class="mb-2" id="preview_website">
                                    <i class="fas fa-globe text-muted me-2"></i>
                                    {{ settings.get('company_website', 'الموقع الإلكتروني') }}
                                </div>
                                <div id="preview_address">
                                    <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                    {{ settings.get('company_address_ar', 'العنوان') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tips Card -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>نصائح</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    استخدم اسماً واضحاً ومميزاً للشركة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    تأكد من صحة رقم الهاتف والبريد الإلكتروني
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    أضف العنوان بالعربية والإنجليزية
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    هذه المعلومات ستظهر في التقارير والفواتير
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const fields = [
        { input: 'company_name', preview: 'preview_company_name' },
        { input: 'showroom_name', preview: 'preview_showroom_name' },
        { input: 'phone', preview: 'preview_phone', icon: 'fas fa-phone' },
        { input: 'email', preview: 'preview_email', icon: 'fas fa-envelope' },
        { input: 'website', preview: 'preview_website', icon: 'fas fa-globe' },
        { input: 'address_ar', preview: 'preview_address', icon: 'fas fa-map-marker-alt' }
    ];

    fields.forEach(field => {
        const input = document.getElementById(field.input);
        const preview = document.getElementById(field.preview);
        
        if (input && preview) {
            input.addEventListener('input', function() {
                let value = this.value || (field.input === 'company_name' ? 'اسم الشركة' : 
                                          field.input === 'showroom_name' ? 'اسم المعرض' : 
                                          'غير محدد');
                
                if (field.icon) {
                    preview.innerHTML = `<i class="${field.icon} text-muted me-2"></i>${value}`;
                } else {
                    preview.textContent = value;
                }
            });
        }
    });
});
</script>
{% endblock %}
