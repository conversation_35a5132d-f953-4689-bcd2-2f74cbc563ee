#!/usr/bin/env python3
"""
أداة إنشاء المزادات السريعة
لإنشاء مزادات بأوقات وأحجام مختلفة بسرعة
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Auction, PremiumNumber
from datetime import datetime, timedelta

def create_quick_auction(number_id, duration_minutes=5, start_delay_seconds=30):
    """إنشاء مزاد سريع"""
    with app.app_context():
        try:
            # البحث عن الرقم
            number = PremiumNumber.query.get(number_id)
            if not number:
                print(f"❌ الرقم {number_id} غير موجود")
                return False

            # التأكد من وجود البيانات المطلوبة
            if not hasattr(number, 'number') or not hasattr(number, 'price') or number.number is None or number.price is None:
                print(f"❌ بيانات الرقم المميز غير مكتملة")
                return False

            if number.status != 'available':
                print(f"❌ الرقم {number.number} غير متاح للمزاد")
                return False

            # حساب الأوقات
            start_time = datetime.now() + timedelta(seconds=start_delay_seconds)
            end_time = start_time + timedelta(minutes=duration_minutes)

            # إنشاء المزاد
            auction = Auction(
                premium_number_id=number_id,
                title=f"مزاد سريع للرقم {number.number}",
                description=f"مزاد سريع لمدة {duration_minutes} دقيقة للرقم المميز {number.number}",
                starting_price=number.price,
                bid_increment=500,  # زيادة صغيرة للمزادات السريعة
                start_time=start_time,
                end_time=end_time,
                commission_rate=5.0,
                auto_extend=True,
                created_by=1  # افتراضي admin
            )

            db.session.add(auction)
            
            # تحديث حالة الرقم
            number.status = 'auction'
            
            db.session.commit()

            print(f"✅ تم إنشاء مزاد سريع للرقم {number.number}")
            print(f"   🕐 يبدأ في: {start_time.strftime('%H:%M:%S')}")
            print(f"   ⏰ ينتهي في: {end_time.strftime('%H:%M:%S')}")
            print(f"   🌐 رابط المزاد: http://127.0.0.1:9898/auction/details/{auction.id}")
            
            return auction.id

        except Exception as e:
            print(f"❌ خطأ في إنشاء المزاد: {e}")
            db.session.rollback()
            return False

def create_test_auctions():
    """إنشاء مزادات تجريبية بأوقات مختلفة"""
    with app.app_context():
        # البحث عن أرقام متاحة
        available_numbers = PremiumNumber.query.filter_by(status='available').limit(5).all()
        
        if not available_numbers:
            print("❌ لا توجد أرقام متاحة للمزاد")
            return

        print("🚀 إنشاء مزادات تجريبية...")
        
        # مزادات بأوقات مختلفة
        durations = [0.5, 1, 2, 5, 10]  # بالدقائق
        
        for i, number in enumerate(available_numbers[:len(durations)]):
            duration = durations[i]
            start_delay = 30 + (i * 10)  # تأخير متدرج
            
            auction_id = create_quick_auction(
                number.id, 
                duration_minutes=duration,
                start_delay_seconds=start_delay
            )
            
            if auction_id:
                print(f"   📋 مزاد #{auction_id} - الرقم {number.number} - مدة {duration} دقيقة")

def show_menu():
    """عرض قائمة الخيارات"""
    print("\n" + "="*60)
    print("🎯 أداة إنشاء المزادات السريعة")
    print("="*60)
    print("1. إنشاء مزاد سريع (30 ثانية)")
    print("2. إنشاء مزاد قصير (1 دقيقة)")
    print("3. إنشاء مزاد متوسط (5 دقائق)")
    print("4. إنشاء مزاد طويل (30 دقيقة)")
    print("5. إنشاء مزادات تجريبية متعددة")
    print("6. عرض الأرقام المتاحة")
    print("0. خروج")
    print("="*60)

def show_available_numbers():
    """عرض الأرقام المتاحة"""
    with app.app_context():
        numbers = PremiumNumber.query.filter_by(status='available').all()
        
        if not numbers:
            print("❌ لا توجد أرقام متاحة حالياً")
            return
            
        print("\n📋 الأرقام المتاحة للمزاد:")
        print("-" * 50)
        for number in numbers:
            print(f"ID: {number.id:3d} | الرقم: {number.number:>8s} | الفئة: {number.category:>10s} | السعر: {number.price:>8,.0f} ر.ق")

def main():
    """الدالة الرئيسية"""
    print("🎯 مرحباً بك في أداة إنشاء المزادات السريعة")
    
    while True:
        show_menu()
        
        try:
            choice = input("\nاختر رقم الخيار: ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام الأداة!")
                break
                
            elif choice == '1':
                # مزاد سريع 30 ثانية
                show_available_numbers()
                number_id = input("\nأدخل ID الرقم: ").strip()
                if number_id.isdigit():
                    create_quick_auction(int(number_id), duration_minutes=0.5, start_delay_seconds=10)
                    
            elif choice == '2':
                # مزاد قصير 1 دقيقة
                show_available_numbers()
                number_id = input("\nأدخل ID الرقم: ").strip()
                if number_id.isdigit():
                    create_quick_auction(int(number_id), duration_minutes=1, start_delay_seconds=15)
                    
            elif choice == '3':
                # مزاد متوسط 5 دقائق
                show_available_numbers()
                number_id = input("\nأدخل ID الرقم: ").strip()
                if number_id.isdigit():
                    create_quick_auction(int(number_id), duration_minutes=5, start_delay_seconds=30)
                    
            elif choice == '4':
                # مزاد طويل 30 دقيقة
                show_available_numbers()
                number_id = input("\nأدخل ID الرقم: ").strip()
                if number_id.isdigit():
                    create_quick_auction(int(number_id), duration_minutes=30, start_delay_seconds=60)
                    
            elif choice == '5':
                # مزادات تجريبية متعددة
                create_test_auctions()
                
            elif choice == '6':
                # عرض الأرقام المتاحة
                show_available_numbers()
                
            else:
                print("❌ خيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()
