#!/usr/bin/env python3
"""
Complete Database Fix - Qatar Car Showroom
This script completely recreates the database with correct schema
"""

import sqlite3
import os
from datetime import datetime
from werkzeug.security import generate_password_hash

def backup_database():
    """Create backup of existing database"""
    db_path = 'working_database.db'
    if os.path.exists(db_path):
        backup_path = f'working_database_backup_complete_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"💾 Database backed up to: {backup_path}")
        return backup_path
    return None

def recreate_database():
    """Recreate database with correct schema"""
    db_path = 'working_database.db'
    
    # Remove old database
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️ Old database removed")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Creating new database schema...")
        
        # Create user table
        cursor.execute('''
            CREATE TABLE user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                password_hash VARCHAR(120) NOT NULL,
                role VARCHAR(20) DEFAULT 'admin',
                is_active BOOLEAN DEFAULT 1,
                email VARCHAR(120) UNIQUE,
                full_name VARCHAR(100),
                phone VARCHAR(20),
                avatar VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME
            )
        ''')
        print("✅ User table created")
        
        # Create car table
        cursor.execute('''
            CREATE TABLE car (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                make VARCHAR(50) NOT NULL,
                model VARCHAR(50) NOT NULL,
                year INTEGER NOT NULL,
                price FLOAT NOT NULL,
                status VARCHAR(20) DEFAULT 'available',
                color VARCHAR(30),
                fuel_type VARCHAR(20),
                transmission VARCHAR(20),
                engine_size VARCHAR(20),
                mileage INTEGER,
                body_type VARCHAR(30),
                doors INTEGER,
                seats INTEGER,
                vin_number VARCHAR(50),
                license_plate VARCHAR(20),
                insurance_expiry DATE,
                registration_expiry DATE,
                condition VARCHAR(20),
                features TEXT,
                notes TEXT,
                purchase_date DATE,
                purchase_price FLOAT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ Car table created")
        
        # Create premium_number table
        cursor.execute('''
            CREATE TABLE premium_number (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                number VARCHAR(20) NOT NULL,
                category VARCHAR(20) NOT NULL,
                price FLOAT NOT NULL,
                status VARCHAR(20) DEFAULT 'available'
            )
        ''')
        print("✅ Premium number table created")
        
        # Create customer table
        cursor.execute('''
            CREATE TABLE customer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name_ar VARCHAR(100) NOT NULL,
                name_en VARCHAR(100),
                phone VARCHAR(20) NOT NULL,
                email VARCHAR(100),
                id_number VARCHAR(20),
                nationality VARCHAR(50),
                birth_date DATE,
                address TEXT,
                city VARCHAR(50),
                postal_code VARCHAR(10),
                profession VARCHAR(100),
                company VARCHAR(100),
                monthly_income FLOAT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            )
        ''')
        print("✅ Customer table created")
        
        # Create contract table
        cursor.execute('''
            CREATE TABLE contract (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_number VARCHAR(50) UNIQUE NOT NULL,
                contract_type VARCHAR(30) NOT NULL,
                payment_type VARCHAR(30),
                customer_id INTEGER NOT NULL,
                car_id INTEGER,
                premium_number_id INTEGER,
                total_amount FLOAT NOT NULL,
                down_payment FLOAT DEFAULT 0,
                remaining_amount FLOAT DEFAULT 0,
                monthly_payment FLOAT DEFAULT 0,
                installment_months INTEGER DEFAULT 0,
                interest_rate FLOAT DEFAULT 0,
                contract_date DATE NOT NULL,
                delivery_date DATE,
                warranty_months INTEGER DEFAULT 0,
                insurance_required BOOLEAN DEFAULT 0,
                registration_included BOOLEAN DEFAULT 0,
                special_conditions TEXT,
                notes TEXT,
                discount_amount FLOAT DEFAULT 0,
                tax_amount FLOAT DEFAULT 0,
                commission_amount FLOAT DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft',
                signed_by_customer BOOLEAN DEFAULT 0,
                signed_by_dealer BOOLEAN DEFAULT 0,
                witness_name VARCHAR(100),
                witness_id VARCHAR(20),
                contract_file_path VARCHAR(255),
                pdf_generated BOOLEAN DEFAULT 0,
                word_generated BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (customer_id) REFERENCES customer (id),
                FOREIGN KEY (car_id) REFERENCES car (id),
                FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')
        print("✅ Contract table created")
        
        # Create installment_payment table
        cursor.execute('''
            CREATE TABLE installment_payment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                installment_number INTEGER NOT NULL,
                due_date DATE NOT NULL,
                amount FLOAT NOT NULL,
                paid_amount FLOAT DEFAULT 0,
                payment_date DATE,
                status VARCHAR(20) DEFAULT 'pending',
                late_fee FLOAT DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (contract_id) REFERENCES contract (id)
            )
        ''')
        print("✅ Installment payment table created")
        
        # Create contract_document table
        cursor.execute('''
            CREATE TABLE contract_document (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                document_type VARCHAR(30) NOT NULL,
                document_name VARCHAR(100) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_size INTEGER,
                mime_type VARCHAR(50),
                uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                uploaded_by INTEGER,
                FOREIGN KEY (contract_id) REFERENCES contract (id),
                FOREIGN KEY (uploaded_by) REFERENCES user (id)
            )
        ''')
        print("✅ Contract document table created")
        
        # Create admin user
        password_hash = generate_password_hash('admin123')
        cursor.execute('''
            INSERT INTO user (username, password_hash, role, is_active, email, full_name)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('admin', password_hash, 'admin', 1, '<EMAIL>', 'مدير النظام'))
        print("✅ Admin user created")
        
        conn.commit()
        conn.close()
        
        print("🎉 Database recreated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Qatar Car Showroom - Complete Database Fix")
    print("=" * 60)
    
    # Create backup
    backup_path = backup_database()
    
    # Recreate database
    if recreate_database():
        print("\n✅ Database is now completely fixed!")
        print("🔐 Admin credentials:")
        print("   Username: admin")
        print("   Password: admin123")
        print("\n🌐 Restart the application and try again!")
        if backup_path:
            print(f"💾 Backup saved at: {backup_path}")
    else:
        print("\n❌ Failed to fix database")
        if backup_path:
            print(f"💾 Backup available at: {backup_path}")
