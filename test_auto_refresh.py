#!/usr/bin/env python3
"""
اختبار التحديث التلقائي للعرض المستقل
"""

import os
import sys
from datetime import datetime, timedelta

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_auction():
    """إنشاء مزاد اختبار للتحديث التلقائي"""
    
    try:
        from working_app import app, db, Customer, PremiumNumber, Auction, Bid
        
        with app.app_context():
            print("🧪 إنشاء مزاد اختبار للتحديث التلقائي...")
            
            # إنشاء عميل اختبار
            customer = Customer.query.filter_by(name_ar='عميل اختبار').first()
            if not customer:
                customer = Customer(
                    name_ar='عميل اختبار',
                    phone='+974 1234 5678',
                    email='<EMAIL>'
                )
                db.session.add(customer)
                db.session.flush()
            
            # إنشاء رقم مميز للاختبار
            test_number = PremiumNumber.query.filter_by(number='12345').first()
            if not test_number:
                test_number = PremiumNumber(
                    number='12345',
                    category='اختبار',
                    price=10000,
                    status='auction'
                )
                db.session.add(test_number)
                db.session.flush()
            else:
                test_number.status = 'auction'
            
            # حذف المزاد القديم إن وجد
            old_auction = Auction.query.filter_by(premium_number_id=test_number.id).first()
            if old_auction:
                Bid.query.filter_by(auction_id=old_auction.id).delete()
                db.session.delete(old_auction)
            
            # إنشاء مزاد نشط للاختبار
            start_time = datetime.now()
            end_time = start_time + timedelta(minutes=10)  # مزاد قصير للاختبار
            
            auction = Auction(
                title='مزاد اختبار التحديث التلقائي',
                description='مزاد لاختبار التحديث كل 3 ثوان',
                premium_number_id=test_number.id,
                starting_price=5000,
                current_price=7500,
                reserve_price=8000,
                bid_increment=250,
                start_time=start_time,
                end_time=end_time,
                status='active',
                auto_extend=True,
                commission_rate=2.0,
                total_bids=3
            )
            db.session.add(auction)
            db.session.flush()
            
            # إنشاء مزايدات اختبار
            for i, amount in enumerate([6000, 7000, 7500]):
                bid = Bid(
                    auction_id=auction.id,
                    customer_id=customer.id,
                    bid_amount=amount,
                    bid_time=start_time + timedelta(minutes=i+1)
                )
                db.session.add(bid)
            
            db.session.commit()
            
            print("=" * 50)
            print("✅ تم إنشاء مزاد الاختبار بنجاح!")
            print("=" * 50)
            print(f"🔢 الرقم: {test_number.number}")
            print(f"💰 السعر الحالي: {auction.current_price:,.0f} ر.ق")
            print(f"⏰ ينتهي في: {end_time.strftime('%H:%M:%S')}")
            print(f"🌐 رابط الاختبار:")
            print(f"   http://127.0.0.1:9898/auction/standalone/{auction.id}")
            print("=" * 50)
            print("🔄 اختبار التحديث التلقائي:")
            print("   1. افتح الرابط أعلاه")
            print("   2. راقب العداد التنازلي")
            print("   3. تأكد من التحديث كل 3 ثوان")
            print("   4. جرب تغيير السرعة من الإعدادات")
            print("=" * 50)
            
            return auction.id
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء مزاد الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("🧪 اختبار التحديث التلقائي للعرض المستقل")
    print("🔄 التحديث كل 3 ثوان")
    print("=" * 50)
    
    auction_id = create_test_auction()
    
    if auction_id:
        print("\n🎯 مزاد الاختبار جاهز!")
        print("💡 تأكد من تشغيل النظام أولاً:")
        print("   python working_app.py")
        print("\n🌐 ثم افتح الرابط:")
        print(f"   http://127.0.0.1:9898/auction/standalone/{auction_id}")
        
        # محاولة فتح المتصفح
        try:
            import webbrowser
            print("\n🌐 محاولة فتح المتصفح...")
            webbrowser.open(f"http://127.0.0.1:9898/auction/standalone/{auction_id}")
        except:
            print("💡 افتح المتصفح يدوياً على الرابط أعلاه")
    else:
        print("❌ فشل في إنشاء مزاد الاختبار")

if __name__ == '__main__':
    main()
    input("\nاضغط Enter للخروج...")
