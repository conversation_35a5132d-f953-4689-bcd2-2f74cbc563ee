import sqlite3
import os

print("🚨 إصلاح عاجل لقاعدة البيانات")
print("=" * 40)

# إصلاح قاعدة البيانات
db_files = ['auction.db', 'working_database.db', 'instance/auction.db']

for db_file in db_files:
    if os.path.exists(db_file):
        print(f"🔧 إصلاح {db_file}...")
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # التحقق من وجود الجدول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='customer'")
            if not cursor.fetchone():
                print(f"⚠️ جدول customer غير موجود في {db_file}")
                continue
            
            # إضافة الأعمدة المفقودة
            columns_to_add = [
                ("name_en", "TEXT"),
                ("id_number", "TEXT"),
                ("nationality", "TEXT DEFAULT 'قطري'"),
                ("birth_date", "DATE"),
                ("address", "TEXT"),
                ("city", "TEXT DEFAULT 'الدوحة'"),
                ("postal_code", "TEXT"),
                ("profession", "TEXT"),
                ("company", "TEXT"),
                ("monthly_income", "REAL DEFAULT 0"),
                ("created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
                ("updated_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
                ("notes", "TEXT")
            ]
            
            for column_name, column_type in columns_to_add:
                try:
                    cursor.execute(f"ALTER TABLE customer ADD COLUMN {column_name} {column_type}")
                    print(f"✅ تم إضافة العمود: {column_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" in str(e):
                        print(f"⏭️ العمود {column_name} موجود بالفعل")
                    else:
                        print(f"❌ خطأ في {column_name}: {e}")
            
            # تحديث البيانات الموجودة
            print("🔄 تحديث البيانات...")
            cursor.execute("UPDATE customer SET name_en = 'Customer Name' WHERE name_en IS NULL OR name_en = ''")
            cursor.execute("UPDATE customer SET nationality = 'قطري' WHERE nationality IS NULL OR nationality = ''")
            cursor.execute("UPDATE customer SET city = 'الدوحة' WHERE city IS NULL OR city = ''")
            
            conn.commit()
            conn.close()
            print(f"✅ تم إصلاح {db_file} بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في {db_file}: {e}")
    else:
        print(f"⚠️ الملف {db_file} غير موجود")

print("=" * 40)
print("✅ انتهى الإصلاح العاجل")
print("🚀 يمكنك الآن تشغيل النظام")
