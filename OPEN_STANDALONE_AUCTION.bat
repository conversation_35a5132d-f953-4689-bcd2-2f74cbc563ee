@echo off
chcp 65001 > nul
title تشغيل العرض المستقل - معرض بوخليفة

echo.
echo ========================================
echo   العرض المستقل للمزادات
echo   معرض بوخليفة للسيارات
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 تشغيل النظام...
start /B python working_app.py

echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak > nul

echo 🌐 فتح العرض المستقل...
echo.
echo 📋 المزادات المتاحة:
echo    - المزاد رقم 1: http://127.0.0.1:9898/auction/standalone/1
echo    - المزاد رقم 2: http://127.0.0.1:9898/auction/standalone/2
echo    - المزاد رقم 3: http://127.0.0.1:9898/auction/standalone/3
echo.

REM فتح العرض المستقل للمزاد الأول
start http://127.0.0.1:9898/auction/standalone/1

echo ✅ تم فتح العرض المستقل
echo 🔄 التحديث التلقائي كل 3 ثوان مفعل
echo.
echo 💡 لفتح مزاد آخر، اذهب إلى:
echo    http://127.0.0.1:9898/auction
echo.
echo ⏹️ اضغط أي مفتاح لإغلاق النظام...
pause > nul

echo 🛑 إيقاف النظام...
taskkill /f /im python.exe > nul 2>&1
echo ✅ تم إيقاف النظام
