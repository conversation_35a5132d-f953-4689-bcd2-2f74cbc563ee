{% extends "base.html" %}

{% block title %}إعدادات الشريط المتحرك{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-scroll me-2"></i>إعدادات الشريط المتحرك</h2>
                <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة للإعدادات
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-edit me-2"></i>تخصيص الشريط المتحرك</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <!-- Enable/Disable Ticker -->
                                    <div class="col-12 mb-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="ticker_enabled" name="ticker_enabled" 
                                                   {{ 'checked' if settings.get('ticker_enabled', 'true') == 'true' }}>
                                            <label class="form-check-label" for="ticker_enabled">
                                                <i class="fas fa-power-off me-1"></i>تفعيل الشريط المتحرك
                                            </label>
                                        </div>
                                        <small class="text-muted">إظهار أو إخفاء الشريط المتحرك في جميع الصفحات</small>
                                    </div>

                                    <!-- Ticker Speed -->
                                    <div class="col-md-6 mb-3">
                                        <label for="ticker_speed" class="form-label">
                                            <i class="fas fa-tachometer-alt me-1"></i>سرعة الحركة
                                        </label>
                                        <select class="form-select" id="ticker_speed" name="ticker_speed">
                                            <option value="30" {{ 'selected' if settings.get('ticker_speed', '50') == '30' }}>سريع جداً</option>
                                            <option value="40" {{ 'selected' if settings.get('ticker_speed', '50') == '40' }}>سريع</option>
                                            <option value="50" {{ 'selected' if settings.get('ticker_speed', '50') == '50' }}>متوسط</option>
                                            <option value="60" {{ 'selected' if settings.get('ticker_speed', '50') == '60' }}>بطيء</option>
                                            <option value="80" {{ 'selected' if settings.get('ticker_speed', '50') == '80' }}>بطيء جداً</option>
                                        </select>
                                    </div>

                                    <!-- Text Color -->
                                    <div class="col-md-6 mb-3">
                                        <label for="ticker_color" class="form-label">
                                            <i class="fas fa-palette me-1"></i>لون النص
                                        </label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="ticker_color" name="ticker_color" 
                                                   value="{{ settings.get('ticker_color', '#ffffff') }}">
                                            <input type="text" class="form-control" value="{{ settings.get('ticker_color', '#ffffff') }}" 
                                                   id="ticker_color_text" readonly>
                                        </div>
                                    </div>

                                    <!-- Background Color -->
                                    <div class="col-md-6 mb-3">
                                        <label for="ticker_bg_color" class="form-label">
                                            <i class="fas fa-fill-drip me-1"></i>لون الخلفية
                                        </label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="ticker_bg_color" name="ticker_bg_color" 
                                                   value="{{ settings.get('ticker_bg_color', '#dc3545') }}">
                                            <input type="text" class="form-control" value="{{ settings.get('ticker_bg_color', '#dc3545') }}" 
                                                   id="ticker_bg_color_text" readonly>
                                        </div>
                                    </div>

                                    <!-- Predefined Color Schemes -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-swatchbook me-1"></i>أنظمة ألوان جاهزة
                                        </label>
                                        <div class="d-flex gap-2 flex-wrap">
                                            <button type="button" class="btn btn-sm color-scheme" 
                                                    data-text="#ffffff" data-bg="#dc3545" 
                                                    style="background: #dc3545; color: #ffffff;">أحمر</button>
                                            <button type="button" class="btn btn-sm color-scheme" 
                                                    data-text="#ffffff" data-bg="#0d6efd" 
                                                    style="background: #0d6efd; color: #ffffff;">أزرق</button>
                                            <button type="button" class="btn btn-sm color-scheme" 
                                                    data-text="#ffffff" data-bg="#198754" 
                                                    style="background: #198754; color: #ffffff;">أخضر</button>
                                            <button type="button" class="btn btn-sm color-scheme" 
                                                    data-text="#000000" data-bg="#ffc107" 
                                                    style="background: #ffc107; color: #000000;">أصفر</button>
                                            <button type="button" class="btn btn-sm color-scheme" 
                                                    data-text="#ffffff" data-bg="#6f42c1" 
                                                    style="background: #6f42c1; color: #ffffff;">بنفسجي</button>
                                            <button type="button" class="btn btn-sm color-scheme" 
                                                    data-text="#ffffff" data-bg="#000000" 
                                                    style="background: #000000; color: #ffffff;">أسود</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Live Preview -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة مباشرة</h6>
                        </div>
                        <div class="card-body p-0">
                            <div id="ticker-preview" class="ticker-container" 
                                 style="background-color: {{ settings.get('ticker_bg_color', '#dc3545') }}; 
                                        color: {{ settings.get('ticker_color', '#ffffff') }}; 
                                        height: 40px; overflow: hidden; position: relative;">
                                <div class="ticker-content" style="position: absolute; white-space: nowrap; 
                                     animation: scroll-right {{ settings.get('ticker_speed', '50') }}s linear infinite;">
                                    🚗 مرحباً بكم في معرض قطر للسيارات • أحدث السيارات بأفضل الأسعار • خدمة عملاء متميزة • 
                                    📞 للاستفسار: +974 1234 5678 • 🌟 عروض خاصة هذا الشهر
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Settings -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>الإعدادات الحالية</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <strong>الحالة:</strong> 
                                    <span class="badge {{ 'bg-success' if settings.get('ticker_enabled', 'true') == 'true' else 'bg-secondary' }}">
                                        {{ 'مفعل' if settings.get('ticker_enabled', 'true') == 'true' else 'معطل' }}
                                    </span>
                                </li>
                                <li class="mb-2">
                                    <strong>السرعة:</strong> 
                                    <span class="badge bg-info">{{ settings.get('ticker_speed', '50') }}s</span>
                                </li>
                                <li class="mb-2">
                                    <strong>لون النص:</strong> 
                                    <span class="badge" style="background-color: {{ settings.get('ticker_color', '#ffffff') }}; 
                                          color: {{ '#000000' if settings.get('ticker_color', '#ffffff') == '#ffffff' else '#ffffff' }};">
                                        {{ settings.get('ticker_color', '#ffffff') }}
                                    </span>
                                </li>
                                <li class="mb-2">
                                    <strong>لون الخلفية:</strong> 
                                    <span class="badge" style="background-color: {{ settings.get('ticker_bg_color', '#dc3545') }}; 
                                          color: {{ '#000000' if settings.get('ticker_bg_color', '#dc3545') in ['#ffffff', '#ffc107'] else '#ffffff' }};">
                                        {{ settings.get('ticker_bg_color', '#dc3545') }}
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Tips -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>نصائح</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    استخدم ألواناً متباينة للنص والخلفية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    السرعة المتوسطة مناسبة لمعظم المحتوى
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    يمكن تعطيل الشريط مؤقتاً عند الحاجة
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    المعاينة تظهر الشكل النهائي
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes scroll-right {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.ticker-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.color-scheme {
    border: 2px solid transparent !important;
}

.color-scheme:hover {
    border-color: #0d6efd !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Color picker sync
    const colorInputs = [
        { picker: 'ticker_color', text: 'ticker_color_text' },
        { picker: 'ticker_bg_color', text: 'ticker_bg_color_text' }
    ];

    colorInputs.forEach(input => {
        const picker = document.getElementById(input.picker);
        const text = document.getElementById(input.text);
        
        picker.addEventListener('input', function() {
            text.value = this.value;
            updatePreview();
        });
    });

    // Color scheme buttons
    document.querySelectorAll('.color-scheme').forEach(button => {
        button.addEventListener('click', function() {
            const textColor = this.dataset.text;
            const bgColor = this.dataset.bg;
            
            document.getElementById('ticker_color').value = textColor;
            document.getElementById('ticker_color_text').value = textColor;
            document.getElementById('ticker_bg_color').value = bgColor;
            document.getElementById('ticker_bg_color_text').value = bgColor;
            
            updatePreview();
        });
    });

    // Speed change
    document.getElementById('ticker_speed').addEventListener('change', updatePreview);
    
    // Enable/disable toggle
    document.getElementById('ticker_enabled').addEventListener('change', updatePreview);

    function updatePreview() {
        const preview = document.getElementById('ticker-preview');
        const content = preview.querySelector('.ticker-content');
        const enabled = document.getElementById('ticker_enabled').checked;
        const speed = document.getElementById('ticker_speed').value;
        const textColor = document.getElementById('ticker_color').value;
        const bgColor = document.getElementById('ticker_bg_color').value;
        
        preview.style.backgroundColor = bgColor;
        preview.style.color = textColor;
        content.style.color = textColor;
        content.style.animationDuration = speed + 's';
        
        if (!enabled) {
            preview.style.opacity = '0.5';
            content.style.animationPlayState = 'paused';
        } else {
            preview.style.opacity = '1';
            content.style.animationPlayState = 'running';
        }
    }
});
</script>
{% endblock %}
