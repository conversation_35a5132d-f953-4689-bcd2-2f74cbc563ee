#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template_string, redirect, url_for, session, request, flash
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'qatar-showroom-2024'

def init_simple_db():
    """إنشاء قاعدة بيانات بسيطة"""
    conn = sqlite3.connect('simple_system.db')
    cursor = conn.cursor()
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT UNIQUE,
            password TEXT,
            name TEXT
        )
    ''')
    
    # جدول العملاء
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY,
            name_ar TEXT,
            name_en TEXT,
            phone TEXT,
            email TEXT,
            id_number TEXT,
            nationality TEXT DEFAULT 'قطري',
            city TEXT DEFAULT 'الدوحة',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول العقود
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS contracts (
            id INTEGER PRIMARY KEY,
            customer_id INTEGER,
            contract_type TEXT DEFAULT 'car_sale',
            description TEXT,
            total_amount REAL,
            payment_method TEXT DEFAULT 'cash',
            status TEXT DEFAULT 'active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
    ''')
    
    # إنشاء مستخدم افتراضي
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password, name) 
        VALUES ('admin', 'admin', 'مدير النظام')
    ''')
    
    # إنشاء عملاء تجريبيين
    sample_customers = [
        ('أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '50123456', '<EMAIL>', '12345678901'),
        ('فاطمة علي الأنصاري', 'Fatima Ali Al-Ansari', '50123457', '<EMAIL>', '12345678902'),
        ('محمد سالم المري', 'Mohammed Salem Al-Marri', '50123458', '<EMAIL>', '12345678903'),
        ('نورا خالد الثاني', 'Nora Khalid Al-Thani', '50123459', '<EMAIL>', '12345678904'),
        ('عبدالله أحمد الكواري', 'Abdullah Ahmed Al-Kuwari', '50123460', '<EMAIL>', '12345678905')
    ]
    
    for name_ar, name_en, phone, email, id_number in sample_customers:
        cursor.execute('''
            INSERT OR IGNORE INTO customers (name_ar, name_en, phone, email, id_number)
            VALUES (?, ?, ?, ?, ?)
        ''', (name_ar, name_en, phone, email, id_number))
    
    # إنشاء عقود تجريبية
    sample_contracts = [
        (1, 'car_sale', 'تويوتا كامري 2023 - أبيض', 150000, 'cash'),
        (2, 'premium_number', 'رقم مميز 777', 300000, 'installment'),
        (3, 'car_sale', 'نيسان التيما 2023 - أسود', 200000, 'installment'),
        (4, 'premium_number', 'رقم مميز 999', 450000, 'cash'),
        (5, 'car_sale', 'هوندا أكورد 2023 - فضي', 180000, 'cash')
    ]
    
    for customer_id, contract_type, description, total_amount, payment_method in sample_contracts:
        cursor.execute('''
            INSERT OR IGNORE INTO contracts (customer_id, contract_type, description, total_amount, payment_method)
            VALUES (?, ?, ?, ?, ?)
        ''', (customer_id, contract_type, description, total_amount, payment_method))
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # إحصائيات سريعة
    conn = sqlite3.connect('simple_system.db')
    cursor = conn.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM customers")
    customers_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM contracts")
    contracts_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM contracts WHERE status = 'active'")
    active_contracts = cursor.fetchone()[0]
    
    conn.close()
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معرض قطر للسيارات - النظام البسيط</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .sidebar {
                background: linear-gradient(135deg, #2c3e50, #34495e);
                min-height: 100vh;
                padding: 20px 0;
            }
            .sidebar .nav-link {
                color: #ecf0f1;
                padding: 15px 25px;
                margin: 5px 15px;
                border-radius: 10px;
                transition: all 0.3s ease;
            }
            .sidebar .nav-link:hover {
                background: rgba(255,255,255,0.1);
                color: white;
                transform: translateX(-5px);
            }
            .sidebar .nav-link.active {
                background: #3498db;
                color: white;
            }
            .main-content {
                background: #f8f9fa;
                min-height: 100vh;
                padding: 30px;
            }
            .stat-card {
                border: none;
                border-radius: 15px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .stat-card:hover {
                transform: translateY(-5px);
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3 sidebar">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-car"></i> معرض قطر للسيارات
                        </h4>
                        <small class="text-light">النظام البسيط</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                        <a class="nav-link" href="{{ url_for('customers') }}">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                        <a class="nav-link" href="{{ url_for('contracts') }}">
                            <i class="fas fa-file-contract"></i> العقود
                        </a>
                        <a class="nav-link" href="#" onclick="openAuctions()">
                            <i class="fas fa-gavel"></i> المزادات
                        </a>
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </nav>
                </div>
                
                <!-- Main Content -->
                <div class="col-md-9 main-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>لوحة التحكم الرئيسية</h2>
                        <div class="text-muted">
                            <i class="fas fa-clock"></i> {{ current_time }}
                        </div>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card stat-card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <h3>{{ customers_count }}</h3>
                                    <p>إجمالي العملاء</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card stat-card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-contract fa-3x mb-3"></i>
                                    <h3>{{ contracts_count }}</h3>
                                    <p>إجمالي العقود</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card stat-card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                                    <h3>{{ active_contracts }}</h3>
                                    <p>العقود النشطة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5><i class="fas fa-plus"></i> إجراءات سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('customers') }}" class="btn btn-outline-primary">
                                            <i class="fas fa-user-plus"></i> إضافة عميل جديد
                                        </a>
                                        <a href="{{ url_for('contracts') }}" class="btn btn-outline-success">
                                            <i class="fas fa-file-plus"></i> إنشاء عقد جديد
                                        </a>
                                        <button class="btn btn-outline-warning" onclick="openAuctions()">
                                            <i class="fas fa-gavel"></i> عرض المزادات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5><i class="fas fa-info-circle"></i> حالة النظام</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> 
                                        <strong>النظام يعمل بشكل طبيعي</strong>
                                    </div>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> قاعدة البيانات متصلة</li>
                                        <li><i class="fas fa-check text-success"></i> جميع الأقسام تعمل</li>
                                        <li><i class="fas fa-check text-success"></i> المزادات المستقلة متاحة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            function openAuctions() {
                window.open('auctions_index.html', '_blank');
            }
        </script>
    </body>
    </html>
    ''', 
    current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    customers_count=customers_count,
    contracts_count=contracts_count,
    active_contracts=active_contracts)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username == 'admin' and password == 'admin':
            session['user_id'] = 1
            session['username'] = username
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-card {
                background: rgba(255,255,255,0.95);
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                backdrop-filter: blur(10px);
                max-width: 400px;
                width: 100%;
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="fas fa-car fa-3x text-primary mb-3"></i>
                <h3>معرض قطر للسيارات</h3>
                <p class="text-muted">النظام البسيط</p>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" name="username" value="admin" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" name="password" value="admin" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </button>
            </form>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    المستخدم: admin | كلمة المرور: admin
                </small>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/customers')
def customers():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = sqlite3.connect('simple_system.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM customers ORDER BY created_at DESC")
    customers_list = cursor.fetchall()
    conn.close()
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة العملاء - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('contracts') }}">العقود</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> إدارة العملاء</h2>
                <button class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> إضافة عميل جديد
                </button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم بالعربية</th>
                                    <th>الاسم بالإنجليزية</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهوية</th>
                                    <th>الجنسية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers_list %}
                                <tr>
                                    <td>{{ customer[0] }}</td>
                                    <td><strong>{{ customer[1] }}</strong></td>
                                    <td>{{ customer[2] }}</td>
                                    <td>{{ customer[3] }}</td>
                                    <td>{{ customer[4] }}</td>
                                    <td>{{ customer[5] }}</td>
                                    <td>{{ customer[6] }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                        <button class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', customers_list=customers_list)

@app.route('/contracts')
def contracts():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = sqlite3.connect('simple_system.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT c.*, cu.name_ar 
        FROM contracts c 
        LEFT JOIN customers cu ON c.customer_id = cu.id 
        ORDER BY c.created_at DESC
    ''')
    contracts_list = cursor.fetchall()
    conn.close()
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إدارة العقود - معرض قطر للسيارات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('index') }}">
                    <i class="fas fa-car"></i> معرض قطر للسيارات
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    <a class="nav-link" href="{{ url_for('customers') }}">العملاء</a>
                    <a class="nav-link" href="{{ url_for('logout') }}">خروج</a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-contract"></i> إدارة العقود</h2>
                <button class="btn btn-success">
                    <i class="fas fa-file-plus"></i> إنشاء عقد جديد
                </button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العميل</th>
                                    <th>نوع العقد</th>
                                    <th>الوصف</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>طريقة الدفع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contract in contracts_list %}
                                <tr>
                                    <td>{{ contract[0] }}</td>
                                    <td><strong>{{ contract[9] or 'غير محدد' }}</strong></td>
                                    <td>
                                        {% if contract[2] == 'car_sale' %}
                                            <span class="badge bg-primary">بيع سيارة</span>
                                        {% elif contract[2] == 'premium_number' %}
                                            <span class="badge bg-warning">رقم مميز</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ contract[2] }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ contract[3] }}</td>
                                    <td><strong>{{ "{:,.0f}".format(contract[4]) }} ر.ق</strong></td>
                                    <td>
                                        {% if contract[5] == 'cash' %}
                                            <span class="badge bg-success">نقدي</span>
                                        {% elif contract[5] == 'installment' %}
                                            <span class="badge bg-info">أقساط</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ contract[5] }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if contract[6] == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ contract[6] }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                        <button class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button class="btn btn-sm btn-success">
                                            <i class="fas fa-print"></i> طباعة
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''', contracts_list=contracts_list)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

if __name__ == '__main__':
    init_simple_db()
    print("🚀 النظام البسيط لمعرض قطر للسيارات")
    print("=" * 50)
    print("🌐 الخادم: http://127.0.0.1:8080")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin")
    print("✅ جميع الأقسام تعمل:")
    print("   ✓ العملاء")
    print("   ✓ العقود") 
    print("   ✓ المزادات (مستقلة)")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    app.run(host='127.0.0.1', port=8080, debug=False)
