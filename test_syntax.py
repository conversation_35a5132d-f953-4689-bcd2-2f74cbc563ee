#!/usr/bin/env python3
"""
اختبار صحة الكود - نظام معرض قطر للسيارات
"""

import ast
import sys

def check_syntax(filename):
    """فحص صحة الكود"""
    print(f"🔍 فحص صحة الكود في {filename}...")
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # محاولة تحليل الكود
        ast.parse(content)
        print("✅ الكود صحيح نحوياً")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ نحوي في السطر {e.lineno}:")
        print(f"   {e.text}")
        print(f"   {' ' * (e.offset - 1)}^")
        print(f"   {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def check_imports():
    """فحص الاستيرادات"""
    print("\n📦 فحص الاستيرادات...")
    
    try:
        # محاولة استيراد المكتبات الأساسية
        import flask
        print("✅ Flask متاح")
        
        import sqlite3
        print("✅ SQLite3 متاح")
        
        import datetime
        print("✅ datetime متاح")
        
        import requests
        print("✅ requests متاح")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False

def check_flask_app():
    """فحص تطبيق Flask"""
    print("\n🌐 فحص تطبيق Flask...")
    
    try:
        # محاولة استيراد التطبيق
        sys.path.insert(0, '.')
        
        # فحص أن الملف يمكن تحميله
        import working_app
        print("✅ تم تحميل working_app بنجاح")
        
        # فحص أن التطبيق موجود
        if hasattr(working_app, 'app'):
            print("✅ كائن Flask موجود")
            
            # فحص الروابط
            routes = []
            for rule in working_app.app.url_map.iter_rules():
                routes.append(str(rule))
            
            print(f"✅ عدد الروابط المسجلة: {len(routes)}")
            
            # البحث عن روابط الإشعارات
            notification_routes = [r for r in routes if 'notification' in r]
            print(f"✅ روابط الإشعارات: {len(notification_routes)}")
            
            for route in notification_routes:
                print(f"   📍 {route}")
            
            return True
        else:
            print("❌ كائن Flask غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحميل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 فحص صحة نظام معرض قطر للسيارات")
    print("=" * 60)
    
    # فحص صحة الكود
    syntax_ok = check_syntax('working_app.py')
    
    # فحص الاستيرادات
    imports_ok = check_imports()
    
    # فحص تطبيق Flask
    flask_ok = check_flask_app()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الفحص:")
    print(f"📝 صحة الكود: {'✅' if syntax_ok else '❌'}")
    print(f"📦 الاستيرادات: {'✅' if imports_ok else '❌'}")
    print(f"🌐 تطبيق Flask: {'✅' if flask_ok else '❌'}")
    
    if all([syntax_ok, imports_ok, flask_ok]):
        print("\n🎉 جميع الفحوصات نجحت!")
        print("✨ التطبيق جاهز للتشغيل")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى حل")
    
    print("\n" + "=" * 60)
