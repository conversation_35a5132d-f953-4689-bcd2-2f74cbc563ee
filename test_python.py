#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔧 اختبار Python...")

try:
    import sys
    print(f"✅ Python version: {sys.version}")
    
    import flask
    print("✅ Flask متوفر")
    
    from flask import Flask
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return '''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>اختبار الخادم</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    text-align: center; 
                    padding: 50px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
                .container {
                    background: rgba(255,255,255,0.1);
                    padding: 30px;
                    border-radius: 20px;
                    backdrop-filter: blur(10px);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎉 الخادم يعمل بنجاح!</h1>
                <p>Python و Flask يعملان بشكل صحيح</p>
                <p>الوقت: <span id="time"></span></p>
            </div>
            <script>
                function updateTime() {
                    document.getElementById('time').textContent = new Date().toLocaleString('ar-QA');
                }
                setInterval(updateTime, 1000);
                updateTime();
            </script>
        </body>
        </html>
        '''
    
    print("🚀 بدء الخادم على http://127.0.0.1:8080")
    app.run(host='127.0.0.1', port=8080, debug=True)
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
