#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
معرض قطر للسيارات - النسخة المُصلحة
Flask app مبسط وعملي
"""

import os
import sys
from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'qatar-showroom-secret-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///working_database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# قاعدة البيانات
db = SQLAlchemy(app)

# النماذج المبسطة
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='admin')

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    phone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(100))
    nationality = db.Column(db.String(50), default='قطري')
    created_at = db.Column(db.DateTime, default=datetime.now)

class Car(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    make = db.Column(db.String(50), nullable=False)
    model = db.Column(db.String(50), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='available')
    color = db.Column(db.String(30))
    features = db.Column(db.Text)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.now)

class PremiumNumber(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.String(20), nullable=False)
    category = db.Column(db.String(20), nullable=False)
    price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='available')

class Contract(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    contract_number = db.Column(db.String(50), unique=True, nullable=False)
    contract_type = db.Column(db.String(30), nullable=False)
    payment_method = db.Column(db.String(30))
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    car_id = db.Column(db.Integer, db.ForeignKey('car.id'))
    premium_number_id = db.Column(db.Integer, db.ForeignKey('premium_number.id'))
    total_amount = db.Column(db.Float, nullable=False)
    down_payment = db.Column(db.Float, default=0)
    remaining_amount = db.Column(db.Float, default=0)
    monthly_payment = db.Column(db.Float, default=0)
    installment_months = db.Column(db.Integer, default=0)
    contract_date = db.Column(db.Date, default=datetime.now().date)
    warranty_months = db.Column(db.Integer, default=0)
    insurance_required = db.Column(db.Boolean, default=False)
    registration_included = db.Column(db.Boolean, default=False)
    special_conditions = db.Column(db.Text)
    notes = db.Column(db.Text)
    discount_amount = db.Column(db.Float, default=0)
    tax_amount = db.Column(db.Float, default=0)
    commission_amount = db.Column(db.Float, default=0)
    status = db.Column(db.String(20), default='active')
    signed_by_customer = db.Column(db.Boolean, default=False)
    signed_by_dealer = db.Column(db.Boolean, default=False)
    witness_name = db.Column(db.String(100))
    witness_id = db.Column(db.String(20))
    contract_file_path = db.Column(db.String(255))
    pdf_generated = db.Column(db.Boolean, default=False)
    word_generated = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # العلاقات
    customer = db.relationship('Customer', backref='contracts')
    car = db.relationship('Car', backref='contracts')
    premium_number = db.relationship('PremiumNumber', backref='contracts')

# الصفحات
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # الإحصائيات
    try:
        total_customers = Customer.query.count()
        total_cars = Car.query.count()
        available_cars = Car.query.filter_by(status='available').count()
        total_contracts = Contract.query.count()
        total_premium_numbers = PremiumNumber.query.count()
    except Exception as e:
        print(f"خطأ في الإحصائيات: {e}")
        total_customers = total_cars = available_cars = total_contracts = total_premium_numbers = 0
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معرض قطر للسيارات - النسخة المُصلحة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .main-container { background: rgba(255,255,255,0.95); border-radius: 20px; padding: 40px; margin: 20px auto; max-width: 1200px; }
            .header-banner { background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
            .dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 25px; margin: 30px 0; }
            .dashboard-card { background: white; border-radius: 15px; padding: 30px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease; border-left: 5px solid #3498db; }
            .dashboard-card:hover { transform: translateY(-5px); }
            .dashboard-card.customers { border-left-color: #e74c3c; }
            .dashboard-card.cars { border-left-color: #f39c12; }
            .dashboard-card.contracts { border-left-color: #27ae60; }
            .dashboard-card.numbers { border-left-color: #9b59b6; }
            .card-icon { font-size: 3rem; margin-bottom: 20px; }
            .action-btn { padding: 12px 25px; border-radius: 25px; font-weight: bold; text-decoration: none; margin: 10px 5px; transition: all 0.3s ease; }
            .action-btn:hover { transform: scale(1.05); text-decoration: none; }
        </style>
    </head>
    <body>
        <div class="main-container">
            <div class="header-banner">
                <h1><i class="fas fa-car"></i> معرض قطر للسيارات</h1>
                <p class="lead">النسخة المُصلحة - تعمل بدون أخطاء</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-check-circle"></i> Flask يعمل بشكل مثالي
                    </span>
                </div>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-database"></i> متصل بقاعدة البيانات!</h5>
                <p class="mb-0">جميع البيانات محدثة ومتاحة</p>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card customers">
                    <div class="card-icon text-danger">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>العملاء</h4>
                    <p>إدارة بيانات العملاء</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ total_customers }}</h5>
                        <small>عميل مسجل</small>
                    </div>
                    <a href="{{ url_for('customers') }}" class="btn btn-danger action-btn">
                        <i class="fas fa-eye"></i> عرض العملاء
                    </a>
                </div>
                
                <div class="dashboard-card cars">
                    <div class="card-icon text-warning">
                        <i class="fas fa-car"></i>
                    </div>
                    <h4>السيارات</h4>
                    <p>إدارة مخزون السيارات</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ available_cars }}/{{ total_cars }}</h5>
                        <small>متاح/إجمالي</small>
                    </div>
                    <a href="{{ url_for('cars') }}" class="btn btn-warning action-btn">
                        <i class="fas fa-eye"></i> عرض السيارات
                    </a>
                </div>
                
                <div class="dashboard-card contracts">
                    <div class="card-icon text-success">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <h4>العقود</h4>
                    <p>إدارة العقود والمبيعات</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ total_contracts }}</h5>
                        <small>عقد</small>
                    </div>
                    <a href="{{ url_for('contracts') }}" class="btn btn-success action-btn">
                        <i class="fas fa-eye"></i> عرض العقود
                    </a>
                </div>
                
                <div class="dashboard-card numbers">
                    <div class="card-icon text-primary">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <h4>الأرقام المميزة</h4>
                    <p>إدارة الأرقام المميزة</p>
                    <div class="mt-3">
                        <h5 class="text-primary">{{ total_premium_numbers }}</h5>
                        <small>رقم مميز</small>
                    </div>
                    <a href="{{ url_for('premium_numbers') }}" class="btn btn-primary action-btn">
                        <i class="fas fa-eye"></i> عرض الأرقام
                    </a>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                    <p class="mb-0">
                        <strong>الخادم:</strong> Flask | 
                        <strong>قاعدة البيانات:</strong> SQLite | 
                        <strong>المنفذ:</strong> 5000 | 
                        <strong>المستخدم:</strong> {{ session.username }}
                    </p>
                </div>
                <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </body>
    </html>
    ''', 
    total_customers=total_customers,
    total_cars=total_cars, 
    available_cars=available_cars,
    total_contracts=total_contracts,
    total_premium_numbers=total_premium_numbers)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # تسجيل دخول مبسط
        if username == 'admin' and password == 'admin':
            session['user_id'] = 1
            session['username'] = 'admin'
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول - معرض قطر</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .login-card { border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        </style>
    </head>
    <body class="d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card">
                        <div class="card-header bg-success text-white text-center">
                            <h3><i class="fas fa-car"></i> معرض قطر للسيارات</h3>
                            <p class="mb-0">النسخة المُصلحة</p>
                        </div>
                        <div class="card-body p-4">
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                                            {{ message }}
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" name="username" value="admin" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" name="password" value="admin" required>
                                </div>
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-sign-in-alt"></i> دخول
                                </button>
                            </form>
                            
                            <div class="mt-3 text-center">
                                <small class="text-muted">
                                    المستخدم: admin | كلمة المرور: admin
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/customers')
def customers():
    """صفحة العملاء"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    try:
        customers_list = Customer.query.all()
    except Exception as e:
        print(f"خطأ في جلب العملاء: {e}")
        customers_list = []
    
    return render_template_string('''
    <h2>العملاء</h2>
    <p>عدد العملاء: {{ customers|length }}</p>
    <a href="{{ url_for('index') }}">العودة للرئيسية</a>
    ''', customers=customers_list)

@app.route('/cars')
def cars():
    """صفحة السيارات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    try:
        cars_list = Car.query.all()
    except Exception as e:
        print(f"خطأ في جلب السيارات: {e}")
        cars_list = []
    
    return render_template_string('''
    <h2>السيارات</h2>
    <p>عدد السيارات: {{ cars|length }}</p>
    <a href="{{ url_for('index') }}">العودة للرئيسية</a>
    ''', cars=cars_list)

@app.route('/contracts')
def contracts():
    """صفحة العقود"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    try:
        contracts_list = Contract.query.all()
    except Exception as e:
        print(f"خطأ في جلب العقود: {e}")
        contracts_list = []
    
    return render_template_string('''
    <h2>العقود</h2>
    <p>عدد العقود: {{ contracts|length }}</p>
    <a href="{{ url_for('index') }}">العودة للرئيسية</a>
    ''', contracts=contracts_list)

@app.route('/premium_numbers')
def premium_numbers():
    """صفحة الأرقام المميزة"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    try:
        numbers_list = PremiumNumber.query.all()
    except Exception as e:
        print(f"خطأ في جلب الأرقام المميزة: {e}")
        numbers_list = []
    
    return render_template_string('''
    <h2>الأرقام المميزة</h2>
    <p>عدد الأرقام: {{ numbers|length }}</p>
    <a href="{{ url_for('index') }}">العودة للرئيسية</a>
    ''', numbers=numbers_list)

def setup_database():
    """إعداد قاعدة البيانات"""
    try:
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")
            
            # إنشاء مستخدم admin إذا لم يكن موجود
            if User.query.count() == 0:
                admin_user = User(
                    username='admin',
                    password_hash=generate_password_hash('admin'),
                    role='admin'
                )
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء مستخدم admin")
            
            print("✅ تم إعداد قاعدة البيانات بنجاح")
            
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")

if __name__ == '__main__':
    print("🚀 معرض قطر للسيارات - النسخة المُصلحة")
    print("=" * 50)
    
    try:
        # إعداد قاعدة البيانات
        setup_database()
        
        print("=" * 50)
        print("🎉 النظام جاهز للتشغيل!")
        print("🌐 الخادم: http://127.0.0.1:5000")
        print("👤 المستخدم: admin")
        print("🔑 كلمة المرور: admin")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 50)
        
        # تشغيل الخادم
        app.run(host='127.0.0.1', port=5000, debug=True)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
