/* Sidebar Styles */
:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --text-light: #ecf0f1;
    --text-muted: #bdc3c7;
    --border-color: #34495e;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    transition: margin-right 0.3s ease;
}

body.sidebar-open {
    margin-right: var(--sidebar-width);
}

body.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-light);
    z-index: 1000;
    transition: width 0.3s ease, transform 0.3s ease;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 3px;
}

/* Sidebar Header */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.brand {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--text-light);
    text-decoration: none;
}

.brand i {
    font-size: 1.5rem;
    margin-left: 10px;
    color: var(--accent-color);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.sidebar-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}

.sidebar.collapsed .brand span {
    display: none;
}

/* Menu Sections */
.sidebar-menu {
    padding: 20px 0;
}

.menu-section {
    margin-bottom: 30px;
}

.menu-title {
    padding: 0 20px 10px;
    font-size: 0.8rem;
    text-transform: uppercase;
    color: var(--text-muted);
    font-weight: 600;
    letter-spacing: 1px;
}

.sidebar.collapsed .menu-title {
    display: none;
}

/* Menu Items */
.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    border-right: 3px solid transparent;
}

.menu-item:hover {
    background-color: rgba(255,255,255,0.1);
    color: var(--text-light);
    text-decoration: none;
}

.menu-item.active {
    background-color: var(--accent-color);
    border-right-color: #fff;
}

.menu-item i {
    font-size: 1.1rem;
    width: 20px;
    margin-left: 15px;
    text-align: center;
}

.menu-item span {
    font-size: 0.95rem;
    white-space: nowrap;
}

.sidebar.collapsed .menu-item span {
    display: none;
}

.menu-item .badge {
    background-color: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: auto;
    margin-left: 10px;
    animation: pulse 2s infinite;
}

.menu-item .badge.zero {
    display: none;
}

.sidebar.collapsed .menu-item .badge {
    display: none;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background-color: var(--secondary-color);
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-light);
}

.user-role {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.sidebar.collapsed .user-details {
    display: none;
}

.logout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 10px;
    background-color: #e74c3c;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.logout-btn:hover {
    background-color: #c0392b;
    color: white;
    text-decoration: none;
}

/* Top Bar */
.topbar {
    position: fixed;
    top: 0;
    left: 0;
    right: var(--sidebar-width);
    height: 60px;
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 999;
    transition: right 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sidebar.collapsed ~ .topbar {
    right: var(--sidebar-collapsed-width);
}

.topbar-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 8px;
    border-radius: 5px;
    margin-left: 10px;
    transition: background-color 0.3s;
}

.sidebar-toggle-btn:hover {
    background-color: #f8f9fa;
}

.breadcrumb {
    margin-right: 20px;
}

.current-page {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.topbar-right {
    display: flex;
    align-items: center;
}

.notifications {
    margin-left: 20px;
}

.notification-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #6c757d;
    cursor: pointer;
    padding: 8px;
    border-radius: 5px;
    position: relative;
    transition: background-color 0.3s;
}

.notification-btn:hover {
    background-color: #f8f9fa;
}

.notification-count {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 2px 5px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    animation: pulse 2s infinite;
}

.notification-count.zero {
    display: none;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Notification Dropdown */
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 10px;
}

.notification-dropdown .dropdown-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 10px 15px;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.urgent {
    border-left: 3px solid #dc3545;
}

.notification-item.warning {
    border-left: 3px solid #ffc107;
}

.notification-item.info {
    border-left: 3px solid #17a2b8;
}

.notification-item.success {
    border-left: 3px solid #28a745;
}

.notification-item .notification-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    margin-right: 10px;
}

.notification-item.urgent .notification-icon {
    background-color: #dc3545;
}

.notification-item.warning .notification-icon {
    background-color: #ffc107;
}

.notification-item.info .notification-icon {
    background-color: #17a2b8;
}

.notification-item.success .notification-icon {
    background-color: #28a745;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.notification-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

/* User Menu */
.user-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 20px;
    transition: background-color 0.3s;
}

.user-btn:hover {
    background-color: #f8f9fa;
}

.user-name {
    margin-right: 8px;
    font-size: 0.9rem;
    color: #495057;
}

.user-avatar-small {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
}

/* Main Content */
.main-content {
    margin-top: 60px;
    padding: 20px;
    transition: margin-right 0.3s ease;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    body.sidebar-open,
    body.sidebar-collapsed {
        margin-right: 0;
    }
    
    .topbar {
        right: 0;
    }
    
    .sidebar-toggle-btn {
        display: block;
    }
}

/* Scrolling Ticker */
.ticker-container {
    position: fixed;
    top: 0;
    left: 0;
    right: var(--sidebar-width);
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    z-index: 998;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    border-bottom: 2px solid rgba(255,255,255,0.2);
    animation: gradientShift 10s ease-in-out infinite;
}

@keyframes gradientShift {
    0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    25% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    50% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    75% { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
}

.ticker-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255,255,255,0.1) 50%,
        transparent 100%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.sidebar.collapsed ~ .ticker-container {
    right: var(--sidebar-collapsed-width);
}

.ticker-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 20px;
    position: relative;
}

.ticker-controls {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.ticker-control-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.ticker-control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.ticker-control-btn:active {
    transform: scale(0.95);
}

.ticker-label {
    background: rgba(255,255,255,0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
    margin-left: 15px;
    white-space: nowrap;
    backdrop-filter: blur(10px);
}

.ticker-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.ticker-scroll {
    display: flex;
    align-items: center;
    height: 100%;
    animation: scroll-right 60s linear infinite;
    white-space: nowrap;
}

.ticker-item {
    display: inline-flex;
    align-items: center;
    margin-left: 40px;
    padding: 8px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.ticker-item:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.05);
}

.ticker-item i {
    margin-left: 8px;
    font-size: 1.1rem;
}

.ticker-item .item-title {
    font-weight: 600;
    margin-left: 10px;
}

.ticker-item .item-price {
    color: #ffd700;
    font-weight: bold;
    margin-right: 10px;
}

.ticker-item .item-status {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    margin-right: 10px;
}

.ticker-item.premium-number {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.ticker-item.car {
    background: linear-gradient(45deg, #48cae4, #023e8a);
    box-shadow: 0 4px 15px rgba(72, 202, 228, 0.3);
}

.ticker-item.premium-number:hover {
    background: linear-gradient(45deg, #ff5252, #ffb300);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.ticker-item.car:hover {
    background: linear-gradient(45deg, #29b6f6, #1565c0);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(72, 202, 228, 0.4);
}

@keyframes scroll-right {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Add pulsing effect for new items */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.ticker-item.new-item {
    animation: pulse 2s ease-in-out 3;
}

/* Add glow effect for VIP numbers */
.ticker-item.premium-number.vip {
    background: linear-gradient(45deg, #ffd700, #ffb300);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

.ticker-item.premium-number.vip:hover {
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
}

/* Auction size controls */
.auction-size-controls {
    position: fixed;
    top: 60px;
    right: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.size-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 5px 10px;
    margin: 2px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.size-btn:hover {
    background: #0056b3;
    transform: scale(1.05);
}

.size-btn.active {
    background: #28a745;
}

/* Responsive auction display */
.auction-container.size-small {
    max-width: 600px;
    margin: 0 auto;
}

.auction-container.size-medium {
    max-width: 900px;
    margin: 0 auto;
}

.auction-container.size-large {
    max-width: 1200px;
    margin: 0 auto;
}

.auction-container.size-fullscreen {
    max-width: 100%;
    margin: 0;
    padding: 0 10px;
}

/* Pause animation on hover */
.ticker-content:hover .ticker-scroll {
    animation-play-state: paused;
}

/* Adjust topbar and main content for ticker */
.topbar {
    top: 50px;
}

.main-content {
    margin-top: 110px; /* 50px ticker + 60px topbar */
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .ticker-container {
        right: 0;
        height: 40px;
    }

    .ticker-label {
        padding: 5px 10px;
        font-size: 0.8rem;
    }

    .ticker-item {
        padding: 5px 10px;
        margin-left: 20px;
    }

    .ticker-item .item-title {
        font-size: 0.9rem;
    }

    .topbar {
        top: 40px;
    }

    .main-content {
        margin-top: 100px; /* 40px ticker + 60px topbar */
    }
}

/* Animation */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.menu-item {
    animation: slideIn 0.3s ease forwards;
}

.menu-item:nth-child(1) { animation-delay: 0.1s; }
.menu-item:nth-child(2) { animation-delay: 0.2s; }
.menu-item:nth-child(3) { animation-delay: 0.3s; }
.menu-item:nth-child(4) { animation-delay: 0.4s; }
.menu-item:nth-child(5) { animation-delay: 0.5s; }
