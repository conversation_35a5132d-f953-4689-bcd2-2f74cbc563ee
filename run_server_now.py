#!/usr/bin/env python3
"""
Qatar Car Showroom - Immediate Server Start
"""

import os
import sys

# Set working directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)
sys.path.insert(0, script_dir)

print("=" * 50)
print("Qatar Car Showroom System")
print("=" * 50)

try:
    # Import and run the working app
    print("Loading application...")
    
    # Direct import and execution
    exec(open('working_app.py').read())
    
except FileNotFoundError:
    print("ERROR: working_app.py not found!")
    print("Make sure you're in the correct directory.")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    input("\nPress Enter to exit...")
