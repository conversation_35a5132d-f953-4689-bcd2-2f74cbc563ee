#!/usr/bin/env python3
"""
تطبيق اختبار بسيط لـ API حذف الإشعارات
"""

from flask import Flask, request, jsonify, session
import sqlite3
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'test_secret_key'

# إعداد قاعدة البيانات
def init_db():
    """إعداد قاعدة البيانات"""
    conn = sqlite3.connect('car_showroom.db')
    cursor = conn.cursor()
    
    # التحقق من وجود جدول الإشعارات المحذوفة
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='dismissed_notification'
    """)
    
    if not cursor.fetchone():
        cursor.execute('''
            CREATE TABLE dismissed_notification (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                notification_type VARCHAR(50) NOT NULL,
                notification_id VARCHAR(100) NOT NULL,
                dismissed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, notification_type, notification_id)
            )
        ''')
        conn.commit()
        print("✅ تم إنشاء جدول الإشعارات المحذوفة")
    
    conn.close()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return '''
    <h1>اختبار API حذف الإشعارات</h1>
    <p>الروابط المتاحة:</p>
    <ul>
        <li><a href="/login">تسجيل الدخول</a></li>
        <li><a href="/test">اختبار API</a></li>
        <li><a href="/api/notifications/count">عداد الإشعارات</a></li>
    </ul>
    '''

@app.route('/login')
def login():
    """تسجيل دخول تجريبي"""
    session['user_id'] = 1
    return 'تم تسجيل الدخول بنجاح! <a href="/test">اختبار API</a>'

@app.route('/test')
def test_page():
    """صفحة اختبار"""
    return '''
    <h1>اختبار API حذف الإشعارات</h1>
    <button onclick="testDismiss()">اختبار حذف إشعار</button>
    <button onclick="testRestore()">اختبار استعادة إشعار</button>
    <div id="result"></div>
    
    <script>
    function testDismiss() {
        fetch('/api/notifications/dismiss', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({type: 'test', id: '123'})
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = 
                '<h3>نتيجة الحذف:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        });
    }
    
    function testRestore() {
        fetch('/api/notifications/restore', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({type: 'test', id: '123'})
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = 
                '<h3>نتيجة الاستعادة:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        });
    }
    </script>
    '''

@app.route('/api/notifications/count')
def notifications_count():
    """عداد الإشعارات"""
    return jsonify({
        'count': 5,
        'message': 'API يعمل بنجاح'
    })

@app.route('/api/notifications/dismiss', methods=['POST'])
def dismiss_notification():
    """API لحذف إشعار"""
    print("🗑️ تم استقبال طلب حذف إشعار")
    
    if 'user_id' not in session:
        print("❌ المستخدم غير مسجل")
        return jsonify({'success': False, 'message': 'غير مسموح'})
    
    try:
        data = request.get_json()
        print(f"📝 البيانات المستقبلة: {data}")
        
        notification_type = data.get('type')
        notification_id = data.get('id')
        
        if not notification_type or not notification_id:
            print("❌ بيانات غير مكتملة")
            return jsonify({'success': False, 'message': 'بيانات غير مكتملة'})
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('car_showroom.db')
        cursor = conn.cursor()
        
        # التحقق من عدم وجود الإشعار محذوف مسبقاً
        cursor.execute('''
            SELECT id FROM dismissed_notification 
            WHERE user_id = ? AND notification_type = ? AND notification_id = ?
        ''', (session['user_id'], notification_type, notification_id))
        
        if cursor.fetchone():
            print("✅ الإشعار محذوف مسبقاً")
            conn.close()
            return jsonify({'success': True, 'message': 'الإشعار محذوف مسبقاً'})
        
        # إضافة الإشعار للمحذوفات
        cursor.execute('''
            INSERT INTO dismissed_notification 
            (user_id, notification_type, notification_id)
            VALUES (?, ?, ?)
        ''', (session['user_id'], notification_type, notification_id))
        
        conn.commit()
        conn.close()
        
        print("✅ تم حذف الإشعار بنجاح")
        return jsonify({'success': True, 'message': 'تم حذف الإشعار'})
        
    except Exception as e:
        print(f"❌ خطأ في حذف الإشعار: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {e}'})

@app.route('/api/notifications/restore', methods=['POST'])
def restore_notification():
    """API لاستعادة إشعار محذوف"""
    print("🔄 تم استقبال طلب استعادة إشعار")
    
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'غير مسموح'})
    
    try:
        data = request.get_json()
        notification_type = data.get('type')
        notification_id = data.get('id')
        
        if not notification_type or not notification_id:
            return jsonify({'success': False, 'message': 'بيانات غير مكتملة'})
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('car_showroom.db')
        cursor = conn.cursor()
        
        # حذف الإشعار من المحذوفات
        cursor.execute('''
            DELETE FROM dismissed_notification 
            WHERE user_id = ? AND notification_type = ? AND notification_id = ?
        ''', (session['user_id'], notification_type, notification_id))
        
        if cursor.rowcount > 0:
            conn.commit()
            conn.close()
            print("✅ تم استعادة الإشعار بنجاح")
            return jsonify({'success': True, 'message': 'تم استعادة الإشعار'})
        else:
            conn.close()
            print("❌ الإشعار غير موجود في المحذوفات")
            return jsonify({'success': False, 'message': 'الإشعار غير موجود في المحذوفات'})
        
    except Exception as e:
        print(f"❌ خطأ في استعادة الإشعار: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {e}'})

if __name__ == '__main__':
    print("🚀 تطبيق اختبار API حذف الإشعارات")
    print("=" * 50)
    
    # إعداد قاعدة البيانات
    init_db()
    
    print("🌐 الخادم: http://127.0.0.1:9899")
    print("🔗 صفحة الاختبار: http://127.0.0.1:9899/test")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    app.run(host='127.0.0.1', port=9899, debug=True)
