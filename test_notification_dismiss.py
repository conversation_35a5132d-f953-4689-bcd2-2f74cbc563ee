#!/usr/bin/env python3
"""
اختبار نظام حذف الإشعارات - نظام معرض قطر للسيارات
"""

import requests
import json

def login_session():
    """تسجيل الدخول وإرجاع الجلسة"""
    base_url = "http://127.0.0.1:9898"
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code in [200, 302]:
        return session, base_url
    else:
        return None, None

def test_dismiss_api():
    """اختبار API حذف الإشعارات"""
    session, base_url = login_session()
    
    if not session:
        print("❌ فشل تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # اختبار حذف إشعار
    print("\n🗑️ اختبار حذف إشعار...")
    
    dismiss_data = {
        'type': 'test_notification',
        'id': 'test_123'
    }
    
    try:
        response = session.post(
            f"{base_url}/api/notifications/dismiss",
            json=dismiss_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ تم حذف الإشعار بنجاح")
                
                # اختبار حذف نفس الإشعار مرة أخرى
                response2 = session.post(
                    f"{base_url}/api/notifications/dismiss",
                    json=dismiss_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response2.status_code == 200:
                    data2 = response2.json()
                    if data2.get('success'):
                        print("✅ التعامل مع الإشعارات المحذوفة مسبقاً: يعمل")
                    else:
                        print(f"❌ خطأ في التعامل مع الإشعارات المحذوفة: {data2.get('message')}")
                
                return True
            else:
                print(f"❌ فشل حذف الإشعار: {data.get('message')}")
                return False
        else:
            print(f"❌ خطأ في API: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_restore_api():
    """اختبار API استعادة الإشعارات"""
    session, base_url = login_session()
    
    if not session:
        return False
    
    print("\n🔄 اختبار استعادة إشعار...")
    
    restore_data = {
        'type': 'test_notification',
        'id': 'test_123'
    }
    
    try:
        response = session.post(
            f"{base_url}/api/notifications/restore",
            json=restore_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ تم استعادة الإشعار بنجاح")
                return True
            else:
                print(f"❌ فشل استعادة الإشعار: {data.get('message')}")
                return False
        else:
            print(f"❌ خطأ في API: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_notifications_count():
    """اختبار تحديث عداد الإشعارات"""
    session, base_url = login_session()
    
    if not session:
        return False
    
    print("\n📊 اختبار عداد الإشعارات...")
    
    try:
        # جلب العداد قبل الحذف
        response1 = session.get(f"{base_url}/api/notifications/count")
        
        if response1.status_code == 200:
            data1 = response1.json()
            count_before = data1.get('count', 0)
            print(f"✅ عداد الإشعارات قبل الحذف: {count_before}")
            
            # حذف إشعار تجريبي
            dismiss_data = {
                'type': 'test_count',
                'id': 'count_test_456'
            }
            
            session.post(
                f"{base_url}/api/notifications/dismiss",
                json=dismiss_data,
                headers={'Content-Type': 'application/json'}
            )
            
            # جلب العداد بعد الحذف
            response2 = session.get(f"{base_url}/api/notifications/count")
            
            if response2.status_code == 200:
                data2 = response2.json()
                count_after = data2.get('count', 0)
                print(f"✅ عداد الإشعارات بعد الحذف: {count_after}")
                
                # التحقق من وجود معلومات إضافية
                if 'upcoming_maintenance' in data2:
                    print(f"✅ إشعارات الصيانة: {data2['upcoming_maintenance']}")
                if 'expiring_inspections' in data2:
                    print(f"✅ إشعارات الفحص: {data2['expiring_inspections']}")
                
                return True
            else:
                print(f"❌ خطأ في جلب العداد بعد الحذف: {response2.status_code}")
                return False
        else:
            print(f"❌ خطأ في جلب العداد قبل الحذف: {response1.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار العداد: {e}")
        return False

def test_notifications_page():
    """اختبار صفحة الإشعارات مع أزرار الحذف"""
    session, base_url = login_session()
    
    if not session:
        return False
    
    print("\n📄 اختبار صفحة الإشعارات...")
    
    try:
        response = session.get(f"{base_url}/notifications")
        
        if response.status_code == 200:
            content = response.text
            
            # التحقق من وجود أزرار الحذف
            features = [
                ('dismissNotification', 'وظيفة حذف الإشعارات'),
                ('btn-outline-secondary dismiss-btn', 'أزرار الحذف'),
                ('fas fa-times', 'أيقونات الحذف'),
                ('notification-item', 'عناصر الإشعارات'),
                ('showToast', 'نظام الرسائل'),
                ('updateNotificationCount', 'تحديث العداد')
            ]
            
            for feature, description in features:
                if feature in content:
                    print(f"✅ {description}: موجود")
                else:
                    print(f"❌ {description}: غير موجود")
            
            return True
        else:
            print(f"❌ خطأ في تحميل صفحة الإشعارات: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفحة: {e}")
        return False

if __name__ == '__main__':
    print("🚀 اختبار نظام حذف الإشعارات")
    print("=" * 60)
    
    # اختبار API الحذف
    dismiss_test = test_dismiss_api()
    
    # اختبار API الاستعادة
    restore_test = test_restore_api()
    
    # اختبار عداد الإشعارات
    count_test = test_notifications_count()
    
    # اختبار صفحة الإشعارات
    page_test = test_notifications_page()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print("=" * 60)
    
    print(f"🗑️ API حذف الإشعارات: {'✅ يعمل' if dismiss_test else '❌ لا يعمل'}")
    print(f"🔄 API استعادة الإشعارات: {'✅ يعمل' if restore_test else '❌ لا يعمل'}")
    print(f"📊 تحديث عداد الإشعارات: {'✅ يعمل' if count_test else '❌ لا يعمل'}")
    print(f"📄 صفحة الإشعارات: {'✅ تعمل' if page_test else '❌ لا تعمل'}")
    
    # النتيجة النهائية
    if all([dismiss_test, restore_test, count_test, page_test]):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✨ نظام حذف الإشعارات يعمل بشكل مثالي!")
        print("\n🔧 الميزات المتاحة:")
        print("   🗑️ حذف الإشعارات الفردية")
        print("   🔄 استعادة الإشعارات المحذوفة")
        print("   📊 تحديث العداد تلقائياً")
        print("   ✨ تأثيرات بصرية عند الحذف")
        print("   👤 إشعارات مخصصة لكل مستخدم")
    else:
        print("\n⚠️ هناك بعض المشاكل:")
        if not dismiss_test:
            print("   🗑️ مشكلة في API حذف الإشعارات")
        if not restore_test:
            print("   🔄 مشكلة في API استعادة الإشعارات")
        if not count_test:
            print("   📊 مشكلة في تحديث العداد")
        if not page_test:
            print("   📄 مشكلة في صفحة الإشعارات")
    
    print("\n" + "=" * 60)
