#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Customer, PremiumNumber, Auction, Bid
from datetime import datetime, timedelta

def fix_winner_display():
    with app.app_context():
        print("🔧 إصلاح عرض الفائز...")
        
        # إنشاء الجداول
        db.create_all()
        
        # حذف البيانات القديمة
        print("🗑️ حذف البيانات القديمة...")
        Bid.query.delete()
        Auction.query.delete()
        Customer.query.delete()
        PremiumNumber.query.delete()
        db.session.commit()
        
        # إنشاء عميل فائز
        print("👤 إنشاء العميل الفائز...")
        customer = Customer(
            name_ar="أحمد محمد الكعبي",
            phone="+974 5555 1234",
            email="<EMAIL>"
        )
        db.session.add(customer)
        db.session.commit()
        print(f"✅ تم إنشاء العميل: {customer.name_ar} (ID: {customer.id})")
        
        # إنشاء رقم مميز
        print("🔢 إنشاء الرقم المميز...")
        premium_number = PremiumNumber(
            number="777777",
            category="VIP",
            price=75000,
            status="auction"
        )
        db.session.add(premium_number)
        db.session.commit()
        print(f"✅ تم إنشاء الرقم المميز: {premium_number.number} (ID: {premium_number.id})")
        
        # إنشاء مزاد منتهي
        print("🏆 إنشاء المزاد المنتهي...")
        end_time = datetime.now() - timedelta(hours=1)
        start_time = end_time - timedelta(hours=2)
        
        auction = Auction(
            title=f"مزاد الرقم المميز {premium_number.number}",
            description="مزاد تجريبي لاختبار عرض الفائز",
            premium_number_id=premium_number.id,
            starting_price=50000,
            current_price=78850,
            reserve_price=60000,
            bid_increment=1000,
            start_time=start_time,
            end_time=end_time,
            status="ended",
            auto_extend=False,
            commission_rate=5.0,
            commission_amount=3942.5,
            total_bids=1,
            winner="أحمد محمد الكعبي",
            winner_id=customer.id
        )
        db.session.add(auction)
        db.session.commit()
        print(f"✅ تم إنشاء المزاد: {auction.title} (ID: {auction.id})")
        print(f"   الحالة: {auction.status}")
        print(f"   الفائز: {auction.winner}")
        print(f"   ID الفائز: {auction.winner_id}")
        
        # إنشاء مزايدة فائزة
        print("💰 إنشاء المزايدة الفائزة...")
        bid = Bid(
            auction_id=auction.id,
            customer_id=customer.id,
            bid_amount=78850,
            bid_time=end_time - timedelta(minutes=5)
        )
        db.session.add(bid)
        db.session.commit()
        print(f"✅ تم إنشاء المزايدة الفائزة: {bid.bid_amount:,.0f} ر.ق")
        print(f"   ID المزايدة: {bid.id}")
        print(f"   ID العميل: {bid.customer_id}")
        print(f"   ID المزاد: {bid.auction_id}")
        
        # التحقق من البيانات
        print("\n🔍 التحقق من البيانات...")
        auction_check = Auction.query.get(auction.id)
        bid_check = Bid.query.filter_by(auction_id=auction.id).first()
        customer_check = Customer.query.get(customer.id)
        
        print(f"المزاد موجود: {auction_check is not None}")
        print(f"المزايدة موجودة: {bid_check is not None}")
        print(f"العميل موجود: {customer_check is not None}")
        
        if bid_check and customer_check:
            print(f"اسم العميل من المزايدة: {bid_check.customer.name_ar}")
        
        print("\n" + "="*50)
        print("✅ تم إصلاح عرض الفائز بنجاح!")
        print(f"🏆 الفائز: {customer.name_ar}")
        print(f"💰 المبلغ: {bid.bid_amount:,.0f} ر.ق")
        print(f"🌐 اختبر: http://127.0.0.1:9898/auction/standalone/{auction.id}")
        print("="*50)
        
        return auction.id

if __name__ == "__main__":
    try:
        auction_id = fix_winner_display()
        print(f"\n🎯 افتح الرابط: http://127.0.0.1:9898/auction/standalone/{auction_id}")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
