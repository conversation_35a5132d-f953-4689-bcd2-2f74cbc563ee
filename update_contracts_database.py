#!/usr/bin/env python3
"""
تحديث قاعدة البيانات لنظام العقود المتطور
"""

import sqlite3
import os
from datetime import datetime, timedelta

def update_contracts_tables():
    """تحديث جداول العقود مع الحقول الجديدة"""
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "working_database.db")
    
    print("📋 تحديث قاعدة البيانات لنظام العقود المتطور")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على الأعمدة الموجودة في جدول العقود
        cursor.execute("PRAGMA table_info(contract)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        print(f"📋 الأعمدة الموجودة: {len(existing_columns)}")
        
        # قائمة الأعمدة الجديدة المراد إضافتها
        new_columns = [
            ("remaining_amount", "FLOAT DEFAULT 0"),
            ("monthly_payment", "FLOAT DEFAULT 0"),
            ("installment_months", "INTEGER DEFAULT 0"),
            ("interest_rate", "FLOAT DEFAULT 0"),
            ("contract_date", "DATE"),
            ("delivery_date", "DATE"),
            ("warranty_months", "INTEGER DEFAULT 0"),
            ("insurance_required", "BOOLEAN DEFAULT 0"),
            ("registration_included", "BOOLEAN DEFAULT 0"),
            ("special_conditions", "TEXT"),
            ("notes", "TEXT"),
            ("discount_amount", "FLOAT DEFAULT 0"),
            ("tax_amount", "FLOAT DEFAULT 0"),
            ("commission_amount", "FLOAT DEFAULT 0"),
            ("signed_by_customer", "BOOLEAN DEFAULT 0"),
            ("signed_by_dealer", "BOOLEAN DEFAULT 0"),
            ("witness_name", "VARCHAR(100)"),
            ("witness_id", "VARCHAR(20)"),
            ("contract_file_path", "VARCHAR(255)"),
            ("pdf_generated", "BOOLEAN DEFAULT 0"),
            ("word_generated", "BOOLEAN DEFAULT 0"),
            ("updated_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("created_by", "INTEGER")
        ]
        
        # إضافة الأعمدة الجديدة
        added_count = 0
        for column_name, column_type in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE contract ADD COLUMN {column_name} {column_type}"
                    cursor.execute(sql)
                    print(f"✅ تم إضافة العمود: {column_name}")
                    added_count += 1
                except Exception as e:
                    print(f"❌ خطأ في إضافة العمود {column_name}: {e}")
            else:
                print(f"⚠️ العمود {column_name} موجود بالفعل")
        
        # إنشاء جدول دفعات التقسيط
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS installment_payment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                installment_number INTEGER NOT NULL,
                due_date DATE NOT NULL,
                amount FLOAT NOT NULL,
                paid_amount FLOAT DEFAULT 0,
                payment_date DATE,
                status VARCHAR(20) DEFAULT 'pending',
                late_fee FLOAT DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (contract_id) REFERENCES contract (id)
            )
        ''')
        print("✅ تم إنشاء جدول دفعات التقسيط")
        
        # إنشاء جدول مستندات العقد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS contract_document (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contract_id INTEGER NOT NULL,
                document_type VARCHAR(30) NOT NULL,
                document_name VARCHAR(100) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_size INTEGER,
                mime_type VARCHAR(50),
                uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                uploaded_by INTEGER,
                FOREIGN KEY (contract_id) REFERENCES contract (id),
                FOREIGN KEY (uploaded_by) REFERENCES user (id)
            )
        ''')
        print("✅ تم إنشاء جدول مستندات العقد")
        
        # تحديث العقود الموجودة بقيم افتراضية
        update_existing_contracts(cursor)
        
        conn.commit()
        print(f"\n🎉 تم تحديث قاعدة البيانات بنجاح!")
        print(f"📊 تم إضافة {added_count} عمود جديد")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False
    
    finally:
        if conn:
            conn.close()

def update_existing_contracts(cursor):
    """تحديث العقود الموجودة بقيم افتراضية"""
    
    print("\n📝 تحديث العقود الموجودة...")
    
    try:
        # تحديث تاريخ العقد للعقود التي لا تحتوي على تاريخ
        cursor.execute("""
            UPDATE contract 
            SET contract_date = date('now') 
            WHERE contract_date IS NULL
        """)
        
        # تحديث المبلغ المتبقي
        cursor.execute("""
            UPDATE contract 
            SET remaining_amount = total_amount - down_payment 
            WHERE remaining_amount IS NULL OR remaining_amount = 0
        """)
        
        # تحديث فترة الضمان الافتراضية
        cursor.execute("""
            UPDATE contract 
            SET warranty_months = 12 
            WHERE warranty_months IS NULL OR warranty_months = 0
        """)
        
        print("✅ تم تحديث العقود الموجودة")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث العقود الموجودة: {e}")

def create_sample_installment_contracts(cursor):
    """إنشاء عقود تقسيط تجريبية"""
    
    print("\n💳 إنشاء عقود تقسيط تجريبية...")
    
    try:
        # التحقق من وجود عملاء وسيارات
        cursor.execute("SELECT id FROM customer LIMIT 1")
        customer = cursor.fetchone()
        
        cursor.execute("SELECT id FROM car WHERE status = 'available' LIMIT 1")
        car = cursor.fetchone()
        
        if customer and car:
            # إنشاء عقد تقسيط تجريبي
            contract_data = {
                'contract_number': f'INST-{datetime.now().strftime("%Y%m%d")}-001',
                'contract_type': 'installment',
                'payment_type': 'installment',
                'customer_id': customer[0],
                'car_id': car[0],
                'total_amount': 150000,
                'down_payment': 30000,
                'remaining_amount': 120000,
                'monthly_payment': 5000,
                'installment_months': 24,
                'interest_rate': 8.5,
                'contract_date': datetime.now().date(),
                'warranty_months': 24,
                'status': 'active'
            }
            
            cursor.execute('''
                INSERT INTO contract (
                    contract_number, contract_type, payment_type, customer_id, car_id,
                    total_amount, down_payment, remaining_amount, monthly_payment,
                    installment_months, interest_rate, contract_date, warranty_months, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                contract_data['contract_number'], contract_data['contract_type'],
                contract_data['payment_type'], contract_data['customer_id'],
                contract_data['car_id'], contract_data['total_amount'],
                contract_data['down_payment'], contract_data['remaining_amount'],
                contract_data['monthly_payment'], contract_data['installment_months'],
                contract_data['interest_rate'], contract_data['contract_date'],
                contract_data['warranty_months'], contract_data['status']
            ))
            
            contract_id = cursor.lastrowid
            
            # إنشاء جدول الأقساط
            for i in range(1, 25):  # 24 قسط
                due_date = datetime.now().date() + timedelta(days=30 * i)
                cursor.execute('''
                    INSERT INTO installment_payment (
                        contract_id, installment_number, due_date, amount, status
                    ) VALUES (?, ?, ?, ?, ?)
                ''', (contract_id, i, due_date, 5000, 'pending'))
            
            print("✅ تم إنشاء عقد تقسيط تجريبي مع جدول الأقساط")
        else:
            print("⚠️ لا توجد بيانات كافية لإنشاء عقد تجريبي")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء العقود التجريبية: {e}")

if __name__ == "__main__":
    print("📋 تطوير نظام العقود المتطور")
    print("=" * 60)
    
    if update_contracts_tables():
        print("\n🎉 تم تحديث النظام بنجاح!")
        print("💡 الميزات الجديدة:")
        print("   - 4 أنواع عقود: بيع سيارة، شراء سيارة، بيع رقم مميز، تقسيط")
        print("   - 6 طرق دفع: نقدي، تقسيط، تمويل بنكي، إيجار، استبدال، مختلط")
        print("   - حقول مالية شاملة: خصم، ضريبة، فوائد، أقساط")
        print("   - تفاصيل العقد: ضمان، تأمين، ترخيص، شاهد")
        print("   - جدول أقساط تلقائي")
        print("   - نظام مستندات متكامل")
        print("   - واجهة عرض احترافية")
    else:
        print("\n❌ فشل في تحديث النظام")
    
    input("\nاضغط Enter للخروج...")
