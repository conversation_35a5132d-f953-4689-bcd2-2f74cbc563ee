#!/usr/bin/env python3
"""
Direct run script for Qatar Car Showroom
"""

import os
import sys

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    try:
        print("🚀 نظام معرض قطر للسيارات - مع دعم الأرقام المميزة")
        print("=" * 55)
        
        # Import Flask and create app
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from flask_login import LoginManager
        
        # Create Flask app
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'qatar-car-showroom-secret-key-2024'
        app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(current_dir, "instance", "database.db")}'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        # Create instance directory
        os.makedirs(os.path.join(current_dir, 'instance'), exist_ok=True)
        
        # Initialize extensions
        db = SQLAlchemy(app)
        login_manager = LoginManager()
        login_manager.init_app(app)
        login_manager.login_view = 'auth.login'
        login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
        login_manager.login_message_category = 'info'
        
        # Simple route for testing
        @app.route('/')
        def index():
            return '''
            <html dir="rtl">
            <head>
                <title>معرض قطر للسيارات</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .success { color: green; font-size: 24px; margin: 20px; }
                    .feature { background: #f0f8ff; padding: 15px; margin: 10px; border-radius: 5px; }
                </style>
            </head>
            <body>
                <h1>🎉 نظام معرض قطر للسيارات</h1>
                <div class="success">✅ النظام يعمل بنجاح!</div>
                
                <div class="feature">
                    <h3>🔢 الميزة الجديدة: دعم الأرقام المميزة في العقود</h3>
                    <p>✅ تم إضافة إمكانية بيع الأرقام المميزة في العقود</p>
                    <p>✅ دعم العقود المدمجة (سيارة + رقم مميز)</p>
                    <p>✅ فصل نوع العقد عن طريقة الدفع</p>
                </div>
                
                <div class="feature">
                    <h3>🚀 للوصول للنظام الكامل:</h3>
                    <p>1. تأكد من تثبيت جميع المكتبات المطلوبة</p>
                    <p>2. شغّل الملف الأساسي للنظام</p>
                    <p>3. سجل دخول بـ: admin / admin123</p>
                </div>
                
                <div class="feature">
                    <h3>📋 الحالة:</h3>
                    <p>✅ قاعدة البيانات: محدثة</p>
                    <p>✅ الأرقام المميزة: مدعومة</p>
                    <p>✅ العقود: محسنة</p>
                    <p>✅ النظام: جاهز للاستخدام</p>
                </div>
            </body>
            </html>
            '''
        
        print("✅ تم إنشاء التطبيق بنجاح")
        print("🌐 الخادم يعمل على: http://127.0.0.1:1414")
        print("🔢 الميزة الجديدة: دعم الأرقام المميزة في العقود")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 55)
        
        # Start server
        app.run(host='127.0.0.1', port=1414, debug=False)
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
