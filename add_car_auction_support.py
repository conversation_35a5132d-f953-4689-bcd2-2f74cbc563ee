#!/usr/bin/env python3
"""
إضافة دعم مزادات السيارات - نظام معرض قطر للسيارات
"""

import sqlite3
from datetime import datetime
import os

def add_car_auction_support():
    """إضافة دعم مزادات السيارات"""
    
    print("🚗 إضافة دعم مزادات السيارات...")
    print("=" * 60)
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = 'working_database.db'
        if not os.path.exists(db_path):
            db_path = 'car_showroom.db'
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. إضافة عمود نوع المزاد إلى جدول auction
        print("📊 تحديث جدول المزادات...")

        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(auction)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'auction_type' not in columns:
            cursor.execute("ALTER TABLE auction ADD COLUMN auction_type VARCHAR(20) DEFAULT 'premium_number'")
            print("✅ تم إضافة عمود نوع المزاد")

        if 'car_id' not in columns:
            cursor.execute("ALTER TABLE auction ADD COLUMN car_id INTEGER")
            print("✅ تم إضافة عمود معرف السيارة")

        # تحديث القيود - جعل premium_number_id قابل للقيمة NULL
        print("🔧 تحديث قيود الجدول...")

        # إنشاء جدول مؤقت بالهيكل الجديد
        cursor.execute('''
            CREATE TABLE auction_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                auction_type VARCHAR(20) DEFAULT 'premium_number',
                premium_number_id INTEGER,
                car_id INTEGER,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                starting_price FLOAT NOT NULL,
                reserve_price FLOAT,
                current_price FLOAT DEFAULT 0,
                bid_increment FLOAT DEFAULT 1000,
                start_time DATETIME NOT NULL,
                end_time DATETIME NOT NULL,
                status VARCHAR(20) DEFAULT 'scheduled',
                winner_id INTEGER,
                winning_bid_id INTEGER,
                total_bids INTEGER DEFAULT 0,
                views INTEGER DEFAULT 0,
                auto_extend BOOLEAN DEFAULT 1,
                extend_time INTEGER DEFAULT 300,
                commission_rate FLOAT DEFAULT 5.0,
                commission_amount FLOAT DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
                FOREIGN KEY (car_id) REFERENCES car (id),
                FOREIGN KEY (winner_id) REFERENCES customer (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')

        # نسخ البيانات الموجودة
        cursor.execute('''
            INSERT INTO auction_new
            SELECT id, 'premium_number', premium_number_id, NULL, title, description,
                   starting_price, reserve_price, current_price, bid_increment,
                   start_time, end_time, status, winner_id, winning_bid_id,
                   total_bids, views, auto_extend, extend_time, commission_rate,
                   commission_amount, created_at, created_by
            FROM auction
        ''')

        # حذف الجدول القديم وإعادة تسمية الجديد
        cursor.execute('DROP TABLE auction')
        cursor.execute('ALTER TABLE auction_new RENAME TO auction')

        print("✅ تم تحديث هيكل جدول المزادات")
        
        # 2. إنشاء جدول صور المزاد
        print("🖼️ إنشاء جدول صور المزاد...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auction_image (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                auction_id INTEGER NOT NULL,
                image_path VARCHAR(255) NOT NULL,
                image_name VARCHAR(100) NOT NULL,
                is_primary BOOLEAN DEFAULT 0,
                upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                file_size INTEGER,
                FOREIGN KEY (auction_id) REFERENCES auction (id) ON DELETE CASCADE
            )
        ''')
        
        print("✅ تم إنشاء جدول صور المزاد")
        
        # 3. إنشاء فهارس لتحسين الأداء
        print("🔍 إنشاء الفهارس...")
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_auction_type 
            ON auction(auction_type)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_auction_car_id 
            ON auction(car_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_auction_image_auction_id 
            ON auction_image(auction_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_auction_image_primary 
            ON auction_image(auction_id, is_primary)
        ''')
        
        print("✅ تم إنشاء الفهارس")
        
        # 4. إضافة بيانات تجريبية
        print("📝 إضافة بيانات تجريبية...")
        
        # التحقق من وجود سيارات
        cursor.execute("SELECT COUNT(*) FROM car WHERE status = 'available'")
        available_cars = cursor.fetchone()[0]
        
        if available_cars > 0:
            # إضافة مزاد تجريبي للسيارة
            cursor.execute("SELECT id, make, model, year, price FROM car WHERE status = 'available' LIMIT 1")
            car_data = cursor.fetchone()
            
            if car_data:
                car_id, make, model, year, price = car_data
                
                # إنشاء مزاد للسيارة
                start_time = datetime.now()
                end_time = datetime.now().replace(hour=23, minute=59, second=59)
                
                cursor.execute('''
                    INSERT INTO auction (
                        auction_type, car_id, title, description, starting_price, 
                        current_price, start_time, end_time, status, commission_rate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    'car',
                    car_id,
                    f'مزاد السيارة {make} {model} {year}',
                    f'مزاد علني للسيارة {make} {model} موديل {year} في حالة ممتازة',
                    price * 0.8,  # سعر ابتدائي أقل من سعر البيع
                    price * 0.8,
                    start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    end_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'active',
                    5.0
                ))
                
                # تحديث حالة السيارة
                cursor.execute("UPDATE car SET status = 'auction' WHERE id = ?", (car_id,))
                
                print(f"✅ تم إنشاء مزاد تجريبي للسيارة {make} {model}")
        
        # 5. إنشاء مجلدات الصور
        print("📁 إنشاء مجلدات الصور...")
        
        upload_dirs = [
            'static/uploads',
            'static/uploads/auctions'
        ]
        
        for directory in upload_dirs:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ تم إنشاء مجلد: {directory}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إضافة دعم مزادات السيارات بنجاح!")
        print("\n💡 الميزات الجديدة:")
        print("   🚗 مزادات السيارات مع الصور")
        print("   🖼️ رفع صور متعددة للمزاد")
        print("   📱 واجهة محسنة لاختيار نوع المزاد")
        print("   🔍 فهارس محسنة للأداء")
        print("   📊 إحصائيات منفصلة للسيارات والأرقام")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة دعم مزادات السيارات: {e}")
        return False

def test_car_auction_support():
    """اختبار دعم مزادات السيارات"""
    
    print("\n🔍 اختبار دعم مزادات السيارات...")
    
    try:
        db_path = 'working_database.db'
        if not os.path.exists(db_path):
            db_path = 'car_showroom.db'
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # اختبار الجداول الجديدة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auction_image'")
        if cursor.fetchone():
            print("✅ جدول صور المزاد: موجود")
        else:
            print("❌ جدول صور المزاد: غير موجود")
        
        # اختبار الأعمدة الجديدة
        cursor.execute("PRAGMA table_info(auction)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'auction_type' in columns:
            print("✅ عمود نوع المزاد: موجود")
        else:
            print("❌ عمود نوع المزاد: غير موجود")
        
        if 'car_id' in columns:
            print("✅ عمود معرف السيارة: موجود")
        else:
            print("❌ عمود معرف السيارة: غير موجود")
        
        # اختبار البيانات
        cursor.execute("SELECT COUNT(*) FROM auction WHERE auction_type = 'car'")
        car_auctions = cursor.fetchone()[0]
        print(f"📊 عدد مزادات السيارات: {car_auctions}")
        
        cursor.execute("SELECT COUNT(*) FROM auction WHERE auction_type = 'premium_number'")
        number_auctions = cursor.fetchone()[0]
        print(f"📊 عدد مزادات الأرقام: {number_auctions}")
        
        conn.close()
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == '__main__':
    print("🚀 تطوير نظام مزادات السيارات")
    print("=" * 60)
    
    # إضافة الدعم
    success = add_car_auction_support()
    
    if success:
        # اختبار النظام
        test_success = test_car_auction_support()
        
        print("\n" + "=" * 60)
        print("🎉 تم تطوير نظام مزادات السيارات بنجاح!")
        print("\n🌟 الميزات المتاحة الآن:")
        print("   🚗 مزادات السيارات مع الصور")
        print("   🔢 مزادات الأرقام المميزة")
        print("   🖼️ رفع صور متعددة")
        print("   📱 واجهة موحدة للمزادات")
        print("   📊 إحصائيات شاملة")
        
        if test_success:
            print("\n✅ النظام جاهز للاستخدام!")
        else:
            print("\n⚠️ هناك مشاكل في الاختبارات")
    else:
        print("\n❌ فشل في تطوير النظام")
    
    print("\n" + "=" * 60)
