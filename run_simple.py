#!/usr/bin/env python3
"""
Qatar Car Showroom - Simple Run Script
Enhanced with Premium Numbers Support in Contracts
"""

from app import create_app, db
from app.models.user import User
from werkzeug.security import generate_password_hash
import webbrowser
import threading
import time

def setup_admin():
    """Setup admin user"""
    try:
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin',
                is_active=True
            )
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء مستخدم admin")
        else:
            print("✅ مستخدم admin موجود")
    except Exception as e:
        print(f"⚠️ تحذير في إعداد المستخدم: {e}")

def open_browser_delayed():
    """Open browser after delay"""
    time.sleep(3)
    try:
        webbrowser.open('http://127.0.0.1:1414')
        print("🌐 تم فتح المتصفح تلقائياً")
    except:
        print("💡 افتح المتصفح يدوياً على: http://127.0.0.1:1414")

if __name__ == '__main__':
    print("🚀 نظام معرض قطر للسيارات - مع دعم الأرقام المميزة في العقود")
    print("=" * 70)
    
    app = create_app()
    
    with app.app_context():
        db.create_all()
        setup_admin()
    
    print("=" * 60)
    print("🎉 نظام معرض قطر للسيارات - مع دعم الأرقام المميزة")
    print("=" * 60)
    print("🌐 الخادم يعمل على: http://127.0.0.1:1414")
    print("🔐 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("✨ الميزات الجديدة:")
    print("   🚗 بيع السيارات")
    print("   🔢 بيع الأرقام المميزة")
    print("   🎯 بيع السيارات والأرقام المميزة معاً")
    print("   💳 طرق دفع متعددة (نقدي، تقسيط، إيجار، استبدال)")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)
    
    # Open browser in background
    threading.Thread(target=open_browser_delayed, daemon=True).start()
    
    # Start server
    app.run(host='127.0.0.1', port=1414, debug=False, use_reloader=False)
