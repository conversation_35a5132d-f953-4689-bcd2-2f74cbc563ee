#!/usr/bin/env python3
"""
اختبار صفحة الصيانة - نظام معرض قطر للسيارات
"""

import requests
import json

def test_maintenance_page():
    """اختبار صفحة الصيانة"""
    base_url = "http://127.0.0.1:9898"
    
    # إنشاء جلسة
    session = requests.Session()
    
    try:
        print("🔐 تسجيل الدخول...")
        
        # الحصول على صفحة تسجيل الدخول
        login_page = session.get(f"{base_url}/login")
        print(f"✅ صفحة تسجيل الدخول: {login_page.status_code}")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        print(f"✅ تسجيل الدخول: {login_response.status_code}")

        # إذا كان 302 فهذا يعني نجح تسجيل الدخول وتم التحويل
        if login_response.status_code not in [200, 302]:
            print("❌ فشل تسجيل الدخول")
            return False
        
        print("🔧 اختبار صفحة الصيانة...")
        
        # اختبار صفحة الصيانة
        maintenance_response = session.get(f"{base_url}/maintenance")
        print(f"📊 صفحة الصيانة: {maintenance_response.status_code}")
        
        if maintenance_response.status_code == 200:
            print("✅ صفحة الصيانة تعمل بنجاح!")
            
            # التحقق من وجود المحتوى المطلوب
            content = maintenance_response.text
            if "إدارة الصيانة والفحص" in content:
                print("✅ العنوان موجود")
            if "إضافة صيانة" in content:
                print("✅ زر إضافة الصيانة موجود")
            if "إضافة فحص" in content:
                print("✅ زر إضافة الفحص موجود")
            
            return True
        else:
            print(f"❌ خطأ في صفحة الصيانة: {maintenance_response.status_code}")
            print(f"المحتوى: {maintenance_response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_maintenance_api():
    """اختبار API الصيانة"""
    base_url = "http://127.0.0.1:9898"
    
    try:
        print("🔍 اختبار API الإشعارات...")
        
        # اختبار API الإشعارات
        notifications_response = requests.get(f"{base_url}/api/notifications/count")
        print(f"📊 API الإشعارات: {notifications_response.status_code}")
        
        if notifications_response.status_code == 200:
            data = notifications_response.json()
            print(f"✅ عدد الإشعارات: {data.get('count', 0)}")
            
            # التحقق من وجود إشعارات الصيانة
            if 'upcoming_maintenance' in data:
                print(f"✅ إشعارات الصيانة المستحقة: {data['upcoming_maintenance']}")
            if 'expiring_inspections' in data:
                print(f"✅ إشعارات الفحوصات المنتهية: {data['expiring_inspections']}")
            
            return True
        else:
            print(f"❌ خطأ في API الإشعارات: {notifications_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

if __name__ == '__main__':
    print("🚀 اختبار نظام الصيانة والفحص")
    print("=" * 50)
    
    # اختبار API أولاً
    api_test = test_maintenance_api()
    
    print("\n" + "=" * 50)
    
    # اختبار صفحة الصيانة
    page_test = test_maintenance_page()
    
    print("\n" + "=" * 50)
    print("📋 نتائج الاختبار:")
    print(f"   🔗 API الإشعارات: {'✅ يعمل' if api_test else '❌ لا يعمل'}")
    print(f"   📄 صفحة الصيانة: {'✅ تعمل' if page_test else '❌ لا تعمل'}")
    
    if api_test and page_test:
        print("\n🎉 جميع الاختبارات نجحت! نظام الصيانة يعمل بشكل مثالي!")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")
