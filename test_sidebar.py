#!/usr/bin/env python3
"""
Test Sidebar Application
"""

import os
from flask import Flask, render_template, session, redirect, url_for

# Create Flask app
app = Flask(__name__, static_folder='static', template_folder='templates')
app.config['SECRET_KEY'] = 'test-sidebar-key'

@app.route('/')
def index():
    """الصفحة الرئيسية مع الشريط الجانبي"""
    # Simulate session
    session['user_id'] = 1
    session['username'] = 'admin'
    
    # Sample data
    return render_template('index_sidebar.html',
        total_cars=25,
        available_cars=18,
        sold_cars=7,
        reserved_cars=2,
        maintenance_cars=1,
        total_premium_numbers=50,
        available_premium_numbers=35,
        total_contracts=15,
        recent_cars=[],
        total_installments=45,
        due_today_count=3,
        overdue_count=2
    )

@app.route('/cars')
def cars():
    return "صفحة السيارات"

@app.route('/premium_numbers')
def premium_numbers():
    return "صفحة الأرقام المميزة"

@app.route('/auctions')
def auctions():
    return "صفحة المزادات"

@app.route('/customers')
def customers():
    return "صفحة العملاء"

@app.route('/contracts')
def contracts():
    return "صفحة العقود"

@app.route('/installments_management')
def installments_management():
    return "صفحة إدارة الأقساط"

@app.route('/installments_due_today')
def installments_due_today():
    return "صفحة الأقساط المستحقة اليوم"

@app.route('/installments_reports')
def installments_reports():
    return "صفحة تقارير الأقساط"

@app.route('/user_profile')
def user_profile():
    return "صفحة الملف الشخصي"

@app.route('/settings')
def settings():
    return "صفحة الإعدادات"

@app.route('/admin_users')
def admin_users():
    return "صفحة إدارة المستخدمين"

@app.route('/admin_panel')
def admin_panel():
    return "صفحة لوحة التحكم"

@app.route('/add_contract')
def add_contract():
    return "صفحة إضافة عقد"

@app.route('/logout')
def logout():
    return "تسجيل الخروج"

if __name__ == '__main__':
    print("🚀 اختبار الشريط الجانبي")
    print("🌐 الخادم: http://127.0.0.1:5000")
    print("============================================================")
    app.run(debug=True, host='127.0.0.1', port=5000)
