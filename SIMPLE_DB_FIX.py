import sqlite3
import os
from datetime import datetime

print("🔧 إصلاح بسيط لقاعدة البيانات")
print("=" * 40)

# حذف قواعد البيانات القديمة
db_files = ['auction.db', 'working_database.db']
for db_file in db_files:
    if os.path.exists(db_file):
        os.remove(db_file)
        print(f"🗑️ حذف: {db_file}")

# إنشاء قاعدة بيانات جديدة
conn = sqlite3.connect('auction.db')
cursor = conn.cursor()

print("📋 إنشاء الجداول...")

# جدول المستخدمين
cursor.execute('''
    CREATE TABLE user (
        id INTEGER PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        email TEXT,
        password_hash TEXT NOT NULL,
        name TEXT,
        role TEXT DEFAULT 'user',
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
''')

# جدول العملاء مع جميع الحقول
cursor.execute('''
    CREATE TABLE customer (
        id INTEGER PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_en TEXT,
        phone TEXT NOT NULL,
        email TEXT,
        id_number TEXT,
        nationality TEXT DEFAULT 'قطري',
        birth_date DATE,
        address TEXT,
        city TEXT DEFAULT 'الدوحة',
        postal_code TEXT,
        profession TEXT,
        company TEXT,
        monthly_income REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        notes TEXT
    )
''')

# جدول السيارات
cursor.execute('''
    CREATE TABLE car (
        id INTEGER PRIMARY KEY,
        make TEXT NOT NULL,
        model TEXT NOT NULL,
        year INTEGER NOT NULL,
        price REAL NOT NULL,
        color TEXT,
        fuel_type TEXT,
        transmission TEXT,
        engine_size TEXT,
        mileage INTEGER,
        body_type TEXT,
        doors INTEGER,
        seats INTEGER,
        vin_number TEXT,
        license_plate TEXT,
        insurance_expiry DATE,
        registration_expiry DATE,
        condition TEXT,
        status TEXT DEFAULT 'available',
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
''')

# جدول الأرقام المميزة
cursor.execute('''
    CREATE TABLE premium_number (
        id INTEGER PRIMARY KEY,
        number TEXT UNIQUE NOT NULL,
        category TEXT,
        price REAL,
        status TEXT DEFAULT 'available',
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
''')

# جدول العقود
cursor.execute('''
    CREATE TABLE contract (
        id INTEGER PRIMARY KEY,
        customer_id INTEGER NOT NULL,
        car_id INTEGER,
        premium_number_id INTEGER,
        contract_type TEXT DEFAULT 'car_sale',
        payment_method TEXT DEFAULT 'cash',
        total_amount REAL NOT NULL,
        down_payment REAL DEFAULT 0,
        monthly_payment REAL DEFAULT 0,
        installment_months INTEGER DEFAULT 0,
        interest_rate REAL DEFAULT 0,
        remaining_amount REAL DEFAULT 0,
        contract_date DATE DEFAULT CURRENT_DATE,
        delivery_date DATE,
        warranty_period INTEGER DEFAULT 12,
        status TEXT DEFAULT 'active',
        description TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customer (id)
    )
''')

# جدول المزادات
cursor.execute('''
    CREATE TABLE auction (
        id INTEGER PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        premium_number_id INTEGER,
        car_id INTEGER,
        starting_price REAL NOT NULL,
        current_price REAL,
        reserve_price REAL,
        bid_increment REAL DEFAULT 1000,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        status TEXT DEFAULT 'pending',
        auto_extend BOOLEAN DEFAULT 0,
        extend_time INTEGER DEFAULT 300,
        commission_rate REAL DEFAULT 5.0,
        commission_amount REAL DEFAULT 0,
        views INTEGER DEFAULT 0,
        total_bids INTEGER DEFAULT 0,
        winner TEXT,
        winner_id INTEGER,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (premium_number_id) REFERENCES premium_number (id)
    )
''')

# جدول المزايدات
cursor.execute('''
    CREATE TABLE bid (
        id INTEGER PRIMARY KEY,
        auction_id INTEGER NOT NULL,
        customer_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        bid_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_winning BOOLEAN DEFAULT 0,
        FOREIGN KEY (auction_id) REFERENCES auction (id),
        FOREIGN KEY (customer_id) REFERENCES customer (id)
    )
''')

# جدول الأقساط
cursor.execute('''
    CREATE TABLE installment (
        id INTEGER PRIMARY KEY,
        contract_id INTEGER NOT NULL,
        installment_number INTEGER NOT NULL,
        due_date DATE NOT NULL,
        amount REAL NOT NULL,
        paid_amount REAL DEFAULT 0,
        payment_date DATE,
        status TEXT DEFAULT 'pending',
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contract_id) REFERENCES contract (id)
    )
''')

print("✅ تم إنشاء جميع الجداول")

print("📝 إدراج البيانات التجريبية...")

# إدراج مستخدم admin
cursor.execute('''
    INSERT INTO user (username, email, password_hash, name, role)
    VALUES ('admin', '<EMAIL>', 'admin', 'مدير النظام', 'admin')
''')

# إدراج عملاء تجريبيين
customers = [
    ('أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '50123456', '<EMAIL>', '12345678901'),
    ('فاطمة علي الأنصاري', 'Fatima Ali Al-Ansari', '50123457', '<EMAIL>', '12345678902'),
    ('محمد سالم المري', 'Mohammed Salem Al-Marri', '50123458', '<EMAIL>', '12345678903'),
    ('نورا خالد الثاني', 'Nora Khalid Al-Thani', '50123459', '<EMAIL>', '12345678904'),
    ('عبدالله أحمد الكواري', 'Abdullah Ahmed Al-Kuwari', '50123460', '<EMAIL>', '12345678905')
]

for name_ar, name_en, phone, email, id_number in customers:
    cursor.execute('''
        INSERT INTO customer (name_ar, name_en, phone, email, id_number, nationality, city)
        VALUES (?, ?, ?, ?, ?, 'قطري', 'الدوحة')
    ''', (name_ar, name_en, phone, email, id_number))

# إدراج أرقام مميزة
premium_numbers = [
    ('777', 'VIP', 300000, 'auction'),
    ('999', 'VIP', 450000, 'auction'),
    ('555', 'Premium', 180000, 'auction'),
    ('123', 'Standard', 50000, 'available'),
    ('456', 'Standard', 75000, 'available')
]

for number, category, price, status in premium_numbers:
    cursor.execute('''
        INSERT INTO premium_number (number, category, price, status)
        VALUES (?, ?, ?, ?)
    ''', (number, category, price, status))

# إدراج سيارات
cars = [
    ('تويوتا', 'كامري', 2023, 150000, 'أبيض', 'بنزين', 'أوتوماتيك'),
    ('نيسان', 'التيما', 2023, 140000, 'أسود', 'بنزين', 'أوتوماتيك'),
    ('هوندا', 'أكورد', 2023, 160000, 'فضي', 'بنزين', 'أوتوماتيك')
]

for make, model, year, price, color, fuel_type, transmission in cars:
    cursor.execute('''
        INSERT INTO car (make, model, year, price, color, fuel_type, transmission)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (make, model, year, price, color, fuel_type, transmission))

# إدراج عقود تجريبية
contracts = [
    (1, 1, None, 'car_sale', 'cash', 150000, 'بيع سيارة تويوتا كامري'),
    (2, None, 1, 'premium_number', 'installment', 300000, 'بيع رقم مميز 777'),
    (3, 2, None, 'car_sale', 'installment', 140000, 'بيع سيارة نيسان التيما')
]

for customer_id, car_id, premium_number_id, contract_type, payment_method, total_amount, description in contracts:
    cursor.execute('''
        INSERT INTO contract (customer_id, car_id, premium_number_id, contract_type, payment_method, total_amount, description)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (customer_id, car_id, premium_number_id, contract_type, payment_method, total_amount, description))

# إدراج مزادات نشطة
now = datetime.now()
start_time = now.strftime('%Y-%m-%d %H:%M:%S')
end_time = (now.replace(hour=23, minute=59)).strftime('%Y-%m-%d %H:%M:%S')

auctions = [
    ('مزاد الرقم المميز 777', 'مزاد للرقم المميز 777', 1, 250000, 303000, start_time, end_time, 'active'),
    ('مزاد الرقم المميز 999', 'مزاد للرقم المميز 999', 2, 400000, 450000, start_time, end_time, 'active'),
    ('مزاد الرقم المميز 555', 'مزاد للرقم المميز 555', 3, 150000, 180000, start_time, end_time, 'active')
]

for title, description, premium_number_id, starting_price, current_price, start_time, end_time, status in auctions:
    cursor.execute('''
        INSERT INTO auction (title, description, premium_number_id, starting_price, current_price, start_time, end_time, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (title, description, premium_number_id, starting_price, current_price, start_time, end_time, status))

conn.commit()

# نسخ قاعدة البيانات
import shutil
shutil.copy2('auction.db', 'working_database.db')

# إنشاء مجلد instance ونسخ قاعدة البيانات
if not os.path.exists('instance'):
    os.makedirs('instance')
shutil.copy2('auction.db', 'instance/auction.db')

conn.close()

print("✅ تم إنشاء قاعدة البيانات بنجاح!")
print("📊 البيانات المنشأة:")
print("   👤 1 مستخدم admin")
print("   👥 5 عملاء")
print("   🔢 5 أرقام مميزة")
print("   🚗 3 سيارات")
print("   📋 3 عقود")
print("   🎯 3 مزادات نشطة")
print("=" * 40)
print("🚀 يمكنك الآن تشغيل النظام:")
print("   python working_app.py")
print("=" * 40)
