#!/usr/bin/env python3
"""
Fix Database Schema - Qatar Car Showroom
This script fixes the database schema mismatch issue
"""

import sqlite3
import os
from datetime import datetime

def fix_database_schema():
    """Fix the database schema by adding missing columns"""
    db_path = 'working_database.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Fixing database schema...")
        
        # Check current user table structure
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 Current columns: {columns}")
        
        # Add missing columns to user table
        missing_columns = [
            ('full_name', 'TEXT'),
            ('phone', 'TEXT'),
            ('avatar', 'TEXT'),
            ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
            ('last_login', 'DATETIME')
        ]
        
        for column_name, column_type in missing_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE user ADD COLUMN {column_name} {column_type}")
                    print(f"✅ Added column: {column_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e):
                        print(f"⚠️ Warning adding {column_name}: {e}")
        
        # Check if admin user exists
        cursor.execute("SELECT * FROM user WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if not admin_user:
            # Create admin user with basic fields
            from werkzeug.security import generate_password_hash
            password_hash = generate_password_hash('admin123')
            
            cursor.execute("""
                INSERT INTO user (username, password_hash, role, is_active, email, created_at) 
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('admin', password_hash, 'admin', 1, '<EMAIL>', datetime.now()))
            print("✅ Created admin user")
        else:
            print("✅ Admin user already exists")
        
        conn.commit()
        conn.close()
        
        print("🎉 Database schema fixed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        return False

def backup_database():
    """Create a backup of the current database"""
    db_path = 'working_database.db'
    if os.path.exists(db_path):
        backup_path = f'working_database_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"💾 Database backed up to: {backup_path}")
        return backup_path
    return None

if __name__ == '__main__':
    print("🚀 Qatar Car Showroom - Database Schema Fix")
    print("=" * 50)
    
    # Create backup first
    backup_path = backup_database()
    
    # Fix the schema
    if fix_database_schema():
        print("✅ Database is now ready!")
        print("🌐 You can now run the application with: python app.py")
    else:
        print("❌ Failed to fix database")
        if backup_path:
            print(f"💾 Backup available at: {backup_path}")
