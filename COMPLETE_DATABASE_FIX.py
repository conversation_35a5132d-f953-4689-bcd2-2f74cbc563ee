#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
import sys
from datetime import datetime, timedelta
import shutil

def backup_existing_databases():
    """نسخ احتياطي من قواعد البيانات الموجودة"""
    print("💾 إنشاء نسخ احتياطية...")
    
    db_files = ['auction.db', 'working_database.db', 'instance/auction.db']
    backup_folder = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if not os.path.exists(backup_folder):
        os.makedirs(backup_folder)
    
    for db_file in db_files:
        if os.path.exists(db_file):
            backup_path = os.path.join(backup_folder, os.path.basename(db_file))
            shutil.copy2(db_file, backup_path)
            print(f"✅ نسخ احتياطي: {db_file} -> {backup_path}")

def create_complete_database():
    """إنشاء قاعدة بيانات كاملة من الصفر"""
    print("🏗️ إنشاء قاعدة بيانات جديدة...")
    
    # حذف قواعد البيانات القديمة
    db_files = ['auction.db', 'working_database.db']
    for db_file in db_files:
        if os.path.exists(db_file):
            os.remove(db_file)
            print(f"🗑️ تم حذف: {db_file}")
    
    # إنشاء مجلد instance إذا لم يكن موجوداً
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    # إنشاء قاعدة البيانات الرئيسية
    conn = sqlite3.connect('auction.db')
    cursor = conn.cursor()
    
    print("📋 إنشاء جدول المستخدمين...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(80) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            name VARCHAR(100),
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("👥 إنشاء جدول العملاء...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customer (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_ar VARCHAR(100) NOT NULL,
            name_en VARCHAR(100),
            phone VARCHAR(20) NOT NULL,
            email VARCHAR(100),
            id_number VARCHAR(20),
            nationality VARCHAR(50) DEFAULT 'قطري',
            birth_date DATE,
            address TEXT,
            city VARCHAR(50) DEFAULT 'الدوحة',
            postal_code VARCHAR(10),
            profession VARCHAR(100),
            company VARCHAR(100),
            monthly_income REAL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    print("🚗 إنشاء جدول السيارات...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS car (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            make VARCHAR(50) NOT NULL,
            model VARCHAR(50) NOT NULL,
            year INTEGER NOT NULL,
            price REAL NOT NULL,
            color VARCHAR(30),
            fuel_type VARCHAR(20),
            transmission VARCHAR(20),
            engine_size VARCHAR(20),
            mileage INTEGER,
            body_type VARCHAR(30),
            doors INTEGER,
            seats INTEGER,
            vin_number VARCHAR(50),
            license_plate VARCHAR(20),
            insurance_expiry DATE,
            registration_expiry DATE,
            condition VARCHAR(20),
            status VARCHAR(20) DEFAULT 'available',
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("🔢 إنشاء جدول الأرقام المميزة...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS premium_number (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            number VARCHAR(20) UNIQUE NOT NULL,
            category VARCHAR(50),
            price REAL,
            status VARCHAR(20) DEFAULT 'available',
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("📋 إنشاء جدول العقود...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS contract (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER NOT NULL,
            car_id INTEGER,
            premium_number_id INTEGER,
            contract_type VARCHAR(50) DEFAULT 'car_sale',
            payment_method VARCHAR(50) DEFAULT 'cash',
            total_amount REAL NOT NULL,
            down_payment REAL DEFAULT 0,
            monthly_payment REAL DEFAULT 0,
            installment_months INTEGER DEFAULT 0,
            interest_rate REAL DEFAULT 0,
            remaining_amount REAL DEFAULT 0,
            contract_date DATE DEFAULT CURRENT_DATE,
            delivery_date DATE,
            warranty_period INTEGER DEFAULT 12,
            status VARCHAR(20) DEFAULT 'active',
            description TEXT,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customer (id),
            FOREIGN KEY (car_id) REFERENCES car (id),
            FOREIGN KEY (premium_number_id) REFERENCES premium_number (id)
        )
    ''')
    
    print("🎯 إنشاء جدول المزادات...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS auction (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            premium_number_id INTEGER,
            car_id INTEGER,
            starting_price REAL NOT NULL,
            current_price REAL,
            reserve_price REAL,
            bid_increment REAL DEFAULT 1000,
            start_time DATETIME NOT NULL,
            end_time DATETIME NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            auto_extend BOOLEAN DEFAULT 0,
            extend_time INTEGER DEFAULT 300,
            commission_rate REAL DEFAULT 5.0,
            commission_amount REAL DEFAULT 0,
            views INTEGER DEFAULT 0,
            total_bids INTEGER DEFAULT 0,
            winner VARCHAR(100),
            winner_id INTEGER,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
            FOREIGN KEY (car_id) REFERENCES car (id),
            FOREIGN KEY (winner_id) REFERENCES customer (id),
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
    ''')
    
    print("💰 إنشاء جدول المزايدات...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS bid (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            auction_id INTEGER NOT NULL,
            customer_id INTEGER NOT NULL,
            amount REAL NOT NULL,
            bid_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_winning BOOLEAN DEFAULT 0,
            FOREIGN KEY (auction_id) REFERENCES auction (id),
            FOREIGN KEY (customer_id) REFERENCES customer (id)
        )
    ''')
    
    print("💳 إنشاء جدول الأقساط...")
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS installment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contract_id INTEGER NOT NULL,
            installment_number INTEGER NOT NULL,
            due_date DATE NOT NULL,
            amount REAL NOT NULL,
            paid_amount REAL DEFAULT 0,
            payment_date DATE,
            status VARCHAR(20) DEFAULT 'pending',
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contract_id) REFERENCES contract (id)
        )
    ''')
    
    conn.commit()
    print("✅ تم إنشاء جميع الجداول بنجاح")
    
    return conn, cursor

def insert_sample_data(conn, cursor):
    """إدراج بيانات تجريبية شاملة"""
    print("📝 إدراج البيانات التجريبية...")
    
    # إدراج مستخدم admin
    print("👤 إدراج مستخدم admin...")
    cursor.execute('''
        INSERT OR REPLACE INTO user (id, username, email, password_hash, name, role, is_active)
        VALUES (1, 'admin', '<EMAIL>', 'pbkdf2:sha256:260000$salt$hash', 'مدير النظام', 'admin', 1)
    ''')
    
    # إدراج عملاء تجريبيين
    print("👥 إدراج العملاء...")
    customers_data = [
        (1, 'أحمد محمد الكعبي', 'Ahmed Mohammed Al-Kaabi', '50123456', '<EMAIL>', '12345678901', 'قطري', '1985-05-15', 'الدوحة، منطقة الخليج الغربي', 'الدوحة', '12345', 'مهندس', 'شركة قطر للبترول', 25000.0, 'عميل مميز'),
        (2, 'فاطمة علي الأنصاري', 'Fatima Ali Al-Ansari', '50123457', '<EMAIL>', '12345678902', 'قطري', '1990-08-22', 'الدوحة، منطقة الدفنة', 'الدوحة', '12346', 'طبيبة', 'مستشفى حمد', 30000.0, 'عميلة مميزة'),
        (3, 'محمد سالم المري', 'Mohammed Salem Al-Marri', '50123458', '<EMAIL>', '12345678903', 'قطري', '1988-12-10', 'الدوحة، منطقة الريان', 'الدوحة', '12347', 'محاسب', 'وزارة المالية', 20000.0, 'عميل منتظم'),
        (4, 'نورا خالد الثاني', 'Nora Khalid Al-Thani', '50123459', '<EMAIL>', '12345678904', 'قطري', '1992-03-18', 'الدوحة، منطقة الوكرة', 'الدوحة', '12348', 'مدرسة', 'وزارة التعليم', 18000.0, 'عميلة جديدة'),
        (5, 'عبدالله أحمد الكواري', 'Abdullah Ahmed Al-Kuwari', '50123460', '<EMAIL>', '12345678905', 'قطري', '1983-11-25', 'الدوحة، منطقة الثمامة', 'الدوحة', '12349', 'مدير', 'شركة خاصة', 35000.0, 'عميل VIP')
    ]
    
    for customer in customers_data:
        cursor.execute('''
            INSERT OR REPLACE INTO customer 
            (id, name_ar, name_en, phone, email, id_number, nationality, birth_date, address, city, postal_code, profession, company, monthly_income, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', customer)
    
    # إدراج أرقام مميزة
    print("🔢 إدراج الأرقام المميزة...")
    premium_numbers_data = [
        (1, '777', 'VIP', 300000, 'auction', 'رقم مميز من فئة VIP'),
        (2, '999', 'VIP', 450000, 'auction', 'رقم مميز من فئة VIP'),
        (3, '555', 'Premium', 180000, 'auction', 'رقم مميز من فئة Premium'),
        (4, '123', 'Standard', 50000, 'available', 'رقم مميز من فئة Standard'),
        (5, '456', 'Standard', 75000, 'available', 'رقم مميز من فئة Standard'),
        (6, '888', 'VIP', 400000, 'available', 'رقم مميز من فئة VIP'),
        (7, '111', 'Premium', 200000, 'available', 'رقم مميز من فئة Premium')
    ]
    
    for pn in premium_numbers_data:
        cursor.execute('''
            INSERT OR REPLACE INTO premium_number (id, number, category, price, status, description)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', pn)
    
    # إدراج سيارات
    print("🚗 إدراج السيارات...")
    cars_data = [
        (1, 'تويوتا', 'كامري', 2023, 150000, 'أبيض', 'بنزين', 'أوتوماتيك', '2.5L', 5000, 'سيدان', 4, 5, 'TOY123456789', 'ABC123', '2024-12-31', '2024-12-31', 'ممتاز', 'available', 'سيارة تويوتا كامري 2023 بحالة ممتازة'),
        (2, 'نيسان', 'التيما', 2023, 140000, 'أسود', 'بنزين', 'أوتوماتيك', '2.0L', 3000, 'سيدان', 4, 5, 'NIS123456789', 'DEF456', '2024-12-31', '2024-12-31', 'ممتاز', 'available', 'سيارة نيسان التيما 2023 بحالة ممتازة'),
        (3, 'هوندا', 'أكورد', 2023, 160000, 'فضي', 'بنزين', 'أوتوماتيك', '2.4L', 2000, 'سيدان', 4, 5, 'HON123456789', 'GHI789', '2024-12-31', '2024-12-31', 'جديد', 'available', 'سيارة هوندا أكورد 2023 جديدة'),
        (4, 'لكزس', 'ES350', 2023, 200000, 'أبيض لؤلؤي', 'بنزين', 'أوتوماتيك', '3.5L', 1000, 'سيدان', 4, 5, 'LEX123456789', 'JKL012', '2024-12-31', '2024-12-31', 'جديد', 'available', 'سيارة لكزس ES350 2023 فاخرة'),
        (5, 'مرسيدس', 'C-Class', 2023, 250000, 'أسود', 'بنزين', 'أوتوماتيك', '2.0L', 500, 'سيدان', 4, 5, 'MER123456789', 'MNO345', '2024-12-31', '2024-12-31', 'جديد', 'available', 'سيارة مرسيدس C-Class 2023 فاخرة')
    ]
    
    for car in cars_data:
        cursor.execute('''
            INSERT OR REPLACE INTO car 
            (id, make, model, year, price, color, fuel_type, transmission, engine_size, mileage, body_type, doors, seats, vin_number, license_plate, insurance_expiry, registration_expiry, condition, status, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', car)
    
    conn.commit()
    print("✅ تم إدراج جميع البيانات التجريبية")

def create_active_auctions(conn, cursor):
    """إنشاء مزادات نشطة"""
    print("🎯 إنشاء مزادات نشطة...")
    
    current_time = datetime.now()
    
    # مزاد الرقم 777
    start_time_777 = current_time - timedelta(minutes=30)
    end_time_777 = current_time + timedelta(minutes=30)
    
    cursor.execute('''
        INSERT OR REPLACE INTO auction 
        (id, title, description, premium_number_id, starting_price, current_price, reserve_price, bid_increment, start_time, end_time, status, auto_extend, commission_rate, total_bids, views)
        VALUES (1, 'مزاد الرقم المميز 777', 'مزاد للرقم المميز 777 من فئة VIP', 1, 250000, 303000, 280000, 5000, ?, ?, 'active', 1, 5.0, 15, 45)
    ''', (start_time_777.isoformat(), end_time_777.isoformat()))
    
    # مزاد الرقم 999
    start_time_999 = current_time - timedelta(minutes=20)
    end_time_999 = current_time + timedelta(minutes=40)
    
    cursor.execute('''
        INSERT OR REPLACE INTO auction 
        (id, title, description, premium_number_id, starting_price, current_price, reserve_price, bid_increment, start_time, end_time, status, auto_extend, commission_rate, total_bids, views)
        VALUES (2, 'مزاد الرقم المميز 999', 'مزاد للرقم المميز 999 من فئة VIP', 2, 400000, 450000, 420000, 10000, ?, ?, 'active', 1, 5.0, 22, 67)
    ''', (start_time_999.isoformat(), end_time_999.isoformat()))
    
    # مزاد الرقم 555
    start_time_555 = current_time - timedelta(minutes=45)
    end_time_555 = current_time + timedelta(minutes=15)
    
    cursor.execute('''
        INSERT OR REPLACE INTO auction 
        (id, title, description, premium_number_id, starting_price, current_price, reserve_price, bid_increment, start_time, end_time, status, auto_extend, commission_rate, total_bids, views)
        VALUES (3, 'مزاد الرقم المميز 555', 'مزاد للرقم المميز 555 من فئة Premium', 3, 150000, 180000, 160000, 2000, ?, ?, 'active', 1, 5.0, 18, 38)
    ''', (start_time_555.isoformat(), end_time_555.isoformat()))
    
    conn.commit()
    print("✅ تم إنشاء 3 مزادات نشطة")

def create_sample_bids(conn, cursor):
    """إنشاء مزايدات تجريبية"""
    print("💰 إنشاء مزايدات تجريبية...")
    
    current_time = datetime.now()
    
    # مزايدات للمزاد الأول (777)
    bids_777 = [
        (1, 1, 1, 250000, current_time - timedelta(minutes=25)),
        (2, 1, 2, 260000, current_time - timedelta(minutes=20)),
        (3, 1, 3, 275000, current_time - timedelta(minutes=15)),
        (4, 1, 1, 290000, current_time - timedelta(minutes=10)),
        (5, 1, 2, 303000, current_time - timedelta(minutes=5))
    ]
    
    for bid in bids_777:
        cursor.execute('''
            INSERT OR REPLACE INTO bid (id, auction_id, customer_id, amount, bid_time)
            VALUES (?, ?, ?, ?, ?)
        ''', bid)
    
    # مزايدات للمزاد الثاني (999)
    bids_999 = [
        (6, 2, 2, 400000, current_time - timedelta(minutes=18)),
        (7, 2, 4, 420000, current_time - timedelta(minutes=12)),
        (8, 2, 5, 440000, current_time - timedelta(minutes=8)),
        (9, 2, 2, 450000, current_time - timedelta(minutes=3))
    ]
    
    for bid in bids_999:
        cursor.execute('''
            INSERT OR REPLACE INTO bid (id, auction_id, customer_id, amount, bid_time)
            VALUES (?, ?, ?, ?, ?)
        ''', bid)
    
    # مزايدات للمزاد الثالث (555)
    bids_555 = [
        (10, 3, 3, 150000, current_time - timedelta(minutes=40)),
        (11, 3, 1, 165000, current_time - timedelta(minutes=30)),
        (12, 3, 4, 175000, current_time - timedelta(minutes=20)),
        (13, 3, 3, 180000, current_time - timedelta(minutes=10))
    ]
    
    for bid in bids_555:
        cursor.execute('''
            INSERT OR REPLACE INTO bid (id, auction_id, customer_id, amount, bid_time)
            VALUES (?, ?, ?, ?, ?)
        ''', bid)
    
    conn.commit()
    print("✅ تم إنشاء مزايدات تجريبية")

def main():
    """الدالة الرئيسية"""
    print("🚀 إصلاح شامل لقاعدة البيانات")
    print("=" * 60)
    
    try:
        # نسخ احتياطي
        backup_existing_databases()
        
        # إنشاء قاعدة البيانات الجديدة
        conn, cursor = create_complete_database()
        
        # إدراج البيانات التجريبية
        insert_sample_data(conn, cursor)
        
        # إنشاء مزادات نشطة
        create_active_auctions(conn, cursor)
        
        # إنشاء مزايدات تجريبية
        create_sample_bids(conn, cursor)
        
        # إغلاق الاتصال
        conn.close()
        
        # نسخ قاعدة البيانات إلى المواقع الأخرى
        print("📋 نسخ قاعدة البيانات...")
        if os.path.exists('auction.db'):
            shutil.copy2('auction.db', 'working_database.db')
            shutil.copy2('auction.db', 'instance/auction.db')
            print("✅ تم نسخ قاعدة البيانات إلى جميع المواقع")
        
        print("=" * 60)
        print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
        print("✅ الجداول المنشأة:")
        print("   📋 user - المستخدمين")
        print("   👥 customer - العملاء (مع جميع الحقول)")
        print("   🚗 car - السيارات")
        print("   🔢 premium_number - الأرقام المميزة")
        print("   📋 contract - العقود")
        print("   🎯 auction - المزادات")
        print("   💰 bid - المزايدات")
        print("   💳 installment - الأقساط")
        print("✅ البيانات التجريبية:")
        print("   👤 1 مستخدم admin")
        print("   👥 5 عملاء")
        print("   🔢 7 أرقام مميزة")
        print("   🚗 5 سيارات")
        print("   🎯 3 مزادات نشطة")
        print("   💰 13 مزايدة")
        print("=" * 60)
        print("🚀 يمكنك الآن تشغيل النظام:")
        print("   python working_app.py")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
