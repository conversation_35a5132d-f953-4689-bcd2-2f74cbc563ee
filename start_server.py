#!/usr/bin/env python3
"""
Simple server starter for Qatar Car Showroom
"""

if __name__ == '__main__':
    try:
        print("🚀 بدء تشغيل نظام معرض قطر للسيارات...")
        print("🔢 مع دعم الأرقام المميزة في العقود")
        print("=" * 50)
        
        from app import create_app, db
        from app.models.user import User
        from werkzeug.security import generate_password_hash
        
        app = create_app()
        
        with app.app_context():
            db.create_all()
            
            # Create admin user
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role='admin',
                    is_active=True
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء مستخدم admin")
            else:
                print("✅ مستخدم admin موجود")
        
        print("🌐 الخادم يعمل على: http://127.0.0.1:1414")
        print("🔐 اسم المستخدم: admin | كلمة المرور: admin123")
        print("✨ الميزات الجديدة: بيع الأرقام المميزة في العقود")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 50)
        
        app.run(host='127.0.0.1', port=1414, debug=False)
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
