{% extends "base.html" %}

{% block title %}تصدير البيانات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-download me-2"></i>تصدير البيانات</h2>
                <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة للإعدادات
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Export Options -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-file-export me-2"></i>خيارات التصدير</h5>
                        </div>
                        <div class="card-body">
                            <form id="exportForm">
                                <div class="row">
                                    <!-- Data Type Selection -->
                                    <div class="col-md-6 mb-4">
                                        <h6>نوع البيانات</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="export_customers" name="data_types" value="customers" checked>
                                            <label class="form-check-label" for="export_customers">
                                                <i class="fas fa-user-friends me-1"></i>العملاء
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="export_cars" name="data_types" value="cars" checked>
                                            <label class="form-check-label" for="export_cars">
                                                <i class="fas fa-car me-1"></i>السيارات
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="export_contracts" name="data_types" value="contracts" checked>
                                            <label class="form-check-label" for="export_contracts">
                                                <i class="fas fa-file-contract me-1"></i>العقود
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="export_premium_numbers" name="data_types" value="premium_numbers" checked>
                                            <label class="form-check-label" for="export_premium_numbers">
                                                <i class="fas fa-hashtag me-1"></i>الأرقام المميزة
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="export_maintenance" name="data_types" value="maintenance" checked>
                                            <label class="form-check-label" for="export_maintenance">
                                                <i class="fas fa-tools me-1"></i>سجلات الصيانة
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Export Format -->
                                    <div class="col-md-6 mb-4">
                                        <h6>تنسيق التصدير</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" id="format_excel" name="export_format" value="excel" checked>
                                            <label class="form-check-label" for="format_excel">
                                                <i class="fas fa-file-excel me-1 text-success"></i>Excel (.xlsx)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" id="format_csv" name="export_format" value="csv">
                                            <label class="form-check-label" for="format_csv">
                                                <i class="fas fa-file-csv me-1 text-info"></i>CSV (.csv)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" id="format_pdf" name="export_format" value="pdf">
                                            <label class="form-check-label" for="format_pdf">
                                                <i class="fas fa-file-pdf me-1 text-danger"></i>PDF (.pdf)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" id="format_json" name="export_format" value="json">
                                            <label class="form-check-label" for="format_json">
                                                <i class="fas fa-file-code me-1 text-warning"></i>JSON (.json)
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Date Range -->
                                    <div class="col-12 mb-4">
                                        <h6>نطاق التاريخ (اختياري)</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label for="date_from" class="form-label">من تاريخ</label>
                                                <input type="date" class="form-control" id="date_from" name="date_from">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                                <input type="date" class="form-control" id="date_to" name="date_to">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Additional Options -->
                                    <div class="col-12 mb-4">
                                        <h6>خيارات إضافية</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="include_images" name="include_images">
                                            <label class="form-check-label" for="include_images">
                                                تضمين الصور (للسيارات والعملاء)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="include_deleted" name="include_deleted">
                                            <label class="form-check-label" for="include_deleted">
                                                تضمين البيانات المحذوفة
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="compress_file" name="compress_file" checked>
                                            <label class="form-check-label" for="compress_file">
                                                ضغط الملف (ZIP)
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-download me-1"></i>تصدير البيانات
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="selectAllBtn">
                                        <i class="fas fa-check-square me-1"></i>تحديد الكل
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Export History -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-history me-2"></i>سجل التصدير</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>نوع البيانات</th>
                                            <th>التنسيق</th>
                                            <th>الحجم</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="exportHistory">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">لا توجد عمليات تصدير سابقة</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Export Preview -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة التصدير</h6>
                        </div>
                        <div class="card-body">
                            <div id="exportPreview">
                                <p class="text-muted">اختر البيانات والتنسيق لرؤية المعاينة</p>
                            </div>
                        </div>
                    </div>

                    <!-- Export Templates -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-templates me-2"></i>قوالب جاهزة</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm template-btn" 
                                        data-template="complete">
                                    <i class="fas fa-database me-1"></i>تصدير كامل
                                </button>
                                <button class="btn btn-outline-success btn-sm template-btn" 
                                        data-template="customers_only">
                                    <i class="fas fa-users me-1"></i>العملاء فقط
                                </button>
                                <button class="btn btn-outline-info btn-sm template-btn" 
                                        data-template="cars_only">
                                    <i class="fas fa-car me-1"></i>السيارات فقط
                                </button>
                                <button class="btn btn-outline-warning btn-sm template-btn" 
                                        data-template="financial">
                                    <i class="fas fa-money-bill me-1"></i>البيانات المالية
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tips -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>نصائح</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Excel مناسب للتحليل والتقارير
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    CSV مناسب لاستيراد البيانات
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    PDF مناسب للطباعة والأرشفة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    JSON مناسب للتطبيقات البرمجية
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    استخدم الضغط للملفات الكبيرة
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Progress Modal -->
<div class="modal fade" id="exportProgressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">جاري تصدير البيانات</h5>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p id="exportStatus">جاري تحضير البيانات...</p>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="exportProgress"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('exportForm');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const checkboxes = document.querySelectorAll('input[name="data_types"]');
    
    // Select all functionality
    selectAllBtn.addEventListener('click', function() {
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        checkboxes.forEach(cb => cb.checked = !allChecked);
        updatePreview();
        this.innerHTML = allChecked ? 
            '<i class="fas fa-check-square me-1"></i>تحديد الكل' : 
            '<i class="fas fa-square me-1"></i>إلغاء التحديد';
    });

    // Template buttons
    document.querySelectorAll('.template-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const template = this.dataset.template;
            applyTemplate(template);
        });
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        startExport();
    });

    // Update preview when options change
    form.addEventListener('change', updatePreview);

    function applyTemplate(template) {
        // Reset all checkboxes
        checkboxes.forEach(cb => cb.checked = false);
        
        switch(template) {
            case 'complete':
                checkboxes.forEach(cb => cb.checked = true);
                document.getElementById('format_excel').checked = true;
                break;
            case 'customers_only':
                document.getElementById('export_customers').checked = true;
                document.getElementById('format_excel').checked = true;
                break;
            case 'cars_only':
                document.getElementById('export_cars').checked = true;
                document.getElementById('format_excel').checked = true;
                break;
            case 'financial':
                document.getElementById('export_contracts').checked = true;
                document.getElementById('export_premium_numbers').checked = true;
                document.getElementById('format_pdf').checked = true;
                break;
        }
        updatePreview();
    }

    function updatePreview() {
        const selectedData = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.nextElementSibling.textContent.trim());
        
        const format = document.querySelector('input[name="export_format"]:checked').value;
        const dateFrom = document.getElementById('date_from').value;
        const dateTo = document.getElementById('date_to').value;
        
        let preview = '<h6>سيتم تصدير:</h6><ul>';
        selectedData.forEach(data => {
            preview += `<li>${data}</li>`;
        });
        preview += '</ul>';
        
        preview += `<p><strong>التنسيق:</strong> ${format.toUpperCase()}</p>`;
        
        if (dateFrom || dateTo) {
            preview += `<p><strong>النطاق الزمني:</strong> ${dateFrom || 'البداية'} - ${dateTo || 'النهاية'}</p>`;
        }
        
        document.getElementById('exportPreview').innerHTML = preview;
    }

    function startExport() {
        const modal = new bootstrap.Modal(document.getElementById('exportProgressModal'));
        modal.show();
        
        // Simulate export process
        let progress = 0;
        const progressBar = document.getElementById('exportProgress');
        const status = document.getElementById('exportStatus');
        
        const steps = [
            'جاري تحضير البيانات...',
            'جاري استخراج البيانات...',
            'جاري تنسيق البيانات...',
            'جاري إنشاء الملف...',
            'جاري ضغط الملف...',
            'تم الانتهاء!'
        ];
        
        const interval = setInterval(() => {
            progress += 20;
            progressBar.style.width = progress + '%';
            
            if (progress <= 100) {
                status.textContent = steps[Math.floor(progress / 20) - 1] || steps[0];
            }
            
            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(() => {
                    modal.hide();
                    addToHistory();
                    // Here you would normally trigger the actual download
                    alert('تم تصدير البيانات بنجاح!');
                }, 1000);
            }
        }, 800);
    }

    function addToHistory() {
        const tbody = document.getElementById('exportHistory');
        const selectedData = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.nextElementSibling.textContent.trim())
            .join(', ');
        
        const format = document.querySelector('input[name="export_format"]:checked').value.toUpperCase();
        const now = new Date().toLocaleString('ar-QA');
        
        if (tbody.children[0].children.length === 1) {
            tbody.innerHTML = '';
        }
        
        const row = tbody.insertRow(0);
        row.innerHTML = `
            <td>${now}</td>
            <td>${selectedData}</td>
            <td><span class="badge bg-primary">${format}</span></td>
            <td>2.5 MB</td>
            <td><span class="badge bg-success">مكتمل</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-download"></i>
                </button>
            </td>
        `;
    }

    // Initialize preview
    updatePreview();
});
</script>
{% endblock %}
