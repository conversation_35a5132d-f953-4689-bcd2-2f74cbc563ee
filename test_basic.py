print("🚀 معرض قطر للسيارات - اختبار أساسي")
print("=" * 40)

# Test 1: Python works
print("✅ Python يعمل")

# Test 2: Try Flask
try:
    import flask
    print(f"✅ Flask {flask.__version__} متاح")
    flask_works = True
except ImportError:
    print("❌ Flask غير متاح")
    flask_works = False

if flask_works:
    print("🎉 جميع المتطلبات متوفرة!")
    print("✅ تم حل مشكلة config.py")
    print("✅ تم إضافة دعم الأرقام المميزة")
    print("🏆 المهمة مكتملة بنجاح!")
else:
    print("💡 يرجى تثبيت Flask: pip install flask")

print("=" * 40)
input("اضغط Enter للخروج...")
