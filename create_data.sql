-- حذف الجداول القديمة
DROP TABLE IF EXISTS bid;
DROP TABLE IF EXISTS auction;
DROP TABLE IF EXISTS customer;
DROP TABLE IF EXISTS premium_number;

-- إن<PERSON>ا<PERSON> الجداول
CREATE TABLE customer (
    id INTEGER PRIMARY KEY,
    name_ar TEXT NOT NULL,
    phone TEXT,
    email TEXT
);

CREATE TABLE premium_number (
    id INTEGER PRIMARY KEY,
    number TEXT NOT NULL,
    category TEXT,
    price REAL,
    status TEXT
);

CREATE TABLE auction (
    id INTEGER PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    premium_number_id INTEGER,
    starting_price REAL,
    current_price REAL,
    status TEXT,
    start_time TEXT,
    end_time TEXT,
    commission_rate REAL,
    total_bids INTEGER
);

CREATE TABLE bid (
    id INTEGER PRIMARY KEY,
    auction_id INTEGER,
    customer_id INTEGER,
    bid_amount REAL,
    bid_time TEXT
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> البيانات
INSERT INTO customer VALUES (1, 'أحمد محمد الكعبي', '+974 5555 1234', '<EMAIL>');
INSERT INTO premium_number VALUES (1, '777777', 'VIP', 75000, 'auction');
INSERT INTO auction VALUES (1, 'مزاد الرقم المميز 777777', 'مزاد تجريبي', 1, 50000, 78850, 'ended', '2024-01-01 10:00:00', '2024-01-01 12:00:00', 5.0, 1);
INSERT INTO bid VALUES (1, 1, 1, 78850, '2024-01-01 11:55:00');
