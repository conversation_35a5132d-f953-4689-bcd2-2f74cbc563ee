#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime, timed<PERSON><PERSON>

def create_winner_data():
    """إنشاء بيانات الفائز في قاعدة البيانات"""
    
    # حذف قاعدة البيانات القديمة
    if os.path.exists('auction.db'):
        os.remove('auction.db')
    
    # إنشاء قاعدة بيانات جديدة
    conn = sqlite3.connect('auction.db')
    cursor = conn.cursor()
    
    try:
        # إنشاء الجداول
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name_ar TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS premium_number (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                number TEXT NOT NULL UNIQUE,
                category TEXT,
                price REAL,
                status TEXT DEFAULT 'available',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auction (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                premium_number_id INTEGER,
                starting_price REAL NOT NULL,
                current_price REAL DEFAULT 0,
                reserve_price REAL,
                bid_increment REAL DEFAULT 1000,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                status TEXT DEFAULT 'pending',
                auto_extend BOOLEAN DEFAULT 0,
                commission_rate REAL DEFAULT 0,
                commission_amount REAL DEFAULT 0,
                total_bids INTEGER DEFAULT 0,
                winner TEXT,
                winner_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (premium_number_id) REFERENCES premium_number (id),
                FOREIGN KEY (winner_id) REFERENCES customer (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bid (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                auction_id INTEGER NOT NULL,
                customer_id INTEGER NOT NULL,
                bid_amount REAL NOT NULL,
                bid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (auction_id) REFERENCES auction (id),
                FOREIGN KEY (customer_id) REFERENCES customer (id)
            )
        ''')
        
        # إنشاء عميل فائز
        cursor.execute('''
            INSERT INTO customer (id, name_ar, phone, email) 
            VALUES (1, 'أحمد محمد الكعبي', '+974 5555 1234', '<EMAIL>')
        ''')
        
        # إنشاء رقم مميز
        cursor.execute('''
            INSERT INTO premium_number (id, number, category, price, status) 
            VALUES (1, '777777', 'VIP', 75000, 'auction')
        ''')
        
        # إنشاء مزاد منتهي
        end_time = datetime.now() - timedelta(hours=1)
        start_time = end_time - timedelta(hours=2)
        
        cursor.execute('''
            INSERT INTO auction (
                id, title, description, premium_number_id, starting_price, 
                current_price, reserve_price, bid_increment, start_time, 
                end_time, status, auto_extend, commission_rate, 
                commission_amount, total_bids, winner, winner_id, created_at
            ) VALUES (1, ?, ?, 1, 50000, 78850, 60000, 1000, ?, ?, 'ended', 0, 5.0, 3942.5, 1, 'أحمد محمد الكعبي', 1, ?)
        ''', (
            'مزاد الرقم المميز 777777',
            'مزاد تجريبي لاختبار عرض الفائز',
            start_time.isoformat(),
            end_time.isoformat(),
            datetime.now().isoformat()
        ))
        
        # إنشاء مزايدة فائزة
        bid_time = end_time - timedelta(minutes=5)
        cursor.execute('''
            INSERT INTO bid (id, auction_id, customer_id, bid_amount, bid_time) 
            VALUES (1, 1, 1, 78850, ?)
        ''', (bid_time.isoformat(),))
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء البيانات بنجاح!")
        print(f"🏆 الفائز: أحمد محمد الكعبي")
        print(f"💰 المبلغ: 78,850 ر.ق")
        print(f"🌐 الرابط: http://127.0.0.1:9898/auction/standalone/1")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    create_winner_data()
