#!/usr/bin/env python3
"""
اختبار عرض اسم الفائز في المزادات المنتهية
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Auction, PremiumNumber, Bid, Customer
from datetime import datetime, timedelta

def create_test_auction_with_winner():
    """إنشاء مزاد تجريبي مع فائز لاختبار العرض"""
    with app.app_context():
        try:
            # البحث عن رقم متاح
            number = PremiumNumber.query.filter_by(status='available').first()
            if not number:
                print("❌ لا توجد أرقام متاحة")
                return False

            # البحث عن عملاء
            customers = Customer.query.limit(3).all()
            if len(customers) < 2:
                print("❌ يجب وجود عميلين على الأقل لإجراء الاختبار")
                return False

            # إنشاء مزاد منتهي
            start_time = datetime.now() - timedelta(minutes=10)  # بدأ قبل 10 دقائق
            end_time = datetime.now() - timedelta(minutes=1)     # انتهى قبل دقيقة

            auction = Auction(
                premium_number_id=number.id,
                title=f"مزاد اختبار للرقم {number.number}",
                description=f"مزاد اختبار لعرض اسم الفائز - الرقم {number.number}",
                starting_price=number.price,
                current_price=number.price + 2000,  # سعر نهائي
                bid_increment=500,
                start_time=start_time,
                end_time=end_time,
                status='ended',  # مزاد منتهي
                commission_rate=5.0,
                auto_extend=False,
                created_by=1,
                total_bids=3
            )

            db.session.add(auction)
            db.session.flush()  # للحصول على auction.id

            # إضافة مزايدات تجريبية
            bids_data = [
                {'customer': customers[0], 'amount': number.price, 'time_offset': 8},
                {'customer': customers[1], 'amount': number.price + 500, 'time_offset': 5},
                {'customer': customers[0], 'amount': number.price + 1000, 'time_offset': 3},
                {'customer': customers[1], 'amount': number.price + 1500, 'time_offset': 2},
                {'customer': customers[0], 'amount': number.price + 2000, 'time_offset': 1}  # الفائز
            ]

            for i, bid_data in enumerate(bids_data):
                bid = Bid(
                    auction_id=auction.id,
                    customer_id=bid_data['customer'].id,
                    bid_amount=bid_data['amount'],
                    bid_time=start_time + timedelta(minutes=bid_data['time_offset'])
                )
                db.session.add(bid)

            # تحديث حالة الرقم
            number.status = 'sold'
            
            # حساب العمولة
            auction.commission_amount = (auction.current_price * auction.commission_rate) / 100
            
            db.session.commit()

            winner = customers[0]  # الفائز هو العميل الأول
            
            print("✅ تم إنشاء مزاد اختبار بنجاح!")
            print(f"   📋 الرقم: {number.number}")
            print(f"   🏆 الفائز: {winner.name_ar}")
            print(f"   💰 المبلغ النهائي: {auction.current_price:,.0f} ر.ق")
            print(f"   📊 عدد المزايدات: {len(bids_data)}")
            print(f"   💵 العمولة: {auction.commission_amount:,.0f} ر.ق")
            print(f"   🌐 رابط المزاد: http://127.0.0.1:9898/auction/details/{auction.id}")
            print(f"   📺 العرض المستقل: http://127.0.0.1:9898/auction/standalone/{auction.id}")
            print(f"   🔧 إدارة المزادات: http://127.0.0.1:9898/auction_management")
            
            return auction.id

        except Exception as e:
            print(f"❌ خطأ في إنشاء المزاد: {e}")
            db.session.rollback()
            return False

def create_multiple_test_auctions():
    """إنشاء عدة مزادات اختبار بحالات مختلفة"""
    with app.app_context():
        print("🧪 إنشاء مزادات اختبار متعددة...")
        
        # مزاد منتهي مع فائز
        auction1 = create_test_auction_with_winner()
        
        # مزاد نشط مع مزايدات
        try:
            number2 = PremiumNumber.query.filter_by(status='available').first()
            customers = Customer.query.limit(2).all()
            
            if number2 and len(customers) >= 2:
                start_time = datetime.now() - timedelta(minutes=5)
                end_time = datetime.now() + timedelta(minutes=5)
                
                auction2 = Auction(
                    premium_number_id=number2.id,
                    title=f"مزاد نشط للرقم {number2.number}",
                    description=f"مزاد نشط لاختبار عرض المتصدر - الرقم {number2.number}",
                    starting_price=number2.price,
                    current_price=number2.price + 1000,
                    bid_increment=500,
                    start_time=start_time,
                    end_time=end_time,
                    status='active',
                    commission_rate=5.0,
                    auto_extend=True,
                    created_by=1,
                    total_bids=2
                )
                
                db.session.add(auction2)
                db.session.flush()
                
                # إضافة مزايدات للمزاد النشط
                bid1 = Bid(
                    auction_id=auction2.id,
                    customer_id=customers[0].id,
                    bid_amount=number2.price,
                    bid_time=start_time + timedelta(minutes=1)
                )
                
                bid2 = Bid(
                    auction_id=auction2.id,
                    customer_id=customers[1].id,
                    bid_amount=number2.price + 500,
                    bid_time=start_time + timedelta(minutes=2)
                )
                
                bid3 = Bid(
                    auction_id=auction2.id,
                    customer_id=customers[0].id,
                    bid_amount=number2.price + 1000,
                    bid_time=start_time + timedelta(minutes=3)
                )
                
                db.session.add_all([bid1, bid2, bid3])
                number2.status = 'auction'
                
                db.session.commit()
                
                print(f"✅ مزاد نشط #{auction2.id} - المتصدر: {customers[0].name_ar}")
                
        except Exception as e:
            print(f"⚠️ لم يتم إنشاء المزاد النشط: {e}")
            
        print("\n" + "="*60)
        print("🎯 تم إنشاء مزادات الاختبار")
        print("="*60)
        print("📋 للاختبار:")
        print("   1. اذهب لصفحة إدارة المزادات")
        print("   2. تحقق من عرض أسماء الفائزين")
        print("   3. اذهب لتفاصيل المزاد المنتهي")
        print("   4. تحقق من عرض معلومات الفائز")
        print("   5. جرب العرض المستقل")

if __name__ == '__main__':
    print("🧪 اختبار عرض اسم الفائز في المزادات")
    print("="*50)
    
    choice = input("اختر نوع الاختبار:\n1. مزاد واحد منتهي\n2. مزادات متعددة\nالخيار: ").strip()
    
    if choice == '1':
        create_test_auction_with_winner()
    elif choice == '2':
        create_multiple_test_auctions()
    else:
        print("❌ خيار غير صحيح")
    
    input("\nاضغط Enter للخروج...")
