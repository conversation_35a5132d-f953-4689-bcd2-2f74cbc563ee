#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_app import app, db, Auction, PremiumNumber
from datetime import datetime, timed<PERSON><PERSON>

def create_test_auction():
    """Create a test auction for testing the standalone page"""
    with app.app_context():
        # Find an available premium number
        available_number = PremiumNumber.query.filter_by(status='available').first()
        
        if not available_number:
            print("❌ لا توجد أرقام متاحة")
            return
        
        # Create a new auction
        auction = Auction(
            title=f"مزاد الرقم المميز {available_number.number}",
            premium_number_id=available_number.id,
            starting_price=available_number.price,
            current_price=available_number.price,
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(minutes=5),  # 5 minutes auction
            status='active',
            commission_rate=7.5,  # 7.5% commission
            total_bids=0
        )
        
        # Update number status
        available_number.status = 'auction'
        
        # Save to database
        db.session.add(auction)
        db.session.commit()
        
        print(f"✅ تم إنشاء مزاد تجريبي للرقم {available_number.number}")
        print(f"🔗 رابط الصفحة المستقلة: http://127.0.0.1:9898/auction/{auction.id}/standalone")
        print(f"⏰ مدة المزاد: 5 دقائق")
        
        return auction.id

if __name__ == '__main__':
    auction_id = create_test_auction()
    if auction_id:
        print(f"\n🎯 يمكنك الآن اختبار الصفحة المستقلة:")
        print(f"http://127.0.0.1:9898/auction/{auction_id}/standalone")
